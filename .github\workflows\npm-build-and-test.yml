name: Run Tests and Build (npm)

on:
  workflow_call:

jobs:
  test-and-build:
    runs-on: [self-hosted, Linux]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
      - name: Install dependencies
        run: npm install
      - name: Run Tests (with coverage)
        run: npm run test:coverage
      - name: Build 
        run: npm run build
      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: build
          path: dist/