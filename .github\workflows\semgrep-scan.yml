name: Sem<PERSON><PERSON>p Static Analysis

on:
  workflow_call:
    secrets:
      DIGITALOCEAN_ACCESS_TOKEN:
        required: true
      KUBERNETES_CLUSTER_ID:
        required: true
      KUBERNETES_CONTEXT:
        required: true
      SECURE_GITHUB_TOKEN:
        required: true

jobs:
  semgrep:
    name: Run Semgrep Job in DOKS
    runs-on: [self-hosted, Linux]
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Ignore Docker files for Semgrep
        run: |
          echo "Dockerfile" >> .semgrepignore
          echo "docker-compose.yml" >> .semgrepignore

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Install kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'

      - name: Fetch Kubeconfig from DOKS
        run: doctl kubernetes cluster kubeconfig save ${{ secrets.KUBERNETES_CLUSTER_ID }}

      - name: Set Kubernetes Context
        run: kubectl config use-context ${{ secrets.KUBERNETES_CONTEXT }}

      - name: Clean workspace before scan
        run: git clean -fdx

      - name: Remove Semgrep cache (if present)
        run: rm -rf .semgrep_cache || true

      - name: Remove build artifacts (optional)
        run: rm -rf node_modules dist coverage .scannerwork || true

      - name: Ensure Namespace Exists
        run: kubectl create namespace semgrep || true

      - name: Create GitHub Token Secret
        run: |
          kubectl create secret generic secure-github-token --from-literal=token=${{ secrets.SECURE_GITHUB_TOKEN }} -n semgrep --dry-run=client -o yaml | kubectl apply -f -

      - name: Delete Previous Semgrep Job (If Exists)
        run: |
          kubectl delete job semgrep-job -n semgrep --ignore-not-found
          sleep 3

      - name: Apply Semgrep Job
        run: kubectl apply -f ./k8s/semgrep-job.yaml

      - name: Create Semgrep Results Reader Pod
        run: |
          kubectl delete pod semgrep-results-reader -n semgrep --ignore-not-found
          kubectl apply -f ./k8s/semgrep-results-reader.yaml

      - name: Wait for Reader Pod Ready
        run: |
          kubectl wait --for=condition=Ready pod/semgrep-results-reader -n semgrep --timeout=60s

      - name: List files in /results in reader pod
        run: |
          kubectl exec -n semgrep semgrep-results-reader -- ls -l /results

      - name: Copy Semgrep Results from PVC
        run: |
          kubectl cp semgrep/semgrep-results-reader:/results/results.json results.json

      - name: Upload Semgrep Results as Artifact
        uses: actions/upload-artifact@v4
        with:
          name: semgrep-results
          path: results.json
          if-no-files-found: warn
          compression-level: 6
          overwrite: false
          include-hidden-files: false

      - name: Install jq
        run: sudo apt-get update && sudo apt-get install -y jq

      - name: Fail if Semgrep found blocking issues
        run: |
          if jq '.results[] | select(.severity == "WARNING" or .severity == "ERROR")' results.json | grep -q .; then
            echo "Semgrep found blocking issues! Failing the workflow."
            echo "--- Semgrep Findings (first 100 lines) ---"
            jq '.results[] | select(.severity == "WARNING" or .severity == "ERROR")' results.json | head -100
            echo "------------------------"
            exit 1
          else
            echo "No blocking Semgrep issues found."
          fi 