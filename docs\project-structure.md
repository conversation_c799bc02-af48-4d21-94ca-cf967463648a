# Project Structure Documentation

This document provides a comprehensive overview of the ATS Frontend Application project structure, explaining the purpose and organization of each directory and key file.

## 📁 Root Directory Structure

```
ATS-Frontend-Application/
├── docs/                          # Documentation files
├── public/                        # Static assets
├── src/                           # Source code
├── test/                          # Test files
├── scripts/                       # Utility scripts
├── coverage/                      # Test coverage reports
├── .gitignore                     # Git ignore rules
├── docker-compose.sonarqube.yml   # SonarQube Docker configuration
├── jest.config.js                 # Jest testing configuration
├── package.json                   # Project dependencies and scripts
├── sonar-project.properties       # SonarQube configuration
└── README.md                      # Project overview
```

## 📂 Detailed Directory Breakdown

### `/docs` - Documentation
Contains all project documentation in Markdown format:
- `README.md` - Main documentation index
- `sonarqube-setup.md` - SonarQube configuration guide
- `project-structure.md` - This file
- `development-guidelines.md` - Coding standards
- `testing-guide.md` - Testing procedures
- `deployment-guide.md` - Deployment instructions

### `/public` - Static Assets
Publicly accessible files served directly by the web server:
- `favicon.ico` - Browser tab icon
- `careerbuilder.webp` - CareerBuilder logo
- `monster.webp` - Monster logo
- Other static images and assets

### `/src` - Source Code
Main application source code organized by feature and functionality:

#### `/src/api` - API Integration
- `menu.js` - Menu API endpoints
- `snackbar.js` - Snackbar notification API

#### `/src/assets` - Static Resources
- `/fonts` - Custom font files (Inter, Poppins, etc.)
- `/images` - Image assets organized by feature:
  - `/auth` - Authentication-related images
  - `/contact` - Contact page images
  - `/maintenance` - Maintenance page images
  - `/upload` - File upload images
  - `/users` - User-related images
  - `/widget` - Widget components images

#### `/src/components` - Reusable Components
- `/@extended` - Extended Material-UI components
- `/candidates` - Candidate-related components
- `/cards` - Card-based components
- `/custom-components` - Custom reusable components
- `/third-party` - Third-party component integrations
- `Loadable.jsx` - Code splitting component
- `Loader.jsx` - Loading component
- `Locales.jsx` - Internationalization component

#### `/src/contexts` - React Contexts
- `ConfigContext.jsx` - Application configuration context
- `JWTContext.jsx` - JWT authentication context

#### `/src/custom-components` - Project-Specific Components
- `Nametextfield.jsx` - Custom name input field
- `SelectProject.jsx` - Project selection component

#### `/src/data` - Static Data
- `react-table.js` - Table configuration data

#### `/src/hooks` - Custom React Hooks
- `useAuth.js` - Authentication hook
- `useConfig.js` - Configuration hook
- `useLocalStorage.js` - Local storage hook

#### `/src/layout` - Layout Components
- `/Auth` - Authentication layout
- `/Dashboard` - Main dashboard layout
- `/Pages` - Page layouts
- `/Simple` - Simple layout components

#### `/src/menu-items` - Navigation Menu
Menu configuration files for different sections:
- `admin-setup-panel.jsx`
- `admin-setup.jsx`
- `applicant-new.jsx`
- And other menu configurations

#### `/src/pages` - Application Pages
Organized by feature and functionality:

##### `/admin-setup` - Administration Setup
- `/APISettings` - API configuration pages
- `/Applicants` - Applicant management
- `/Campus` - Campus management
- `/Clients` - Client management
- `/GlobalSettings` - Global application settings
- `/JobPosting` - Job posting management
- `/JobRequisition` - Job requisition management
- `/JobTemplate` - Job template management
- `/Leads` - Lead management
- `/Organization` - Organization settings
- `/Placement` - Placement management
- `/Reminders` - Reminder system
- `/Security` - Security settings
- `/TalentBench` - Talent bench management
- `/TextToHire` - Text-to-hire features
- `/Vendor` - Vendor management
- `/WhatsApp` - WhatsApp integration

##### `/admin-setup-panel` - Admin Panel
Similar structure to admin-setup but for panel interface

##### `/applicant-new` - Applicant Management
- `/add` - Add new applicant
- `/edit` - Edit existing applicant
- `/view` - View applicant details

##### `/client-page` - Client Management
- `/add` - Add new client
- `/edit` - Edit existing client
- `/view` - View client details
- `/approvals` - Client approval workflows
- `/client-group` - Client group management

##### Other Page Categories
- `/job-requisition` - Job requisition management
- `/job-template` - Job template management
- `/leads` - Lead management
- `/maintenance` - Maintenance pages
- `/placements` - Placement management
- `/profile` - User profile management
- `/talent-bench` - Talent bench management
- `/vendors` - Vendor management

#### `/src/profile-menu` - Profile Menu Components
- `index.jsx` - Profile menu component

#### `/src/routes` - Application Routing
- `index.jsx` - Main routing configuration
- `LoginRoutes.jsx` - Authentication routes
- `MainRoutes.jsx` - Main application routes

#### `/src/sections` - Page Sections
- `/auth` - Authentication sections
- `/extra-pages` - Additional page sections
- `/tables` - Table components

#### `/src/store` - State Management
- `/reducers` - Redux reducers
  - `actions.js` - Action creators
  - `auth.js` - Authentication reducer

#### `/src/themes` - UI Theming
- `/overrides` - Material-UI component overrides
- `/theme` - Theme configurations
- `index.jsx` - Theme provider
- `palette.js` - Color palette
- `shadows.jsx` - Shadow configurations

#### `/src/utils` - Utility Functions
- `axios.js` - HTTP client configuration
- `getColors.js` - Color utility functions
- `getDropzoneData.js` - File upload utilities
- `/locales` - Internationalization
  - `en.json` - English translations
- `/route-guard` - Route protection
  - `AuthGuard.jsx` - Authentication guard
  - `GuestGuard.jsx` - Guest route guard

### `/test` - Test Files
- `/unit` - Unit tests organized by feature
- `setup.js` - Test setup configuration
- `simple.test.js` - Basic test examples

### `/scripts` - Utility Scripts
- `generate-mock-coverage.js` - Mock coverage report generator

## 🔧 Configuration Files

### Root Level Configuration

#### `package.json`
- Project metadata and dependencies
- NPM scripts for development, testing, and deployment
- Development and production dependencies

#### `jest.config.js`
- Jest testing framework configuration
- Test environment setup (jsdom)
- Coverage reporting configuration
- Module path mapping

#### `sonar-project.properties`
- SonarQube analysis configuration
- Code coverage settings
- Quality gate thresholds
- Project metadata

#### `docker-compose.sonarqube.yml`
- SonarQube Docker container configuration
- Port mapping and environment variables
- Security and performance settings

#### `.gitignore`
- Version control exclusions
- Build artifacts
- Dependencies
- Environment files
- IDE files

## 📊 Key Architectural Patterns

### 1. Feature-Based Organization
Each major feature has its own directory under `/src/pages` with consistent subdirectories:
- `/add` - Creation forms
- `/edit` - Edit forms
- `/view` - Detail views

### 2. Component Hierarchy
- **Layout Components** (`/src/layout`) - Page structure
- **Page Components** (`/src/pages`) - Feature-specific pages
- **Reusable Components** (`/src/components`) - Shared components
- **Custom Components** (`/src/custom-components`) - Project-specific components

### 3. State Management
- **Context API** (`/src/contexts`) - Global state
- **Redux** (`/src/store`) - Complex state management
- **Custom Hooks** (`/src/hooks`) - Reusable state logic

### 4. Routing Structure
- **Main Routes** (`/src/routes/MainRoutes.jsx`) - Protected routes
- **Auth Routes** (`/src/routes/LoginRoutes.jsx`) - Public routes
- **Route Guards** (`/src/utils/route-guard`) - Route protection

## 🎯 Best Practices Implemented

### 1. Separation of Concerns
- Clear separation between UI components, business logic, and data access
- Feature-based organization for easy maintenance

### 2. Reusability
- Shared components in `/src/components`
- Custom hooks for common functionality
- Utility functions for repeated operations

### 3. Scalability
- Modular architecture allows easy feature addition
- Consistent patterns across features
- Clear naming conventions

### 4. Maintainability
- Well-organized file structure
- Consistent coding patterns
- Comprehensive documentation

## 🔍 File Naming Conventions

### Components
- **PascalCase** for component files: `ComponentName.jsx`
- **camelCase** for utility files: `utilityFunction.js`
- **kebab-case** for directories: `feature-name`

### Pages
- **PascalCase** for page components: `PageName.jsx`
- **camelCase** for page utilities: `pageUtility.js`

### Configuration
- **kebab-case** for config files: `config-name.js`
- **dot notation** for special files: `.env`, `.gitignore`

## 📈 Performance Considerations

### 1. Code Splitting
- `Loadable.jsx` component for lazy loading
- Route-based code splitting
- Component-level lazy loading

### 2. Asset Optimization
- Images organized by feature
- Font optimization with fontsource
- Static asset management

### 3. Bundle Optimization
- Tree shaking with ES modules
- Dynamic imports for large components
- Optimized dependency management

## 🛠️ Development Workflow

### 1. Feature Development
1. Create feature directory under `/src/pages`
2. Add components to `/src/components` if reusable
3. Update routing in `/src/routes`
4. Add menu items in `/src/menu-items`
5. Write tests in `/test/unit`

### 2. Component Development
1. Create component in appropriate directory
2. Add PropTypes for type checking
3. Write unit tests
4. Update documentation if needed

### 3. Testing Strategy
1. Unit tests for components
2. Integration tests for features
3. E2E tests for critical paths
4. Coverage reporting with Jest

This structure provides a solid foundation for a scalable, maintainable React application with clear separation of concerns and consistent patterns throughout the codebase. 