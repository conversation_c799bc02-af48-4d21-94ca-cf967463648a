# Semgrep Security Scanning Guide

This guide provides comprehensive instructions for setting up and using Semgrep for security scanning in the ATS Frontend Application.

## 📋 Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Installation](#installation)
4. [Configuration](#configuration)
5. [Running Scans](#running-scans)
6. [Custom Rules](#custom-rules)
7. [Integration with CI/CD](#integration-with-cicd)
8. [Best Practices](#best-practices)
9. [Troubleshooting](#troubleshooting)
10. [Advanced Configuration](#advanced-configuration)

## 🔍 Overview

Semgrep is a fast, open-source static analysis tool for finding bugs and enforcing code standards. It's particularly effective for:

- **Security vulnerabilities** detection
- **Code quality** improvements
- **Best practices** enforcement
- **Custom rule** creation
- **CI/CD integration**

### Why Semgrep for ATS Frontend Application?

- **JavaScript/React Support**: Excellent support for React and JavaScript code
- **Fast Scanning**: Quick analysis of large codebases
- **Custom Rules**: Ability to create project-specific security rules
- **Integration**: Easy integration with existing workflows
- **Free and Open Source**: No licensing costs

## 🔧 Prerequisites

Before setting up Semgrep, ensure you have the following:

- **Node.js** (v16 or higher)
- **npm** or **yarn** package manager
- **Python 3.7+** (for Semgrep installation)
- **Git** for version control
- **Docker** (optional, for containerized scanning)

### Verify Installation

```bash
# Check Node.js version
node --version

# Check npm version
npm --version

# Check Python version
python --version

# Check Git version
git --version
```

## 🚀 Installation

### Method 1: Using pip (Recommended)

```bash
# Install Semgrep using pip
pip install semgrep

# Verify installation
semgrep --version
```

### Method 2: Using Homebrew (macOS)

```bash
# Install Semgrep using Homebrew
brew install semgrep

# Verify installation
semgrep --version
```

### Method 3: Using Docker

```bash
# Pull Semgrep Docker image
docker pull returntocorp/semgrep

# Verify installation
docker run --rm returntocorp/semgrep semgrep --version
```

### Method 4: Using npm (Alternative)

```bash
# Install Semgrep globally using npm
npm install -g @semgrep/semgrep

# Verify installation
semgrep --version
```

## ⚙️ Configuration

### Project Configuration

Create a Semgrep configuration file in your project root:

```yaml
# .semgrep.yml
rules:
  # Include default security rules
  - id: security
    patterns:
      - pattern: $X
    message: "Security issue detected"
    severity: ERROR
    languages: [javascript, jsx]

  # Include React-specific rules
  - id: react-security
    patterns:
      - pattern: dangerouslySetInnerHTML
    message: "Avoid using dangerouslySetInnerHTML"
    severity: WARNING
    languages: [jsx]

  # Include custom rules
  - id: custom-rule
    patterns:
      - pattern: console.log($X)
    message: "Remove console.log statements in production"
    severity: INFO
    languages: [javascript]
```

### Advanced Configuration

```yaml
# .semgrep.yml - Advanced Configuration
rules:
  # Security Rules
  - id: xss-prevention
    patterns:
      - pattern: dangerouslySetInnerHTML={{ __html: $X }}
    message: "Potential XSS vulnerability - sanitize HTML content"
    severity: ERROR
    languages: [jsx]

  - id: sql-injection
    patterns:
      - pattern: $QUERY = "SELECT * FROM " + $USER_INPUT
    message: "Potential SQL injection - use parameterized queries"
    severity: ERROR
    languages: [javascript]

  # Code Quality Rules
  - id: no-console-log
    patterns:
      - pattern: console.log($X)
    message: "Remove console.log statements in production code"
    severity: WARNING
    languages: [javascript, jsx]

  - id: no-debugger
    patterns:
      - pattern: debugger
    message: "Remove debugger statements"
    severity: WARNING
    languages: [javascript, jsx]

  # React Best Practices
  - id: react-hooks-deps
    patterns:
      - pattern: useEffect(() => { $BODY }, [])
    message: "Consider adding dependencies to useEffect"
    severity: INFO
    languages: [jsx]

  - id: react-key-prop
    patterns:
      - pattern: { $ITEMS.map($ITEM => <$COMPONENT $PROPS />) }
    message: "Add key prop to list items"
    severity: WARNING
    languages: [jsx]

# Exclude patterns
exclude:
  - "node_modules/**"
  - "coverage/**"
  - "dist/**"
  - "build/**"
  - "*.test.js"
  - "*.spec.js"
  - "test/**"

# Include patterns
include:
  - "src/**/*.js"
  - "src/**/*.jsx"
  - "src/**/*.ts"
  - "src/**/*.tsx"
```

## 🔄 Running Scans

### Primary Scan Command

The main command for running Semgrep security scans:

```bash
# Run Semgrep security scan
semgrep --config=auto
```

This single command will:
- Automatically detect and scan all supported files in your project
- Use Semgrep's built-in security rules
- Provide detailed output with security findings
- Work across JavaScript, JSX, TypeScript, and other supported languages

### Alternative Output Formats

```bash
# JSON output for CI/CD integration
semgrep --config=auto --json > semgrep-results.json

# SARIF output for GitHub/GitLab integration
semgrep --config=auto --sarif > semgrep-results.sarif

# Verbose output for debugging
semgrep --config=auto --verbose
```

### Integration with npm Scripts

Add this simple script to your `package.json`:

```json
{
  "scripts": {
    "security:scan": "semgrep --config=auto"
  }
}
```

Then run with:
```bash
npm run security:scan
```

### Docker-based Scanning

```bash
# Run Semgrep using Docker
docker run --rm -v "${PWD}:/src" returntocorp/semgrep semgrep --config=auto
```

## 🛠️ Custom Rules

### Creating Custom Rules

Create a custom rules file:

```yaml
# custom-rules.yml
rules:
  # Custom security rule
  - id: custom-auth-check
    patterns:
      - pattern: |
          if (!$AUTH_CHECK) {
            $ACTION
          }
    message: "Add authentication check before sensitive operations"
    severity: ERROR
    languages: [javascript, jsx]

  # Custom React rule
  - id: custom-react-import
    patterns:
      - pattern: import React from 'react'
    message: "Use named imports instead of default import"
    severity: WARNING
    languages: [jsx]

  # Custom API security rule
  - id: api-key-exposure
    patterns:
      - pattern: API_KEY = "$X"
    message: "Do not hardcode API keys in source code"
    severity: ERROR
    languages: [javascript]
```

### Testing Custom Rules

```bash
# Test custom rules
semgrep --config=custom-rules.yml src/

# Test with specific rule
semgrep --config=custom-rules.yml --include="custom-auth-check" src/
```

## 🔄 Integration with CI/CD

### GitHub Actions

```yaml
# .github/workflows/semgrep.yml
name: Semgrep Security Scan

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  semgrep:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Python
      uses: actions/setup-python@v3
      with:
        python-version: '3.9'
    
    - name: Install Semgrep
      run: |
        pip install semgrep
    
    - name: Run Semgrep Scan
      run: |
        semgrep --config=auto --sarif > semgrep-results.sarif
    
    - name: Upload Semgrep Results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: semgrep-results.sarif
```

### GitLab CI

```yaml
# .gitlab-ci.yml
semgrep-scan:
  stage: test
  image: returntocorp/semgrep
  script:
    - semgrep --config=auto --sarif > semgrep-results.sarif
  artifacts:
    reports:
      sarif: semgrep-results.sarif
    paths:
      - semgrep-results.sarif
    expire_in: 1 week
```

### Jenkins Pipeline

```groovy
// Jenkinsfile
pipeline {
    agent any
    
    stages {
        stage('Semgrep Scan') {
            steps {
                sh 'pip install semgrep'
                sh 'semgrep --config=auto --json > semgrep-results.json'
                archiveArtifacts artifacts: 'semgrep-results.json'
            }
        }
    }
}
```

## ✅ Best Practices

### 1. Regular Scanning

```bash
# Simple security scan
semgrep --config=auto

# Or using npm script
npm run security:scan

# Run before commits
git commit -m "feat: add new feature" && semgrep --config=auto
```

### 2. Rule Management

```yaml
# Organize rules by category
rules:
  # Security Rules
  - id: security-xss
    patterns:
      - pattern: dangerouslySetInnerHTML
    message: "XSS vulnerability detected"
    severity: ERROR

  # Code Quality Rules
  - id: quality-console
    patterns:
      - pattern: console.log
    message: "Remove console statements"
    severity: WARNING

  # Performance Rules
  - id: performance-unused
    patterns:
      - pattern: import $X from '$Y'
    message: "Unused import detected"
    severity: INFO
```

### 3. Exclude Patterns

```yaml
# .semgrep.yml
exclude:
  - "node_modules/**"
  - "coverage/**"
  - "dist/**"
  - "build/**"
  - "*.test.js"
  - "*.spec.js"
  - "test/**"
  - "docs/**"
```

### 4. Severity Levels

- **ERROR**: Critical security issues
- **WARNING**: Important issues that should be fixed
- **INFO**: Suggestions and best practices

## 🛠️ Troubleshooting

### Common Issues

#### 1. Installation Issues

```bash
# Clear pip cache
pip cache purge

# Reinstall Semgrep
pip uninstall semgrep
pip install semgrep

# Check Python version
python --version
```

#### 2. Configuration Issues

```bash
# Validate configuration
semgrep --validate --config=.semgrep.yml

# Test configuration
semgrep --config=.semgrep.yml --test
```

#### 3. Performance Issues

```bash
# Run with optimized settings
semgrep --config=auto --max-target-bytes=1000000

# Run with specific directories (if needed)
semgrep --config=auto src/components/
```

#### 4. False Positives

```yaml
# Add exclusions for false positives
rules:
  - id: custom-rule
    patterns:
      - pattern: $X
    message: "Custom rule"
    exclude:
      - "src/utils/legacy.js"
```

### Debug Mode

```bash
# Run with debug information
semgrep --config=auto --verbose --debug

# Run with specific rule debugging
semgrep --config=auto --verbose --debug --include="rule-id"
```

## 🔧 Advanced Configuration

### Custom Rule Development

```yaml
# advanced-rules.yml
rules:
  # Complex pattern matching
  - id: complex-security
    patterns:
      - pattern: |
          $VAR = $USER_INPUT
          $QUERY = "SELECT * FROM users WHERE id = " + $VAR
    message: "SQL injection vulnerability"
    severity: ERROR
    languages: [javascript]

  # Multiple pattern matching
  - id: multiple-patterns
    patterns:
      - pattern: console.log($X)
      - pattern: console.error($X)
      - pattern: console.warn($X)
    message: "Remove console statements"
    severity: WARNING
    languages: [javascript, jsx]
```

### Performance Optimization

```bash
# Run with memory limits
semgrep --config=auto --max-memory=4096

# Run with timeout
semgrep --config=auto --timeout=300

# Run with parallel processing
semgrep --config=auto --jobs=4
```

### Output Formats

```bash
# JSON output
semgrep --config=auto --json > results.json

# SARIF output
semgrep --config=auto --sarif > results.sarif

# Text output
semgrep --config=auto --text > results.txt

# GitLab output
semgrep --config=auto --gitlab > results.gitlab
```

## 📊 Monitoring and Reporting

### Generate Reports

```bash
# Generate HTML report
semgrep --config=auto --html > report.html

# Generate JSON report with metadata
semgrep --config=auto --json --include-metadata > report.json

# Generate summary report
semgrep --config=auto --quiet | grep -E "(ERROR|WARNING|INFO)"
```

### Continuous Monitoring

```bash
# Set up automated scanning
crontab -e

# Add this line for daily scanning
0 2 * * * cd /path/to/project && semgrep --config=auto --json > daily-scan.json
```

## 📚 Additional Resources

- [Semgrep Documentation](https://semgrep.dev/docs/)
- [Semgrep Rules Registry](https://semgrep.dev/r)
- [Semgrep Community](https://semgrep.dev/community)
- [Security Best Practices](https://semgrep.dev/docs/writing-rules/security/)

## 🤝 Support

For additional support or questions:

1. Check the [Troubleshooting](#troubleshooting) section
2. Review Semgrep logs and output
3. Consult the [Semgrep Community](https://semgrep.dev/community)
4. Contact the development team for project-specific issues

This comprehensive Semgrep guide ensures secure, high-quality code through automated static analysis and custom rule enforcement. 