body {
    font-family: sans-serif;
    margin: 0 auto;
    padding: 25px;
    max-width: 400px;
    min-width: 400px;
    background-color: #dfe6e9;
    text-align: center;
  }
  
  .heading {
    text-align: center;
    font-size: 2em;
    color: #2d3436;
    margin: 20px 0 30px;
  }
  
  .card {
    border-radius: 25px;
    position: relative;
    padding: 25px 15px;
    background-color: #81ecec;
    margin: 50px 0;
    height: 200px;
    box-shadow: 0 2px 5px #ccc;
    text-align: left;
  }
  
  .top {
    border-radius: 25px 25px 0 0;
    height: 100px;
    width: 100%;
    background-color: #00cec9;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
  }
  
  .name {
    font-size: 2em;
    color: #2d3436;
    display: flex;
    flex: 1;
    margin: 10% 20px 0;
  }
  
  .card img {
    margin: 30px 20px 0 0;
  }
  
  .circle-img {
    border-radius: 50%;
    border: 7px solid #fff;
    width: 120px;
    height: 120px;
  }
  
  .bottom {
    margin-top: 120px;
  }
  
  .info {
    margin: 20px 0;
    color: #1a7e76;
  }
  


  .card {
    width: 250px;
    margin: 20px auto;
    padding: 20px;
    border-radius: 8px;
    background-color: #ffffff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    font-family: Arial, sans-serif;
  }
  
  .quick-links-container {
    font-family: Arial, sans-serif;
  }
  
  .quick-links-container h2 {
    font-size: 18px;
    margin-bottom: 10px;
  }
  
  .quick-links-container ul {
    list-style-type: none;
    padding: 0;
  }
  
  .quick-links-container ul li {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }
  
  .quick-links-container ul li span {
    font-size: 14px;
    color: #333;
  }
  
  .quick-links-container ul li a {
    font-size: 14px;
    color: #1a73e8;
    text-decoration: none;
  }
  
  .quick-links-container ul li a:hover {
    text-decoration: underline;
  }
  