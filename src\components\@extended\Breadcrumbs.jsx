import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import { useLocation, Link } from 'react-router-dom';

// material-ui
import { useTheme } from '@mui/material/styles';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import MuiBreadcrumbs from '@mui/material/Breadcrumbs';

// project-imports
import MainCard from 'components/MainCard';
import navigation from 'menu-items';
import { ThemeDirection } from 'config';

// assets
import { ArrowRight2, Buildings2, Home3 } from 'iconsax-react';
import AdminPanelSettingsOutlinedIcon from '@mui/icons-material/AdminPanelSettingsOutlined';

// ==============================|| BREADCRUMBS ||============================== //

export default function Breadcrumbs({
  card = false,
  custom = false,
  divider = false,
  heading,
  icon,
  icons,
  links,
  maxItems,
  rightAlign,
  separator,
  title = true,
  titleBottom = true,
  sx,
  ...others
}) {
  const theme = useTheme();
  const location = useLocation();

  const iconSX = {
    marginRight: theme.direction === ThemeDirection.RTL ? 0 : theme.spacing(0.75),
    marginLeft: theme.direction === ThemeDirection.RTL ? theme.spacing(0.75) : 0,
    width: '1rem',
    height: '1rem',
    color: theme.palette.secondary.main
  };

  let customLocation = location.pathname;

  // only used for component demo breadcrumbs
  if (customLocation.includes('/components-overview/breadcrumbs')) {
    customLocation = '/apps/customer/customer-card';
  }

  // item separator
  const SeparatorIcon = separator;
  const separatorIcon = separator ? <SeparatorIcon size={12} /> : <ArrowRight2 size={12} />;

  let breadcrumbContent = <Typography />;

  // Build breadcrumb items
  const breadcrumbItems = [];

  // Home breadcrumb (Keeping it here, but it's not pushed by default for your current request)
  // breadcrumbItems.push(
  //   <Typography
  //     key="home"
  //     component={Link}
  //     to="/"
  //     color="text.secondary"
  //     variant="h6"
  //     sx={{ textDecoration: 'none', display: 'flex', alignItems: 'center' }}
  //   >
  //     {icons && <Home3 style={iconSX} />}
  //     {icon && !icons && <Home3 variant="Bold" style={{ ...iconSX, marginRight: 0 }} />}
  //     {(!icon || icons) && 'Home'}
  //   </Typography>
  // );

  // Dynamically add parent breadcrumbs
  const buildPath = (menuItems, currentPath) => {
    for (const menu of menuItems) {
      const newPath = [...currentPath, menu];
      if (menu.url === customLocation) {
        return newPath;
      }
      if (menu.children) {
        const result = buildPath(menu.children, newPath);
        if (result) return result;
      }
    }
    return null;
  };

  const path = buildPath(navigation?.items || [], []);

  if (path) {
    path.forEach((menuItem) => {
      if (menuItem.breadcrumbs !== false) {
        const IconComponent = menuItem.icon ? menuItem.icon : Buildings2;
        breadcrumbItems.push(
      <Typography
            key={menuItem.id}
        component={Link}
            to={menuItem.url || '#'}
            variant="h5"
            sx={{
              textDecoration: 'none !important', // Force no underline by default
              display: 'flex',
              alignItems: 'center',
              whiteSpace: 'nowrap',
              '&:hover': {
                // Apply underline only for non-active links, AND NOT for 'clients' when it's a parent
                textDecoration: (window.location.pathname === menuItem.url || menuItem.id === 'clients') ? 'none !important' : 'underline !important'
              }
            }}
            color={window.location.pathname === menuItem.url
              ? 'text.primary' // Current page: dark
              : customLocation.startsWith(menuItem.url) && menuItem.url !== '/'
                ? theme.palette.primary.main // Parent in path: Green (from primary.main)
                : 'text.secondary' // Other (e.g., Home if present): light
            }
      >
            {console.log("Breadcrumb Link URL:", menuItem.url)}
            {icons && <IconComponent style={iconSX} />}
            {menuItem.title}
      </Typography>
    );
      }
    });
  }

    breadcrumbContent = (
      <MainCard
        border={card}
        sx={card === false ? { mb: 3, bgcolor: 'transparent', ...sx } : { mb: 3, ...sx }}
        {...others}
        content={card}
        boxShadow={false}
      >
        <Grid
          container
          direction={rightAlign ? 'row' : 'column'}
          justifyContent={rightAlign ? 'space-between' : 'flex-start'}
          alignItems={rightAlign ? 'center' : 'flex-start'}
          spacing={0.5}
        >
        {title && !titleBottom && (
          <Grid item>
            <Typography variant="h3" sx={{ fontWeight: 700 , whiteSpace: 'nowrap'}}>
              {custom ? heading : (path && path[path.length - 1]?.title) || ''}
              </Typography>
            </Grid>
          )}
        <Grid item>
          <MuiBreadcrumbs aria-label="breadcrumb" maxItems={maxItems || 8} separator={separatorIcon} sx={{ flexWrap: 'nowrap', overflowX: 'auto' }}>
            {breadcrumbItems}
          </MuiBreadcrumbs>
        </Grid>
            {title && titleBottom && (
              <Grid item sx={{ mt: card === false ? 0 : 1 }}>
            <Typography variant="h5" sx={{ fontWeight: 700, whiteSpace: 'nowrap', display: 'none' }}>
              {custom ? heading : (path && path[path.length - 1]?.title) || ''}
                </Typography>
              </Grid>
            )}
          </Grid>
          {card === false && divider !== false && <Divider sx={{ mt: 2 }} />}
        </MainCard>
      );

  return breadcrumbContent;
}

Breadcrumbs.propTypes = {
  card: PropTypes.bool,
  custom: PropTypes.bool,
  divider: PropTypes.bool,
  heading: PropTypes.string,
  icon: PropTypes.bool,
  icons: PropTypes.bool,
  links: PropTypes.array,
  maxItems: PropTypes.number,
  rightAlign: PropTypes.bool,
  separator: PropTypes.any,
  title: PropTypes.bool,
  titleBottom: PropTypes.bool,
  sx: PropTypes.any,
  others: PropTypes.any
};
