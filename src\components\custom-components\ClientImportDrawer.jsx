import React, { useState, useMemo } from 'react';
import { Drawer, Box, Typography, IconButton, Paper, Button, Link, Alert, AlertTitle } from '@mui/material';
import DownloadIcon from '@mui/icons-material/Download';
import <PERSON> from 'papaparse';
import PropTypes from 'prop-types';
import EditableImportTable from './react-table-editable/EditableImportTable';

const ClientImportDrawer = ({
  open,
  onClose,
  onImportConfirm // Prop to handle confirmed import data
}) => {
  const [csvData, setCsvData] = useState([]); // State to hold parsed CSV data temporarily
  const [headerMismatch, setHeaderMismatch] = useState(false); // State to indicate header mismatch
  const [mismatchDetails, setMismatchDetails] = useState(null); // State to store mismatch details

  // Define expected headers based on your main client table header names
  const expectedHeaders = useMemo(() => [
    'Client Name',
    'Contact Number',
    'Email',
    'Address',
    'Country',
    'State',
    'City',
    'Postal Code',
    'Website',
    'Status', // Note: There are two 'Status' headers in your main table definition
    'Primary Owner',
    'About Company',
    'Industry',
    'Category',
    'Client Visibility',
    'Created By',
    'Status', // Note: This corresponds to the 'isactive' accessorKey in your table
  ], []);

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      Papa.parse(file, {
        header: true,
        complete: (results) => {
          console.log("Parsed CSV data:", results.data);

          if (!results.data || results.data.length === 0) {
             setCsvData([]);
             setHeaderMismatch(true);
             setMismatchDetails({ message: "Uploaded file is empty or could not be parsed." });
             return;
          }

          const uploadedHeaders = Object.keys(results.data[0]);
          console.log("Uploaded headers:", uploadedHeaders);

          // Compare uploaded headers with expected headers
          const missingHeaders = expectedHeaders.filter(header => !uploadedHeaders.includes(header));
          const extraHeaders = uploadedHeaders.filter(header => !expectedHeaders.includes(header));

          if (missingHeaders.length === 0 && extraHeaders.length === 0) {
            // Headers match, set data and clear mismatch states
            // Before setting data, map uploaded headers (CSV keys) to expected accessorKeys
            const mappedData = results.data.map(row => {
              const mappedRow = {};
              expectedHeaders.forEach((expectedHeader) => {
                // Find the corresponding uploaded header (CSV key) for the expected header
                const uploadedHeader = uploadedHeaders.find(uploaded => uploaded === expectedHeader);
                if (uploadedHeader) {
                  // Map the value from the uploaded header to the expected accessorKey
                   // This part requires knowing the mapping between header name and accessorKey.
                   // For now, I'll assume a direct mapping where header name is the same as accessorKey
                   // if the header name is unique. For duplicates like 'Status', this needs clarification.
                   // A more robust solution would involve a predefined mapping object.
                   let accessorKey;
                   if (expectedHeader === 'Client Name') accessorKey = 'client_name';
                   else if (expectedHeader === 'Contact Number') accessorKey = 'contacts_number';
                   else if (expectedHeader === 'Email') accessorKey = 'email_id';
                   else if (expectedHeader === 'Address') accessorKey = 'address';
                   else if (expectedHeader === 'Country') accessorKey = 'country';
                   else if (expectedHeader === 'State') accessorKey = 'state';
                   else if (expectedHeader === 'City') accessorKey = 'city';
                   else if (expectedHeader === 'Postal Code') accessorKey = 'postal_code';
                   else if (expectedHeader === 'Website') accessorKey = 'website';
                   // Handle the 'Status' headers - this needs clarification based on which 'Status' in CSV maps to which accessorKey
                   // For demonstration, let's assume the first 'Status' in expectedHeaders maps to 'status' and the second to 'isactive'
                   // A better approach is a configuration mapping.
                   else if (expectedHeader === 'Status' && uploadedHeader === 'Status') { // This logic is simplified and needs refinement
                      // This is ambiguous due to duplicate header names. Needs a clear mapping.
                      // For now, let's skip direct mapping based on header name only if duplicates exist.
                       // We need a predefined mapping from uploaded header to accessorKey.
                       // Skipping direct mapping for 'Status' here.
                       // A robust solution requires a header mapping step if ambiguity exists.
                       // If the user clarifies which 'Status' in the CSV maps to 'status' and 'isactive', we can implement the mapping.
                       // For now, let's only map unique headers.
                       // If you can provide a mapping object (e.g., {'Client Name': 'client_name', 'Status (Active/Inactive)': 'isactive'}), I can implement it.
                       console.warn(`Skipping direct mapping for ambiguous header: ${expectedHeader}`);
                   }
                   else if (expectedHeader === 'Primary Owner') accessorKey = 'primary_owner';
                   else if (expectedHeader === 'About Company') accessorKey = 'about_company';
                   else if (expectedHeader === 'Industry') accessorKey = 'industry';
                   else if (expectedHeader === 'Category') accessorKey = 'category';
                   else if (expectedHeader === 'Client Visibility') accessorKey = 'client_visibility';
                   else if (expectedHeader === 'Created By') accessorKey = 'created_by';
                   else if (expectedHeader === 'Is Active') accessorKey = 'isactive'; // Assuming 'Is Active' is the header for isactive
                    else { // For other headers where header name directly matches accessorKey if unique
                       // This is a fallback, but a explicit mapping is safer.
                        accessorKey = expectedHeader.toLowerCase().replace(/ /g, '_'); // Simple conversion, might not match all.
                        // If uploadedHeader exactly matches an expectedHeader and that expectedHeader is unique, map it.
                        if(expectedHeaders.filter(h => h === expectedHeader).length === 1) {
                             // Find the accessorKey for this unique expectedHeader from your main table columns definition.
                             // This requires accessing the columns definition from src/pages/client-page/index.jsx, which is not directly available here.
                             // A predefined mapping object is the best approach.
                             console.warn(`Assuming direct mapping for unique header: ${expectedHeader}. Recommend predefined mapping.`);
                             // For now, let's assume a simple conversion if no explicit mapping above.
                             // This needs a proper mapping based on your main table column definitions.
                              accessorKey = uploadedHeader.toLowerCase().replace(/ /g, '_'); // Simple conversion, may not be accurate.
                        } else {
                             console.warn(`Skipping mapping for ambiguous or unmapped header: ${expectedHeader}`);
                         }
                    }

                   if(accessorKey) {
                       mappedRow[accessorKey] = row[uploadedHeader];
                   }

                }
              });
              // Handle 'isactive' separately if its header is 'Status' in CSV and needs mapping.
              // This requires knowing which 'Status' header in CSV maps to 'isactive'.
              // If your CSV has a header like 'Active Status' or 'Is Active', map it to 'isactive'.
               // If the CSV header for 'isactive' is 'Status' and is ambiguous, user mapping is needed.
               // For now, assuming a header named 'Is Active' or similar in CSV for boolean 'isactive'.
               const isActiveHeader = uploadedHeaders.find(h => h === 'Is Active' || h === 'Active Status'); // Example potential headers for isactive
               if(isActiveHeader) {
                   mappedRow['isactive'] = row[isActiveHeader];
               }

              return mappedRow;
            });
            console.log("Mapped data:", mappedData);
            setCsvData(mappedData);

            setHeaderMismatch(false);
            setMismatchDetails(null);
          } else {
            // Headers mismatch, clear data and set mismatch states
            setCsvData([]);
            setHeaderMismatch(true);
            setMismatchDetails({
              message: "Column headers mismatch.",
              missing: missingHeaders,
              extra: extraHeaders,
            });
          }
        },
        error: (error) => {
          console.error("Error parsing CSV:", error);
          setCsvData([]);
          setHeaderMismatch(true);
          setMismatchDetails({ message: `Error parsing CSV: ${error.message}` });
        },
      });
    }
  };

  const handleConfirmImport = () => {
    console.log("Confirming import with data:", csvData);
    if (onImportConfirm) {
      // Pass the current state of csvData (which might have been edited in EditableImportTable) back
      onImportConfirm(csvData);
    }
    setCsvData([]); // Clear temporary data after confirming
    setHeaderMismatch(false); // Clear mismatch state on close
    setMismatchDetails(null); // Clear mismatch details on close
    onClose(); // Close the drawer
  };

  // Close mismatch alert
  const handleCloseMismatchAlert = () => {
    setHeaderMismatch(false);
    setMismatchDetails(null);
  };

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      sx={{
        width: 800,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: 800,
          boxSizing: 'border-box',
          p: 3
        },
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <DownloadIcon sx={{ mr: 1 }} />
          <Typography variant="h6">Import contacts</Typography>
        </Box>
        <IconButton onClick={onClose}>
          <Typography variant="h6">X</Typography>
        </IconButton>
      </Box>
      <Typography variant="body2" sx={{ mb: 2 }}>
         From here, you can import contacts into ChidhagniATS using a CSV. Take a look at <Link href="#" target="_blank" rel="noopener noreferrer"> this article </Link>
         before you prepare the CSV file for importing. Make sure the CSV is encoded in UTF-8 ar
         the header row has the contact labels listed (name, email, etc.)
      </Typography>
      <Paper
        variant="outlined"
        sx={{
          p: 3,
          textAlign: 'center',
          border: '2px dashed grey',
          bgcolor: 'grey.100',
          cursor: 'pointer'
        }}
        onClick={() => document.getElementById('upload-csv-input').click()}
      >
        <input
          type="file"
          accept=".csv"
          onChange={handleFileUpload}
          style={{ display: 'none' }}
          id="upload-csv-input"
        />
        <DownloadIcon sx={{ fontSize: 40, mb: 1 }} />
        <Typography>Upload a file</Typography>
        <Typography variant="body2" color="textSecondary">or drag and drop your CSV file here</Typography>
      </Paper>

{/* Display mismatch error or editable table */}      
      {headerMismatch && mismatchDetails && (
        <Alert severity="error" sx={{ mt: 2 }} onClose={handleCloseMismatchAlert}>
          <AlertTitle>Import Failed</AlertTitle>
          {mismatchDetails.message}
          {mismatchDetails.missing && mismatchDetails.missing.length > 0 && (
            <Typography variant="body2">Missing expected headers: {mismatchDetails.missing.join(', ')}</Typography>
          )}
          {mismatchDetails.extra && mismatchDetails.extra.length > 0 && (
            <Typography variant="body2">Extra headers found: {mismatchDetails.extra.join(', ')}</Typography>
          )}
        </Alert>
      )}

      {csvData.length > 0 && !headerMismatch && (
        <EditableImportTable data={csvData} setData={setCsvData} />
      )}

      {csvData.length > 0 && !headerMismatch && (
       <Box sx={{ mt: 2, textAlign: 'right' }}>
         <Button variant="contained" onClick={handleConfirmImport}>
           Confirm Import ({csvData.length} rows)
         </Button>
       </Box>
      )}
      <Box sx={{ mt: 3, bgcolor: 'warning.main', p: 2, borderRadius: 1 }}>
        <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', fontWeight: 'bold' }}>
          <Box component="span" sx={{ color: 'warning.dark', mr: 1 }}>!</Box>Important note
        </Typography>
        <Typography variant="body2">
           If an existing contact is found in the CSV file, their information will be\n           updated in Freshdesk
        </Typography>
      </Box>
       <Typography variant="h6" sx={{ mt: 3, mb: 2 }}>Import contacts through apps</Typography>
       <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>Connect apps to add your contacts</Typography>
       {/* Add app connection components here if needed */}

    </Drawer>
  );
};

ClientImportDrawer.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onImportConfirm: PropTypes.func,
};

export default ClientImportDrawer; 