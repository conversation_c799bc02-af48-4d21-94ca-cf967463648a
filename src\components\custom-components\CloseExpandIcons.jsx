import { Box, Tooltip } from "@mui/material";
import IconButton from "themes/overrides/IconButton";
import { Icon } from "iconsax-react";


const CloseExpandIcons = (props) => {
  const { expanded, onToggle } = props;

  const handleToggle = () => {
    onToggle(!expanded); // Toggle the expanded state by calling the onToggle function with the opposite value
  };

  return (
    <Box sx={{ display: "flex", alignItems: "center", "& svg": { mr: 1 } }}>
      <Tooltip title={expanded ? "Collapse" : "Expand"}>
        <IconButton
          sx={{ color: "text.viewData" }}
          variant="contained"
          onClick={handleToggle}
        >
          <Icon
            icon={
              expanded ? "gg:chevron-double-up-r" : "gg:chevron-double-down-r"
            }
            fontSize={28}
          />
        </IconButton>
      </Tooltip>
    </Box>
  );
};

export default CloseExpandIcons;
