import React from 'react';
import { TextField, FormControl } from '@mui/material';
import { Controller } from 'react-hook-form';

const CustomAadharCardInput = ({
  name,
  control,
  placeholder = 'Enter Aadhar Card Number',
  defaultValue = "",
  disabled = false,
  required = true,
  sx = {},
  ...props
}) => {
  const isRequired = required ? 'Aadhar card number is required' : false;

  // Function to format the Aadhar number
  const formatAadhar = (value) => {
    // Remove non-digit characters and limit to 12 digits
    const digitsOnly = value.replace(/\D/g, '').slice(0, 12); // Limit to 12 digits
    return digitsOnly.replace(/(\d{4})(?=\d)/g, '$1 '); // Format every 4 digits with a space
  };

  return (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        rules={{
          required: isRequired,
          validate: (value) => {
            const trimmed = value.trim();
            if (!trimmed) return 'This field cannot be empty or just spaces';
            if (!/^\d{12}$/.test(trimmed.replace(/\s+/g, ''))) {
              return 'Please enter a valid 12-digit Aadhar number';
            }
            return true;
          },
        }}
        render={({ field, fieldState }) => (
          <TextField
            {...field}
            placeholder={placeholder}
            size="small"
            disabled={disabled}
            sx={{
              // borderRadius: "2px",
              // "& fieldset": {
              //   borderRadius: "2px",
              // },
              '& .MuiInputBase-input::placeholder': {
                fontStyle: 'Intra van',
                color: 'rgba(0, 0, 0, 0.6)',
              },
              "& .MuiFormHelperText-root": {
                backgroundColor: "white !important",
                padding: "2px 4px",
                margin: 0,
              },
              '& .MuiOutlinedInput-root': {
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                }
              },
              ...sx,
            }}
            error={Boolean(fieldState?.error)}
            helperText={fieldState?.error?.message}
            onChange={(e) => {
              let value = e.target.value;

              // Format the Aadhar number and limit it to 12 digits
              value = formatAadhar(value);

              field.onChange(value);
            }}
            onBlur={() => {
              let cleaned = field.value.trim();
              field.onChange(cleaned);
            }}
            {...props}
          />
        )}
      />
    </FormControl>
  );
};

export default CustomAadharCardInput;
