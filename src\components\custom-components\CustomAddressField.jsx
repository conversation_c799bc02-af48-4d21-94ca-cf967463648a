import React from 'react';
import { TextField, FormControl } from '@mui/material';
import { Controller } from 'react-hook-form';

const CustomAddressField = ({ name, control, placeholder, defaultValue = '', disabled = false, required, sx = {}, rows = 3, ...props }) => {
  const sanitizedPlaceholder = placeholder?.replace(/\bEnter\b/g, '').trim();

  const isRequired = typeof required !== 'undefined' ? required : sanitizedPlaceholder ? `${sanitizedPlaceholder} is required` : false;

  return (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        rules={{
          required: isRequired,
          pattern: {
            value: /^[a-zA-Z0-9\s,./#\-()]+$/,
            message: 'Invalid address format'
          }
        }}
        render={({ field, fieldState }) => (
          <TextField
            {...field}
            placeholder={placeholder}
            // size="small"
            disabled={disabled}
            multiline
            rows={rows}
            sx={{
              // borderRadius: '2px',
              // '& fieldset': {
              //   borderRadius: '2px',
              // },
              '& .MuiInputBase-input::placeholder': {
                fontStyle: 'Inter var',
                color: 'rgba(0, 0, 0, 0.6)'
              },
              '& .MuiFormHelperText-root': {
                backgroundColor: 'white !important', // ✅ force solid background
                padding: '2px 4px',
                margin: 0
              },
              '& .MuiOutlinedInput-root': {
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                }
              },
              ...sx
            }}
            error={Boolean(fieldState?.error)}
            helperText={fieldState?.error?.message}
            onChange={(e) => {
              const value = e.target.value;
              // Optional: You can sanitize certain characters if needed
              field.onChange(value);
            }}
            {...props}
          />
        )}
      />
    </FormControl>
  );
};

export default CustomAddressField;
