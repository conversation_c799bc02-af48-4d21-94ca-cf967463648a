import React from 'react';
import { TextField, FormControl } from '@mui/material';
import { Controller } from 'react-hook-form';

const CustomCgpaInput = ({
  name,
  control,
  placeholder = 'Enter CGPA',
  defaultValue = '',
  disabled = false,
  required = true,
  maxCgpa = 10.0, // You can pass maxCgpa=4.0 if needed
  sx = {},
  ...props
}) => {
  const isRequired = required ? 'CGPA is required' : false;

  // Function to format CGPA input
  const formatCgpa = (value) => {
    // Allow only numbers and dot
    let formatted = value.replace(/[^0-9.]/g, '');

    // Prevent multiple dots
    const parts = formatted.split('.');
    if (parts.length > 2) {
      formatted = parts[0] + '.' + parts[1];
    }

    // Limit to 2 decimal places
    if (parts[1]?.length > 2) {
      formatted = parts[0] + '.' + parts[1].slice(0, 2);
    }

    // Prevent numbers larger than maxCgpa (But avoid auto-setting to maxCgpa directly)
    if (parseFloat(formatted) > maxCgpa) {
      formatted = formatted.slice(0, formatted.length - 1); // Remove the last character to prevent exceeding
    }

    return formatted;
  };

  return (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        rules={{
          required: isRequired,
          validate: (value) => {
            const trimmed = value.trim();
            if (!trimmed) return 'This field cannot be empty';
            const num = parseFloat(trimmed);
            if (isNaN(num)) return 'Please enter a valid number';
            if (num < 0) return 'CGPA cannot be negative';
            if (num > maxCgpa) return `CGPA cannot be more than ${maxCgpa}`;
            if (!/^\d+(\.\d{1,2})?$/.test(trimmed)) return 'CGPA must have at most 2 decimal places';
            return true;
          }
        }}
        render={({ field, fieldState }) => (
          <TextField
            {...field}
            placeholder={placeholder}
            size="small"
            disabled={disabled}
            sx={{
              // borderRadius: "2px",
              // "& fieldset": {
              //   borderRadius: "2px",
              // },
              '& .MuiInputBase-input::placeholder': {
                fontStyle: 'Intra van',
                color: 'rgba(0, 0, 0, 0.6)'
              },
              '& .MuiFormHelperText-root': {
                backgroundColor: 'white !important',
                padding: '2px 4px',
                margin: 0
              },
              '& .MuiOutlinedInput-root': {
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                }
              },
              ...sx
            }}
            error={Boolean(fieldState?.error)}
            helperText={fieldState?.error?.message}
            onChange={(e) => {
              let value = e.target.value;
              value = formatCgpa(value);
              field.onChange(value);
            }}
            {...props}
          />
        )}
      />
    </FormControl>
  );
};

export default CustomCgpaInput;
