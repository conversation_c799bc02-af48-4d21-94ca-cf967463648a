import React from 'react';
import { TextField } from '@mui/material';
import { Controller } from 'react-hook-form';

const CustomClientName = ({ name, control, placeholder, error, sx = {}, ...props }) => {
  return (
    <Controller
      name={name}
      control={control}
      rules={{
        required: 'Client name is required',
        pattern: {
          value: /^[A-Za-z\s,\.]+$/,
          message: 'Only alphabets, spaces, commas, and periods allowed'
        },
        maxLength: {
          value: 100,
          message: 'Client name must not exceed 100 characters'
        }
      }}
      render={({ field, fieldState }) => (
        <TextField
          {...field}
          placeholder={placeholder}
          size="small"
          sx={{
            // borderRadius: '2px',
            // "& fieldset": { borderRadius: '2px' },
            '& .MuiInputBase-input::placeholder': {
              fontStyle: 'Inter var',
              color: 'rgba(0, 0, 0, 0.6)'
            },
            '& .MuiFormHelperText-root': {
              backgroundColor: 'white !important', // ✅ force solid background
              padding: '2px 4px',
              margin: 0
            },
            '& .MuiOutlinedInput-root': {
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: 'primary.main'
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: 'primary.main'
              }
            },
            ...sx
          }}
          error={Boolean(fieldState?.error)}
          helperText={fieldState?.error?.message}
          onChange={(e) => {
            const sanitizedValue = e.target.value
              .replace(/[^A-Za-z\s,\.]/g, '') // Only valid characters
              .replace(/^\s+/, '') // Remove leading spaces
              .replace(/\s{2,}/g, ' ') // Remove multiple spaces
              .replace(/\b\w/g, (char) => char.toUpperCase()); // Capitalize

            field.onChange(sanitizedValue);
          }}
          {...props}
        />
      )}
    />
  );
};

export default CustomClientName;
