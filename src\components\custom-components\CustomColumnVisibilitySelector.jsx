import React from 'react';
import PropTypes from 'prop-types';
import { Stack } from '@mui/material';
import { SelectColumnVisibility } from 'components/third-party/react-table';

const ColumnVisibilitySelector = ({ table }) => {
  return (
    <SelectColumnVisibility
      {...{
        getVisibleLeafColumns: table.getVisibleLeafColumns,
        getIsAllColumnsVisible: table.getIsAllColumnsVisible,
        getToggleAllColumnsVisibilityHandler: table.getToggleAllColumnsVisibilityHandler,
        getAllColumns: table.getAllColumns
      }}
    />
  );
};

ColumnVisibilitySelector.propTypes = {
  table: PropTypes.object.isRequired // React Table instance
};

export default ColumnVisibilitySelector;
