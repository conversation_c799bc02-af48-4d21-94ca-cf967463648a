import React, { useState } from 'react';
import { TextField, FormControl, InputLabel, Stack, Select, MenuItem, FormHelperText } from '@mui/material';
import { Controller } from 'react-hook-form';
import { PatternFormat } from 'react-number-format';
const CustomContactNumberField = ({ name, control, placeholder = 'Phone Number', defaultValue = '', sx = {}, ...props }) => {
  // Define a list of country codes for the dropdown
  const countryCodes = [
    { code: '+91', label: '+91' },
    { code: '1-671', label: '1-671' },
    { code: '+36', label: '+36' },
    { code: '+255', label: '(255)' }, // Assuming (255) means +255
    { code: '+39', label: '+39' },
    { code: '1-876', label: '1-876' },
    { code: '+7', label: '+7' },
    { code: '+254', label: '(254)' }, // Assuming (254) means +254
    { code: '+373', label: '(373)' }, // Assuming (373) means +373
    { code: '1-664', label: '1-664' },
    { code: '+95', label: '+95' },
    { code: '+264', label: '(264)' } // Assuming (264) means +264
    // Add more country codes as needed
  ];

  // We need to manage the selected country code and the phone number part separately
  // before combining them for the form value.
  // For simplicity, let's start with a default country code.
  // You might want to initialize this based on user locale or defaultValue if possible.
  const [selectedCountryCode, setSelectedCountryCode] = useState('+91'); // State for dropdown value

  return (
    <FormControl fullWidth error={Boolean(control._formState.errors[name])} sx={sx}>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        render={({ field, fieldState }) => {
          // We need to parse the initial defaultValue to set the dropdown and input values.
          // This is a simplified approach. A more robust solution might be needed
          // depending on how defaultValue is formatted.
          // For now, let's assume defaultValue is the full string like "+918654239581"
          // and we try to match a known country code at the start.

          // A better approach might be to have two separate fields in your form state:
          // one for country code and one for the number, managed by two separate Controllers
          // or using setValue/watch from react-hook-form in the parent component.

          // For this refactoring request, let's keep it as a single field 'name'
          // but the onChange logic will combine the two parts.

          // Extract just the number part from the field.value for PatternFormat
          // This requires a way to separate the code from the number.
          // A simple approach is to remove the currently selected country code prefix.
          const phoneNumberPart = field.value ? field.value.replace(selectedCountryCode, '').trim() : '';

          return (
            <Stack sx={{ gap: 1 }}>
              <Stack direction="row" sx={{ gap: 1, justifyContent: 'space-between', alignItems: 'center' }}>
                <Select
                  value={selectedCountryCode}
                  onChange={(event) => {
                    setSelectedCountryCode(event.target.value);
                    // When the country code changes, update the combined field value
                    const newFullValue = event.target.value + phoneNumberPart; // Combine new code with current number
                    field.onChange(newFullValue); // Update react-hook-form field value
                  }}
                  sx={{
                    '& .MuiInputBase-input::placeholder': {
                      fontStyle: 'Inter var',
                      color: 'rgba(0, 0, 0, 0.6)'
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main'
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main'
                    },
                     backgroundColor: 'rgba(248, 249, 250, 1)'
                  }}
                  size="small"
                >
                  {countryCodes.map((country) => (
                    <MenuItem key={country.code} value={country.code}>
                      {country.label}
                    </MenuItem>
                  ))}
                </Select>

                <PatternFormat
                  value={phoneNumberPart}
                  format="##########"
                  mask="_"
                  customInput={TextField}
                  placeholder={placeholder}
                  fullWidth
                  onValueChange={(values) => {
                    const rawNumber = values.value;
                    const newFullValue = selectedCountryCode + rawNumber;
                    field.onChange(newFullValue);
                  }}
                  onBlur={field.onBlur}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      // borderRadius: '0 4px 4px 0',
                      // '& fieldset': { borderLeft: 'none' },
                      '&:hover fieldset': { borderColor: 'primary.main' },
                      '&.Mui-focused fieldset': { borderColor: 'primary.main', borderLeft: 'none' }
                    },
                    '& .MuiInputBase-input::placeholder': {
                      fontStyle: 'Inter var',
                      color: 'rgba(0, 0, 0, 0.6)'
                    },
                     backgroundColor: 'rgba(248, 249, 250, 1)'
                  }}
                  size="small"
                  inputProps={{
                    inputMode: 'numeric'
                  }}
                />
              </Stack>
            </Stack>
          );
        }}
      />
      {control._formState.errors[name] && <FormHelperText>{control._formState.errors[name].message}</FormHelperText>}
    </FormControl>
  );
};

export default CustomContactNumberField;
