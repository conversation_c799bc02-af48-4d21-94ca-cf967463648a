import React, { useEffect, useState } from 'react';
import { FormControl, MenuItem, Select, FormHelperText } from '@mui/material';
import { Controller } from 'react-hook-form';

const CustomDropdownField = ({
  name,
  control,
  placeholder,
  options = [],
  sx = {},
  rules = {}, // Add this prop to accept validation rules
  multiple = false,
  defaultValue,
  renderValue,
  ...props
}) => {
  const [dropdownOptions, setDropdownOptions] = useState(options);

  useEffect(() => {
    const sortedOptions = [...options].sort((a, b) => a.value.localeCompare(b.value));
    setDropdownOptions(sortedOptions);
  }, [options]);

  // Default render value function
  const defaultRenderValue = (selected) => {
    if (multiple) {
      if (Array.isArray(selected) && selected.length > 0) {
        return selected.join(', ');
      }
      return '';
    } else {
      return selected || '';
    }
  };

  return (
    <FormControl fullWidth error={Boolean(control?._formState?.errors?.[name])}>
      <Controller
        name={name}
        control={control}
        defaultValue={multiple ? (defaultValue || []) : (defaultValue || '')}
        rules={rules} // Apply validation rules here
        render={({ field, fieldState }) => (
          <>
            <Select
              {...field}
              value={field.value || (multiple ? [] : '')}
              size="small"
              fullWidth
              displayEmpty
              variant="outlined"
              multiple={multiple}
              renderValue={renderValue || ((selected) => {
                const displayValue = defaultRenderValue(selected);
                return displayValue || (
                  <em style={{ fontFamily: "'Inter var', sans-serif", fontStyle: 'normal', color: 'rgba(0, 0, 0, 0.6)' }}>{placeholder}</em>
                );
              })}
              sx={{
                backgroundColor: sx.backgroundColor || 'white',
                // borderRadius: "2px",
                // '& .MuiSelect-select': {
                //   // backgroundColor: "white",
                // },
                // "& fieldset": {
                //   borderRadius: "2px",
                // },
                '& .MuiInputBase-input::placeholder': {
                  fontStyle: 'Inter var',
                  color: 'rgba(0, 0, 0, 0.6)'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                },
                ...sx
              }}
              MenuProps={{
                PaperProps: {
                  sx: {
                    borderRadius: '0px !important',
                    padding: '0 !important',
                    margin: '0 !important'
                  }
                },
                MenuListProps: {
                  sx: {
                    paddingTop: '0 !important',
                    paddingBottom: '0 !important'
                  }
                }
              }}
              {...props}
            >
              {dropdownOptions.map((option) => (
                <MenuItem
                  key={option.value}
                  value={option.value}
                  sx={{
                    border: '1px solid transparent',
                    borderRadius: '0px',
                    padding: '4px',
                    '&:hover': {
                      borderColor: 'black',
                      backgroundColor: 'rgba(0, 0, 0, 0.1)'
                    },
                    '&.Mui-focusVisible': {
                      borderColor: 'black'
                    }
                  }}
                >
                  {option.label || option.value}
                </MenuItem>
              ))}
            </Select>
            {fieldState.error && <FormHelperText>{fieldState.error.message}</FormHelperText>}
          </>
        )}
      />
    </FormControl>
  );
};

export default CustomDropdownField;
