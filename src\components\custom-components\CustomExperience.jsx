import React from 'react';
import { TextField } from '@mui/material';
import { Controller } from 'react-hook-form';

const CustomExperience = ({ name, control, placeholder, error, sx = {}, ...props }) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => (
        <TextField
          {...field}
          placeholder={placeholder}
          type="number"
          size="small"
          fullWidth
          sx={{
            '& .MuiInputBase-input::placeholder': {
              fontStyle: 'Inter var',
              color: 'rgba(0, 0, 0, 0.6)'
            },
            '& .MuiFormHelperText-root': {
              backgroundColor: 'white !important',
              padding: '2px 4px',
              margin: 0
            },
            '& .MuiOutlinedInput-root': {
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: 'primary.main'
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: 'primary.main'
              }
            },
            ...sx
          }}
          error={Boolean(fieldState?.error)}
          helperText={fieldState?.error?.message}
          onChange={(e) => {
            const value = e.target.value;
            if (value === '' || (Number(value) >= 0 && Number(value) <= 30)) {
              field.onChange(value);
            }
          }}
          {...props}
        />
      )}
    />
  );
};

export default CustomExperience;
