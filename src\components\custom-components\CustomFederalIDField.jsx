import React, { useState } from 'react';
import { TextField, FormControl } from '@mui/material';
import { Controller } from 'react-hook-form';

const CustomFederalIDField = ({ name, control, placeholder, sx = {}, ...props }) => {
  const [customError, setCustomError] = useState(''); // State for custom error message

  return (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        rules={{
          pattern: {
            value: /^(?!0{9})\d{9}$/, // Validate exactly 9 digits and not all zeroes
            message: 'Federal ID must be exactly 9 digits and cannot be all zeros'
          }
        }}
        render={({ field, fieldState }) => (
          <TextField
            {...field} // Spread react-hook-form field props
            placeholder={placeholder} // Placeholder for the input
            size="small" // Set input size
            sx={{
              // borderRadius: "2px",
              '& .MuiSelect-select': {},
              // "& fieldset": {
              //   borderRadius: "2px",
              // },
              '& .MuiInputBase-input::placeholder': {
                // Targets placeholder text
                fontStyle: 'Inter var', // Makes it Inter var
                color: 'rgba(0, 0, 0, 0.6)' // Adjusts placeholder color for better visibility
              },
              '& .MuiFormHelperText-root': {
                backgroundColor: 'white !important', // ✅ force solid background
                padding: '2px 4px',
                margin: 0
              },
              '& .MuiOutlinedInput-root': {
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                }
              },
              ...sx
            }}
            error={Boolean(fieldState?.error) || Boolean(customError)} // Show error if validation fails or custom error is set
            helperText={
              fieldState?.error?.message || customError // Display error message
            }
            inputProps={{
              maxLength: 9, // Limit input to 9 characters
              inputMode: 'numeric' // Use numeric keyboard
            }}
            onChange={(e) => {
              let sanitizedValue = e.target.value.replace(/[^0-9]/g, '').slice(0, 9); // Allow only numbers and limit to 9 digits

              // Check for all-zero input
              if (sanitizedValue === '000000000') {
                setCustomError('Federal ID cannot be all zeros'); // Set custom error
              } else {
                setCustomError(''); // Clear custom error
              }

              field.onChange(sanitizedValue); // Update react-hook-form value
            }}
            {...props} // Spread additional props
          />
        )}
      />
    </FormControl>
  );
};

export default CustomFederalIDField;
