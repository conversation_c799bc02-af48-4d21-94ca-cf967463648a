import React from 'react';
import { FormHelperText } from '@mui/material';
import PropTypes from 'prop-types';

const CustomFormHelperText = ({ children, ...props }) => {
  return (
    <FormHelperText
      
      {...props}
    >
      {children}
    </FormHelperText>
  );
};

CustomFormHelperText.propTypes = {
  children: PropTypes.node,
  sx: PropTypes.object
};

export default CustomFormHelperText; 