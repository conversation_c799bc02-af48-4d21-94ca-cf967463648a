import React from 'react';
import {
  TextField,
  FormControl,
  Chip,
  Stack,
  FormHelperText
} from '@mui/material';
import { styled } from '@mui/system';
import { useController } from 'react-hook-form';
import Autocomplete from '@mui/material/Autocomplete';
import { CloseCircle } from 'iconsax-react';

const CustomMultiTagInput = ({
  name,
  control,
  placeholder = 'Type and press Enter',
  sx = {},
}) => {
  const { field, fieldState } = useController({
    name,
    control,
    defaultValue: [],
  });

  return (
    <FormControl fullWidth error={Boolean(fieldState?.error)} sx={sx}>
      <Stack spacing={1}>
        <Autocomplete
          multiple
          freeSolo
          size='small'
          fullWidth
          id={name}
          options={[]}
          value={field.value}
          // getOptionLabel={(label) => label}
          onChange={(event, newValue) => {
            field.onChange(newValue);
          }}
         
          renderInput={(params) => (
            <TextField
              {...params}
              name={name}
              placeholder={placeholder}
              // label={label}
              sx={{
                backgroundColor : "transparent",
                '& .MuiFormHelperText-root': {
                  // backgroundColor: 'white !important',
                  padding: '2px 4px',
                  margin: 0
                },
                '& .MuiInputBase-input::placeholder': {
                fontStyle: 'Inter var',
                color: 'rgba(0, 0, 0, 0.6)'
              },
                '& .MuiOutlinedInput-root': {
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'primary.main'
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'primary.main'
                  }
                }
              }}
            />
          )}
          renderTags={(value, getTagProps) =>
            value.map((option, index) => (
              <Chip
                {...getTagProps({ index })}
                variant="combined"
                key={index}
                label={option}
                deleteIcon={<CloseCircle style={{ fontSize: '0.75rem' }} />}
                sx={{ 
                  color: 'text.primary',
                  backgroundColor: 'rgba(248, 249, 250, 1)',
                  '& .MuiChip-deleteIcon': {
                    color: 'primary.main',
                    '&:hover': {
                      color: 'black'
                    }
                  }
                }}
              />
            ))
          }
        />
      </Stack>
      {fieldState?.error && (
        <FormHelperText>{fieldState.error.message}</FormHelperText>
      )}
    </FormControl>
  );
};

export default CustomMultiTagInput;
