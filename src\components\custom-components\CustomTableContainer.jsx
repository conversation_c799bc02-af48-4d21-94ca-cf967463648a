import React, { Fragment, useState, useMemo, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  Button,
  TableHead,
  TableRow,
  Paper,
  Box,
  Stack,
  Divider,
  IconButton
} from '@mui/material';
import useMediaQuery from '@mui/material/useMediaQuery';

import { Add } from 'iconsax-react';
import MainCard from 'components/MainCard';
import {
  CSVExport,
  DebouncedInput,
  HeaderSort,
  TablePagination,
  SelectColumnVisibility,
  RowSelection
} from 'components/third-party/react-table';

import { flexRender } from '@tanstack/react-table';

export const fuzzyFilter = (row, columnId, value, addMeta) => {
  const itemRank = rankItem(row.getValue(columnId), value);
  addMeta(itemRank);
  return itemRank.passed;
};

export const fuzzySort = (rowA, rowB, columnId) => {
  let dir = 0;
  if (rowA.columnFiltersMeta[columnId]) {
    dir = compareItems(rowA.columnFiltersMeta[columnId], rowB.columnFiltersMeta[columnId]);
  }
  return dir === 0 ? sortingFns.alphanumeric(rowA, rowB, columnId) : dir;
};

const CustomTableContainer = ({
  table,
  data,
  addLabel,
  onAddClick,
  csvFilename,
  showAddButton = true,
  showImportButton = true,
  onImportClick,
  rowSelection,
  theme,
  iconButton,
  groups,
  approval
}) => {
  const backColor = theme.palette.primary.lighter;
  const matchDownSM = useMediaQuery(theme.breakpoints.down('sm'));

  const visibleColumnHeaders = useMemo(() => {
    return table
      .getVisibleLeafColumns()
      .filter((col) => !!col.columnDef.accessorKey)
      .map((col) => (typeof col.columnDef.header === 'string' ? col.columnDef.header : ''))
      .filter(Boolean);
  }, [table.getVisibleLeafColumns()]);

  const [placeholderIndex, setPlaceholderIndex] = useState(0);
  const [globalFilter, setGlobalFilter] = useState('');

  useEffect(() => {
    const interval = setInterval(() => {
      setPlaceholderIndex((prevIndex) => (prevIndex + 1) % visibleColumnHeaders.length);
    }, 2000);
    return () => clearInterval(interval);
  }, [visibleColumnHeaders]);

  const rotatingPlaceholder = visibleColumnHeaders.length > 0 ? `Search by ${visibleColumnHeaders[placeholderIndex]}` : 'Search';

  const headers = [];
  table.getVisibleLeafColumns().forEach((column) => {
    if (column.columnDef.accessorKey) {
      headers.push({
        label: typeof column.columnDef.header === 'string' ? column.columnDef.header : '#',
        key: column.columnDef.accessorKey
      });
    }
  });

  return (
    <>
      <MainCard content={false}>
        <Stack
          direction="row"
          sx={{ gap: 2, flexWrap: 'wrap', alignItems: 'center', justifyContent: 'space-between', p: 1, backgroundColor: 'rgba(248, 249, 250, 1)' }}
        >
          <DebouncedInput
            value={globalFilter ?? ''}
            onFilterChange={(value) => setGlobalFilter(String(value))}
            placeholder={rotatingPlaceholder}
          />

          <Stack direction="row" sx={{ gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <SelectColumnVisibility
              {...{
                getVisibleLeafColumns: table.getVisibleLeafColumns,
                getIsAllColumnsVisible: table.getIsAllColumnsVisible,
                getToggleAllColumnsVisibilityHandler: table.getToggleAllColumnsVisibilityHandler,
                getAllColumns: table.getAllColumns
              }}
            />

            {approval && (
              <IconButton
                color="primary"
                variant="contained"
                onClick={() => console.log('approval clicked')}
                sx={{ '&:hover': { backgroundColor: theme.palette.primary.lighter } }}
              >
                {approval}
              </IconButton>
            )}

            {iconButton && (
              <IconButton
                color="primary"
                variant="contained"
                onClick={() => console.log('IconButton clicked')}
                sx={{ '&:hover': { backgroundColor: theme.palette.primary.lighter } }}
              >
                {iconButton}
              </IconButton>
            )}

            {groups && (
              <IconButton
                color="primary"
                variant="contained"
                onClick={() => console.log('groups clicked')}
                sx={{ '&:hover': { backgroundColor: theme.palette.primary.lighter } }}
              >
                {groups}
              </IconButton>
            )}

            {showAddButton && (
              <Button variant="contained" startIcon={<Add />} size="large" onClick={onAddClick}>
                Add {addLabel}
              </Button>
            )}

            {showImportButton && (
              <Button variant="contained" size="large" onClick={onImportClick}>
                Import
              </Button>
            )}

            <CSVExport
              {...{
                data: table.getSortedRowModel().rows.map((d) => d.original),
                headers,
                filename: csvFilename
              }}
            />
          </Stack>
        </Stack>
        <Divider />
        {/* <Stack
        direction="row"
        sx={{ gap: 2, flexWrap: 'wrap', alignItems: 'center', justifyContent: 'space-between', p: 3, backgroundColor: 'rgba(248, 249, 250, 1)'}}
      >
        
        <DebouncedInput
          value={globalFilter ?? ''}
          onFilterChange={(value) => setGlobalFilter(String(value))}
          placeholder={rotatingPlaceholder}
        />
        
        <Stack direction="row" sx={{ gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
          <SelectColumnVisibility
            {...{
              getVisibleLeafColumns: table.getVisibleLeafColumns,
              getIsAllColumnsVisible: table.getIsAllColumnsVisible,
              getToggleAllColumnsVisibilityHandler: table.getToggleAllColumnsVisibilityHandler,
              getAllColumns: table.getAllColumns
            }}
          />
          {showAddButton && (
            <Button variant="contained" startIcon={<Add />} size="large" onClick={onAddClick}>
              Add {addLabel}
            </Button>
          )}
          {showImportButton && (
            <Button variant="contained"  size="large" onClick={onImportClick}>
              Import
            </Button>
          )}
          <CSVExport
            {...{
              data: table.getSortedRowModel().rows.map((d) => d.original),
              headers,
              filename: csvFilename
            }}
          />
            
        </Stack>
      </Stack> */}
      </MainCard>

      <MainCard content={false} sx={{ mt: 2, borderRadius: '0px' }}>
        <Stack>
          <RowSelection selected={Object.keys(rowSelection).length} />
          <TableContainer>
            <Table>
              <TableHead>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      if (header.column.columnDef.meta !== undefined && header.column.getCanSort()) {
                        Object.assign(header.column.columnDef.meta, {
                          className: header.column.columnDef.meta.className + ' cursor-pointer prevent-select'
                        });
                      }

                      return (
                        <TableCell
                          key={header.id}
                          sx={{ py: 2.5, px: 1 }}
                          {...header.column.columnDef.meta}
                          onClick={header.column.getToggleSortingHandler()}
                          {...(header.column.getCanSort() &&
                            header.column.columnDef.meta === undefined && {
                              className: 'cursor-pointer prevent-select'
                            })}
                        >
                          {header.isPlaceholder ? null : (
                            <Stack direction="row" sx={{ gap: 1, alignItems: 'center' }}>
                              <Box>{flexRender(header.column.columnDef.header, header.getContext())}</Box>
                              {header.column.getCanSort() && <HeaderSort column={header.column} />}
                            </Stack>
                          )}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                ))}
              </TableHead>
              <TableBody>
                {table.getRowModel().rows.map((row) => (
                  <Fragment key={row.id}>
                    <TableRow>
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id} sx={{ py: 0.2, px: 1 }} {...cell.column.columnDef.meta}>
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  </Fragment>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          <Divider />
          <Box sx={{ p: 2 }}>
            <TablePagination
              {...{
                setPageSize: table.setPageSize,
                setPageIndex: table.setPageIndex,
                getState: table.getState,
                getPageCount: table.getPageCount
              }}
            />
          </Box>
        </Stack>
      </MainCard>
    </>
  );
};

// 🧾 PropTypes
CustomTableContainer.propTypes = {
  table: PropTypes.object.isRequired,
  data: PropTypes.array.isRequired,
  rowSelection: PropTypes.object.isRequired,
  globalFilter: PropTypes.string,
  setGlobalFilter: PropTypes.func,
  theme: PropTypes.object.isRequired,
  onAddClick: PropTypes.func,
  addLabel: PropTypes.string,
  csvFilename: PropTypes.string,
  showAddButton: PropTypes.bool,
  iconButton: PropTypes.node,
  groups: PropTypes.node,
  showImportButton: PropTypes.bool,
  onImportClick: PropTypes.func,
  approval: PropTypes.node
};

export default CustomTableContainer;
