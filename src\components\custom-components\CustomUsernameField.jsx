import React from 'react';
import { TextField, FormControl } from '@mui/material';
import { Controller } from 'react-hook-form';

const CustomUsernameField = ({ name, control, placeholder, defaultValue = '', disabled = false, required, sx = {}, ...props }) => {
  const sanitizedPlaceholder = placeholder?.replace(/\bEnter\b/g, '').trim();

  const isRequired = typeof required !== 'undefined' ? required : sanitizedPlaceholder ? `${sanitizedPlaceholder} is required` : false;

  const rules = {
    required: isRequired,
    pattern: {
      value: /^[a-z]+(_[a-z]+)*$/, // Lowercase + underscores only (no double/leading/trailing)
      message: 'Only lowercase letters and underscores allowed (e.g., user_name)'
    }
  };

  return (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        rules={rules}
        render={({ field, fieldState }) => (
          <TextField
            {...field}
            placeholder={placeholder}
            size="small"
            disabled={disabled}
            sx={{
              // borderRadius: '2px',
              // '& fieldset': {
              //   borderRadius: '2px'
              // },
              '& .MuiInputBase-input::placeholder': {
                fontStyle: 'italic',
                color: 'rgba(0, 0, 0, 0.6)'
              },
              '& .MuiFormHelperText-root': {
                backgroundColor: 'white !important',
                padding: '2px 4px',
                margin: 0
              },
              '& .MuiOutlinedInput-root': {
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                }
              },
              ...sx
            }}
            error={Boolean(fieldState?.error)}
            helperText={fieldState?.error?.message}
            inputProps={{
              maxLength: 50
            }}
            onChange={(e) => {
              let value = e.target.value;
              value = value.replace(/[^a-z_]/g, '').toLowerCase();
              field.onChange(value);
            }}
            {...props}
          />
        )}
      />
    </FormControl>
  );
};

export default CustomUsernameField;
