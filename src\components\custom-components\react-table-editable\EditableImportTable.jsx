import React, { useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper } from '@mui/material';
import { useReactTable, getCoreRowModel, flexRender } from '@tanstack/react-table';

// Assuming CellEditable is available in components/third-party/react-table
// You might need to adjust the import path based on your project structure
import { CellEditable } from 'components/third-party/react-table';

const EditableImportTable = ({
  data, // This will be the parsed CSV data from the drawer
  setData // Function to update data within this component
}) => {

  // Define columns based on expected CSV structure and main table columns
  const columns = useMemo(() => [
    {
      header: 'Client Name',
      accessorKey: 'client_name',
      dataType: 'text',
      cell: CellEditable
    },
    {
      header: 'Contact Number',
      accessorKey: 'contacts_number',
      dataType: 'text',
      cell: CellEditable
    },
    {
      header: 'Email',
      accessorKey: 'email_id',
      dataType: 'text',
      cell: CellEditable
    },
    {
      header: 'Address',
      accessorKey: 'address',
      dataType: 'text',
      cell: CellEditable
    },
    {
      header: 'Country',
      accessorKey: 'country',
      dataType: 'text',
      cell: CellEditable
    },
    {
      header: 'State',
      accessorKey: 'state',
      dataType: 'text',
      cell: CellEditable
    },
    {
      header: 'City',
      accessorKey: 'city',
      dataType: 'text',
      cell: CellEditable
    },
    {
      header: 'Postal Code',
      accessorKey: 'postal_code',
      dataType: 'text',
      cell: CellEditable
    },
    {
      header: 'Website',
      accessorKey: 'website',
      dataType: 'text',
      cell: CellEditable
    },
    {
      header: 'Status',
      accessorKey: 'status',
      dataType: 'text',
      cell: CellEditable
    },
    {
      header: 'Primary Owner',
      accessorKey: 'primary_owner',
      dataType: 'text',
      cell: CellEditable
    },
    {
      header: 'About Company',
      accessorKey: 'about_company',
      dataType: 'text',
      cell: CellEditable
    },
    {
      header: 'Industry',
      accessorKey: 'industry',
      dataType: 'text',
      cell: CellEditable
    },
    {
      header: 'Category',
      accessorKey: 'category',
      dataType: 'text',
      cell: CellEditable
    },
    {
      header: 'Client Visibility',
      accessorKey: 'client_visibility',
      dataType: 'text',
      cell: CellEditable
    },
    {
      header: 'Created By',
      accessorKey: 'created_by',
      dataType: 'text',
      cell: CellEditable
    },
    // {
    //   header: 'Is Active',
    //   accessorKey: 'isactive',
    //   dataType: 'boolean', // Assuming isactive is a boolean
    //   cell: CellEditable
    // },
  ], []);

  const table = useReactTable({
    data,
    columns,
    defaultColumn: {
      cell: CellEditable
    },
    getCoreRowModel: getCoreRowModel(),
    meta: {
      updateData: (rowIndex, columnId, value) => {
        setData((old) =>
          old.map((row, index) => {
            if (index === rowIndex) {
              return {
                ...old[rowIndex],
                [columnId]: value
              };
            }
            return row;
          })
        );
      }
    },
    debugTable: true,
  });

  return (
    <TableContainer component={Paper} sx={{ mt: 2, maxHeight: 300 }}>
      <Table stickyHeader>
        <TableHead>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <TableCell key={header.id} {...header.column.columnDef.meta}>
                  {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableHead>
        <TableBody>
          {table.getRowModel().rows.map((row) => (
            <TableRow key={row.id}>
              {row.getVisibleCells().map((cell) => (
                <TableCell key={cell.id} {...cell.column.columnDef.meta}>
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

EditableImportTable.propTypes = {
  data: PropTypes.array.isRequired,
  setData: PropTypes.func.isRequired,
};

export default EditableImportTable;
