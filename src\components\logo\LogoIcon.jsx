// material-ui
import { useTheme } from '@mui/material/styles';

/**
 * if you want to use image instead of <svg> uncomment following.
 *
 * import LogoIconCarrierDark from 'assets/images/logo-icon-dark.svg';
 * import LogoIconCarrier from 'assets/images/logo-icon.svg';
 *
 */

// ==============================|| LOGO ICON SVG ||============================== //

export default function LogoIconCarrier() {
  const theme = useTheme();

  return (
    /**
     * if you want to use image instead of svg uncomment following, and comment out <svg> element.
     *
     * <img src={theme.palette.mode === ThemeMode.DARK ? LogoIconCarrierDark : LogoIconCarrier} alt="icon logo" width="100" />
     *
     */
    <svg width="50" height="45" viewBox="-3 0 66 28" fill="none" xmlns="http://www.w3.org/2000/svg" transform="scale(0.8)">
      <path
        fill={theme.palette.primary.main}
        d="M0 45.26L13.75 9.47L18.85 9.47L33.50 45.26L28.10 45.26L23.93 34.42L8.96 34.42L5.03 45.26L0 45.26M10.33 30.57L22.46 30.57L18.73 20.65Q17.02 16.14 16.19 13.23Q15.50 16.67 14.26 20.07L10.33 30.57Z"
        transform="scale(0.3), translate(55, -25)"
      />
      <path
        fill={theme.palette.primary.main}
        d="M11.79 45.26L11.79 13.70L0 13.70L0 9.47L28.37 9.47L28.37 13.70L16.53 13.70L16.53 45.26L11.79 45.26Z"
        transform="scale(0.3), translate(95, -25)"
      />
      <path
        fill={theme.palette.primary.main}
        d="M0 33.76L4.47 33.37Q4.79 36.06 5.94 37.78Q7.10 39.50 9.55 40.56Q11.99 41.63 15.04 41.63Q17.75 41.63 19.82 40.82Q21.90 40.01 22.91 38.61Q23.93 37.21 23.93 35.55Q23.93 33.86 22.95 32.60Q21.97 31.35 19.73 30.49Q18.29 29.93 13.35 28.75Q8.42 27.56 6.45 26.51Q3.88 25.17 2.62 23.18Q1.37 21.19 1.37 18.73Q1.37 16.02 2.91 13.66Q4.44 11.30 7.40 10.08Q10.35 8.86 13.96 8.86Q17.94 8.86 20.98 10.14Q24.02 11.43 25.66 13.92Q27.29 16.41 27.42 19.56L22.88 19.90Q22.51 16.50 20.40 14.77Q18.29 13.04 14.16 13.04Q9.86 13.04 7.90 14.61Q5.93 16.19 5.93 18.41Q5.93 20.34 7.32 21.58Q8.69 22.83 14.47 24.13Q20.24 25.44 22.39 26.42Q25.51 27.86 27.00 30.07Q28.49 32.28 28.49 35.16Q28.49 38.01 26.86 40.54Q25.22 43.07 22.16 44.47Q19.09 45.87 15.26 45.87Q10.40 45.87 7.12 44.46Q3.83 43.04 1.97 40.20Q0.10 37.35 0 33.76Z"
        transform="scale(0.3), translate(135, -25)"
      />
      <path
        fill={theme.palette.primary.main}
        d="M26.90 32.71L31.64 33.91Q30.15 39.75 26.28 42.81Q22.41 45.87 16.82 45.87Q11.04 45.87 7.41 43.52Q3.78 41.16 1.89 36.69Q0 32.23 0 27.10Q0 21.51 2.14 17.35Q4.27 13.18 8.22 11.02Q12.16 8.86 16.89 8.86Q22.27 8.86 25.93 11.60Q29.59 14.33 31.03 19.29L26.37 20.39Q25.12 16.48 22.75 14.70Q20.39 12.92 16.80 12.92Q12.67 12.92 9.90 14.89Q7.13 16.87 6.01 20.20Q4.88 23.54 4.88 27.08Q4.88 31.64 6.21 35.05Q7.54 38.45 10.35 40.14Q13.16 41.82 16.43 41.82Q20.41 41.82 23.17 39.53Q25.93 37.23 26.90 32.71Z"
        transform="scale(0.9), translate(0, 0)"
        stroke={theme.palette.primary.main} /* Add stroke color */
        stroke-width="2.2" /* Increase stroke width for bold effect */
      />
    </svg>
  );
}
