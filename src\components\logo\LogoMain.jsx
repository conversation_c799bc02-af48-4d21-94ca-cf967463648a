// material-ui
import { useTheme } from '@mui/material/styles';

/**
 * if you want to use image instead of <svg> uncomment following.
 *
 * import logoDark from 'assets/images/logo-dark.svg';
 * import logo from 'assets/images/logo.svg';
 *
 */

// ==============================|| LOGO SVG ||============================== //

export default function LogoMain() {
  const theme = useTheme();

  return (
    /**
     * if you want to use image instead of svg uncomment following, and comment out <svg> element.
     *
     * <img src={theme.palette.mode === ThemeMode.DARK ? logoDark : logo} alt="icon logo" width="100" />
     *
     */
    <>
      <svg width="160" height="40" viewBox="15 0 67 32.7" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          fill={theme.palette.primary.main}
          d="M0 45.26L13.75 9.47L18.85 9.47L33.50 45.26L28.10 45.26L23.93 34.42L8.96 34.42L5.03 45.26L0 45.26M10.33 30.57L22.46 30.57L18.73 20.65Q17.02 16.14 16.19 13.23Q15.50 16.67 14.26 20.07L10.33 30.57Z"
          transform="scale(0.2), translate(420, -10)"
        />
        <path
          fill={theme.palette.primary.main}
          d="M11.79 45.26L11.79 13.70L0 13.70L0 9.47L28.37 9.47L28.37 13.70L16.53 13.70L16.53 45.26L11.79 45.26Z"
          transform="scale(0.2), translate(455, -10)"
        />
        <path
          fill={theme.palette.primary.main}
          d="M0 33.76L4.47 33.37Q4.79 36.06 5.94 37.78Q7.10 39.50 9.55 40.56Q11.99 41.63 15.04 41.63Q17.75 41.63 19.82 40.82Q21.90 40.01 22.91 38.61Q23.93 37.21 23.93 35.55Q23.93 33.86 22.95 32.60Q21.97 31.35 19.73 30.49Q18.29 29.93 13.35 28.75Q8.42 27.56 6.45 26.51Q3.88 25.17 2.62 23.18Q1.37 21.19 1.37 18.73Q1.37 16.02 2.91 13.66Q4.44 11.30 7.40 10.08Q10.35 8.86 13.96 8.86Q17.94 8.86 20.98 10.14Q24.02 11.43 25.66 13.92Q27.29 16.41 27.42 19.56L22.88 19.90Q22.51 16.50 20.40 14.77Q18.29 13.04 14.16 13.04Q9.86 13.04 7.90 14.61Q5.93 16.19 5.93 18.41Q5.93 20.34 7.32 21.58Q8.69 22.83 14.47 24.13Q20.24 25.44 22.39 26.42Q25.51 27.86 27.00 30.07Q28.49 32.28 28.49 35.16Q28.49 38.01 26.86 40.54Q25.22 43.07 22.16 44.47Q19.09 45.87 15.26 45.87Q10.40 45.87 7.12 44.46Q3.83 43.04 1.97 40.20Q0.10 37.35 0 33.76Z"
          transform="scale(0.2), translate(490, -10)"
        />
        <path
          fill={theme.palette.primary.main}
          d="M26.90 32.71L31.64 33.91Q30.15 39.75 26.28 42.81Q22.41 45.87 16.82 45.87Q11.04 45.87 7.41 43.52Q3.78 41.16 1.89 36.69Q0 32.23 0 27.10Q0 21.51 2.14 17.35Q4.27 13.18 8.22 11.02Q12.16 8.86 16.89 8.86Q22.27 8.86 25.93 11.60Q29.59 14.33 31.03 19.29L26.37 20.39Q25.12 16.48 22.75 14.70Q20.39 12.92 16.80 12.92Q12.67 12.92 9.90 14.89Q7.13 16.87 6.01 20.20Q4.88 23.54 4.88 27.08Q4.88 31.64 6.21 35.05Q7.54 38.45 10.35 40.14Q13.16 41.82 16.43 41.82Q20.41 41.82 23.17 39.53Q25.93 37.23 26.90 32.71Z"
          transform="scale(0.7), translate(-18, -8)"
          stroke={theme.palette.primary.main} /* Add stroke color */
          stroke-width="2.2" /* Increase stroke width for bold effect */
        />
        <path
          fill={theme.palette.primary.main}
          d="M0 45.26L0 9.47L4.39 9.47L4.39 22.31Q7.47 18.75 12.16 18.75Q15.04 18.75 17.16 19.89Q19.29 21.02 20.20 23.02Q21.12 25.02 21.12 28.83L21.12 45.26L16.72 45.26L16.72 28.83Q16.72 25.54 15.30 24.04Q13.87 22.53 11.25 22.53Q9.30 22.53 7.58 23.55Q5.86 24.56 5.13 26.29Q4.39 28.03 4.39 31.08L4.39 45.26L0 45.26Z"
          transform="scale(0.5), translate(28, 5)"
          stroke={theme.palette.primary.main} /* Add stroke color */
          stroke-width="2.2" /* Increase stroke width for bold effect */
        />
        <path
          fill={theme.palette.primary.main}
          d="M0 14.53L0 9.47L4.39 9.47L4.39 14.53L0 14.53M0 45.26L0 19.34L4.39 19.34L4.39 45.26L0 45.26Z"
          transform="scale(0.5), translate(58, 5)"
          stroke={theme.palette.primary.main} /* Add stroke color */
          stroke-width="2.2" /* Increase stroke width for bold effect */
        />
        <path
          fill={theme.palette.primary.main}
          d="M18.41 45.26L18.41 41.99Q15.94 45.85 11.16 45.85Q8.06 45.85 5.46 44.14Q2.86 42.43 1.43 39.37Q0 36.30 0 32.32Q0 28.44 1.29 25.28Q2.59 22.12 5.18 20.43Q7.76 18.75 10.96 18.75Q13.31 18.75 15.14 19.74Q16.97 20.73 18.12 22.31L18.12 9.47L22.49 9.47L22.49 45.26L18.41 45.26M4.52 32.32Q4.52 37.30 6.62 39.77Q8.72 42.24 11.57 42.24Q14.45 42.24 16.47 39.88Q18.48 37.52 18.48 32.69Q18.48 27.37 16.43 24.88Q14.38 22.39 11.38 22.39Q8.45 22.39 6.48 24.78Q4.52 27.17 4.52 32.32Z"
          transform="scale(0.5), translate(68, 5)"
          stroke={theme.palette.primary.main} /* Add stroke color */
          stroke-width="2.2" /* Increase stroke width for bold effect */
        />
        <path
          fill={theme.palette.primary.main}
          d="M0 45.26L0 9.47L4.39 9.47L4.39 22.31Q7.47 18.75 12.16 18.75Q15.04 18.75 17.16 19.89Q19.29 21.02 20.20 23.02Q21.12 25.02 21.12 28.83L21.12 45.26L16.72 45.26L16.72 28.83Q16.72 25.54 15.30 24.04Q13.87 22.53 11.25 22.53Q9.30 22.53 7.58 23.55Q5.86 24.56 5.13 26.29Q4.39 28.03 4.39 31.08L4.39 45.26L0 45.26Z"
          transform="scale(0.5), translate(98, 5)"
          stroke={theme.palette.primary.main} /* Add stroke color */
          stroke-width="2.2" /* Increase stroke width for bold effect */
        />
        <path
          fill={theme.palette.primary.main}
          d="M18.41 42.07Q15.97 44.14 13.71 45.00Q11.45 45.85 8.86 45.85Q4.59 45.85 2.29 43.76Q0 41.67 0 38.43Q0 36.52 0.87 34.95Q1.73 33.37 3.14 32.42Q4.54 31.47 6.30 30.98Q7.59 30.64 10.21 30.32Q15.53 29.69 18.04 28.81Q18.07 27.91 18.07 27.66Q18.07 24.98 16.82 23.88Q15.14 22.39 11.82 22.39Q8.72 22.39 7.24 23.47Q5.76 24.56 5.05 27.32L0.76 26.73Q1.34 23.97 2.69 22.28Q4.03 20.58 6.57 19.67Q9.11 18.75 12.45 18.75Q15.77 18.75 17.85 19.53Q19.92 20.31 20.90 21.50Q21.88 22.68 22.27 24.49Q22.49 25.61 22.49 28.54L22.49 34.40Q22.49 40.53 22.77 42.15Q23.05 43.77 23.88 45.26L19.29 45.26Q18.60 43.90 18.41 42.07M18.04 32.25Q15.65 33.23 10.86 33.91Q8.15 34.30 7.03 34.79Q5.91 35.28 5.30 36.22Q4.69 37.16 4.69 38.31Q4.69 40.06 6.02 41.24Q7.35 42.41 9.91 42.41Q12.45 42.41 14.43 41.30Q16.41 40.19 17.33 38.26Q18.04 36.77 18.04 33.86L18.04 32.25Z"
          transform="scale(0.5), translate(128, 5)"
          stroke={theme.palette.primary.main} /* Add stroke color */
          stroke-width="2.2" /* Increase stroke width for bold effect */
        />
        <path
          fill={theme.palette.primary.main}
          d="M0.88 47.41L5.15 48.05Q5.42 50.02 6.64 50.93Q8.28 52.15 11.11 52.15Q14.16 52.15 15.82 50.93Q17.48 49.71 18.07 47.51Q18.41 46.17 18.38 41.87Q15.50 45.26 11.21 45.26Q5.86 45.26 2.93 41.41Q0 37.55 0 32.15Q0 28.44 1.34 25.31Q2.69 22.17 5.24 20.46Q7.79 18.75 11.23 18.75Q15.82 18.75 18.80 22.46L18.80 19.34L22.85 19.34L22.85 41.75Q22.85 47.80 21.62 50.33Q20.39 52.86 17.71 54.32Q15.04 55.79 11.13 55.79Q6.49 55.79 3.64 53.70Q0.78 51.61 0.88 47.41M4.52 31.84Q4.52 36.94 6.54 39.28Q8.57 41.63 11.62 41.63Q14.65 41.63 16.70 39.29Q18.75 36.96 18.75 31.98Q18.75 27.22 16.64 24.80Q14.53 22.39 11.55 22.39Q8.62 22.39 6.57 24.77Q4.52 27.15 4.52 31.84Z"
          transform="scale(0.5), translate(158, 5)"
          stroke={theme.palette.primary.main} /* Add stroke color */
          stroke-width="2.2" /* Increase stroke width for bold effect */
        />
        <path
          fill={theme.palette.primary.main}
          d="M0 45.26L0 19.34L3.96 19.34L3.96 23.02Q6.81 18.75 12.21 18.75Q14.55 18.75 16.52 19.59Q18.48 20.43 19.46 21.80Q20.43 23.17 20.83 25.05Q21.07 26.27 21.07 29.32L21.07 45.26L16.67 45.26L16.67 29.49Q16.67 26.81 16.16 25.48Q15.65 24.15 14.34 23.35Q13.04 22.56 11.28 22.56Q8.47 22.56 6.43 24.34Q4.39 26.12 4.39 31.10L4.39 45.26L0 45.26Z"
          transform="scale(0.5), translate(188, 5)"
          stroke={theme.palette.primary.main} /* Add stroke color */
          stroke-width="2.2" /* Increase stroke width for bold effect */
        />
        <path
          fill={theme.palette.primary.main}
          d="M0 14.53L0 9.47L4.39 9.47L4.39 14.53L0 14.53M0 45.26L0 19.34L4.39 19.34L4.39 45.26L0 45.26Z"
          transform="scale(0.5), translate(218, 5)"
          stroke={theme.palette.primary.main} /* Add stroke color */
          stroke-width="2.2" /* Increase stroke width for bold effect */
        />
      </svg>
    </>
  );
}
