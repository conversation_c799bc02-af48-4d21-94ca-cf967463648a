import PropTypes from 'prop-types';
// material-ui
import Checkbox from '@mui/material/Checkbox';

// ==============================|| ROW SELECTION - CHECKBOX ||============================== //

export default function IndeterminateCheckbox({ indeterminate, ...rest }) {
  return <Checkbox {...rest} indeterminate={typeof indeterminate === 'boolean' && !rest.checked && indeterminate} sx={{ transform: 'scale(0.75)', padding: '1.5px',           // Adjust the padding to reduce space
    margin: '0',        }}  />;
}

IndeterminateCheckbox.propTypes = { indeterminate: PropTypes.bool };
