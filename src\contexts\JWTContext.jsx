// import { createContext, useEffect, useReducer } from 'react';

// // third-party
// import { Chance } from 'chance';
// import { jwtDecode } from 'jwt-decode';

// // reducer - state management
// import { LOGIN, LOGOUT } from 'store/reducers/actions';
// import authReducer from 'store/reducers/auth';

// // project-imports
// import Loader from 'components/Loader';
// import axios from 'utils/axios';

// const chance = new Chance();

// // constant
// const initialState = {
//   isLoggedIn: false,
//   isInitialized: false,
//   user: null
// };

// const verifyToken = (serviceToken) => {
//   if (!serviceToken) {
//     return false;
//   }
//   const decoded = jwtDecode(serviceToken);

//   /**
//    * Property 'exp' does not exist on type '<T = unknown>(token: string, options?: JwtDecodeOptions | undefined) => T'.
//    */
//   return decoded.exp > Date.now() / 1000;
// };

// const setSession = (serviceToken) => {
//   if (serviceToken) {
//     localStorage.setItem('serviceToken', serviceToken);
//     axios.defaults.headers.common.Authorization = `Bearer ${serviceToken}`;
//   } else {
//     localStorage.removeItem('serviceToken');
//     delete axios.defaults.headers.common.Authorization;
//   }
// };

// // ==============================|| JWT CONTEXT & PROVIDER ||============================== //

// const JWTContext = createContext(null);

// export const JWTProvider = ({ children }) => {
//   const [state, dispatch] = useReducer(authReducer, initialState);

//   useEffect(() => {
//     const init = async () => {
//       try {
//         const serviceToken = localStorage.getItem('serviceToken');
//         if (serviceToken && verifyToken(serviceToken)) {
//           setSession(serviceToken);
//           const response = await axios.get('/api/account/me');
//           const { user } = response.data;

//           dispatch({
//             type: LOGIN,
//             payload: {
//               isLoggedIn: true,
//               user
//             }
//           });
//         } else {
//           dispatch({
//             type: LOGOUT
//           });
//         }
//       } catch (err) {
//         console.error(err);
//         dispatch({
//           type: LOGOUT
//         });
//       }
//     };

//     init();
//   }, []);

//   // const login = async (email, password) => {
//   //   const response = await axios.post('http://127.0.0.1:8000/auth/token', { email, password });
//   //   const { serviceToken, user } = response.data;
//   //   setSession(serviceToken);
//   //   dispatch({
//   //     type: LOGIN,
//   //     payload: {
//   //       isLoggedIn: true,
//   //       user
//   //     }
//   //   });
//   // };
//   // const login = async (username, password) => {
//   //   const response = await axios.post('http://127.0.0.1:8000/auth/token', { username, password });
//   //   const { accessToken } = response.data;
//   //   setSession(accessToken);
//   //   dispatch({
//   //     type: LOGIN,
//   //     payload: {
//   //       isLoggedIn: true,
//   //       user
//   //     }
//   //   });
//   // };
//   // const login = async (username, password) => {
//   //   const params = new URLSearchParams();
//   //   params.append('username', username);
//   //   params.append('password', password);
  
//   //   const response = await axios.post('http://127.0.0.1:8000/auth/token', params, {
//   //     headers: {
//   //       'Content-Type': 'application/x-www-form-urlencoded'
//   //     }
//   //   });
  
//   //   const { access_token } = response.data;
//   //   setSession(access_token);
//   //   dispatch({
//   //     type: LOGIN,
//   //     payload: {
//   //       isLoggedIn: true,
//   //       user
//   //     }
//   //   });
//   // };

//   // this below is code is working for sign up

// // const login = async (username, password) => {
// //   try {
// //     const params = new URLSearchParams();
// //     params.append('username', username);
// //     params.append('password', password);

// //     const response = await axios.post('http://127.0.0.1:8000/token', params, {
// //       headers: {
// //         'Content-Type': 'application/x-www-form-urlencoded'
// //       }
// //     });

// //     const { access_token } = response.data;
// //     setSession(access_token);

// //     // Assuming `user` is part of the response data
// //     const user = { username }; // Replace this with actual user data if available in response

// //     dispatch({
// //       type: LOGIN,
// //       payload: {
// //         isLoggedIn: true,
// //         user
// //       }
// //     });
// //   } catch (error) {
// //     console.error('Login error:', error);
// //     // Handle error accordingly
// //   }
// // };



// const login = async (username, password) => {
//   try {
//     const params = new URLSearchParams();
//     params.append('username', username);
//     params.append('password', password);

//     const response = await axios.post('http://127.0.0.1:8000/token', params, {
//       headers: {
//         'Content-Type': 'application/x-www-form-urlencoded'
//       }
//     });

//     const { access_token } = response.data;
    
//     // Log the token to the console for inspection
//     console.log('Token received:', access_token);

//     setSession(access_token);
//     const storedToken = localStorage.getItem('serviceToken');
//     console.log('Token after setting in localStorage:', storedToken);
//     // Assuming `user` is part of the response data
//     const user = { username }; // Replace this with actual user data if available in response

//     dispatch({
//       type: LOGIN,
//       payload: {
//         isLoggedIn: true,
//         user
//       }
//     });
//   } catch (error) {
//     console.error('Login error:', error);
//     // Handle error accordingly
//   }
// };


//   // const register = async (email, password, firstName, lastName) => {
//   //   // todo: this flow need to be recode as it not verified
//   //   const id = chance.bb_pin();
//   //   const response = await axios.post('http://127.0.0.1:8000/auth/', {
//   //     id,
//   //     email,
//   //     password,
//   //     firstName,
//   //     lastName
//   //   });
//   //   let users = response.data;

//   //   if (window.localStorage.getItem('users') !== undefined && window.localStorage.getItem('users') !== null) {
//   //     const localUsers = window.localStorage.getItem('users');
//   //     users = [
//   //       ...JSON.parse(localUsers),
//   //       {
//   //         id,
//   //         email,
//   //         password,
//   //         name: `${firstName} ${lastName}`
//   //       }
//   //     ];
//   //   }

//   //   window.localStorage.setItem('users', JSON.stringify(users));
//   // };


//   const register = async (username, email, password, first_name, last_name, role) => {
//     // Generate a unique ID (ensure `chance` is correctly imported and used)
//     const id = chance.bb_pin();
  
//     // Send registration request to the backend
//     const response = await axios.post('http://127.0.0.1:8000/users/', {
//       id,
//       username,   // Added field
//       email,
//       password,
//       first_name,
//       last_name,
//       role         // Added field
//     });

//     let users = response.data;
  
//     // Update local storage with the new user data
//     if (window.localStorage.getItem('users') !== undefined && window.localStorage.getItem('users') !== null) {
//       const localUsers = window.localStorage.getItem('users');
//       users = [
//         ...JSON.parse(localUsers),
//         {
//           id,
//           username,  // Added field
//           email,
//           password,
//           name: `${first_name} ${last_name}`,
//           role       // Added field
//         }
//       ];
//     }
  
//     window.localStorage.setItem('users', JSON.stringify(users));
//   };
  


//   const logout = () => {
//     setSession(null);
//     dispatch({ type: LOGOUT });
//   };

//   const resetPassword = async (email) => {
//     console.log('email - ', email);
//   };

//   const updateProfile = () => {};

//   if (state.isInitialized !== undefined && !state.isInitialized) {
//     return <Loader />;
//   }

//   return <JWTContext.Provider value={{ ...state, login, logout, register, resetPassword, updateProfile }}>{children}</JWTContext.Provider>;
// };

// import { createContext, useEffect, useReducer } from 'react';

// // third-party
// import { Chance } from 'chance';
// import { jwtDecode } from 'jwt-decode'; // Correct the import statement

// // reducer - state management
// import { LOGIN, LOGOUT } from 'store/reducers/actions';
// import authReducer from 'store/reducers/auth';

// // project-imports
// import Loader from 'components/Loader';
// import axios from 'utils/axios';

// const chance = new Chance();

// // constant
// const initialState = {
//   isLoggedIn: false,
//   isInitialized: false,
//   user: null
// };

// const verifyToken = (serviceToken) => {
//   if (!serviceToken) {
//     return false;
//   }
//   const decoded = jwtDecode(serviceToken);
//   return decoded.exp > Date.now() / 1000;
// };

// const setSession = (serviceToken) => {
//   if (serviceToken) {
//     localStorage.setItem('serviceToken', serviceToken);
//     axios.defaults.headers.common.Authorization = `Bearer ${serviceToken}`;
//   } else {
//     localStorage.removeItem('serviceToken');
//     delete axios.defaults.headers.common.Authorization;
//   }
// };

// const JWTContext = createContext(null);

// export const JWTProvider = ({ children }) => {
//   const [state, dispatch] = useReducer(authReducer, initialState);

//   useEffect(() => {
//     const init = async () => {
//       try {
//         const serviceToken = localStorage.getItem('serviceToken');
//         console.log('Service Token:', serviceToken);
  
//         // Check if the token exists and is valid
//         if (serviceToken && verifyToken(serviceToken)) {
//   console.log('Token is valid');
//   setSession(serviceToken);

//   try {
//     console.log("Attempting to fetch user data...");
//     const response = await axios.get('http://127.0.0.1:8000/users/get');
//     console.log("Service token working, response:", response);

//     const user = response.data?.user;

//     if (user) {
//       console.log("User data extracted:", user);
//       dispatch({
//         type: LOGIN,
//         payload: {
//           isLoggedIn: true,
//           user
//         }
//       });
//       console.log("Dispatched LOGIN action with user data");
//     } else {
//       console.error('User data is missing from the response');
//       dispatch({
//         type: LOGOUT
//       });
//     }
//   } catch (apiError) {
//     console.error('Error occurred while fetching user data:', apiError.response?.data || apiError.message);
//     dispatch({
//       type: LOGOUT
//     });
//   }
// } else {
//   console.log("No valid token found, logging out");
//   localStorage.removeItem('serviceToken'); // Clear any invalid or expired token
//   dispatch({
//     type: LOGOUT
//   });
// }

  

//   const login = async (username, password) => {
//     try {
//       const params = new URLSearchParams();
//       params.append('username', username);
//       params.append('password', password);
  
//       const response = await axios.post('http://127.0.0.1:8000/token', params, {
//         headers: {
//           'Content-Type': 'application/x-www-form-urlencoded'
//         }
//       });
  
//       console.log("=========================");
//       console.log(response);
  
//       if (response.data.access_token) {  // Check for access_token instead of serviceToken
//         const { access_token: serviceToken } = response.data;  // Rename access_token to serviceToken
//         setSession(serviceToken);
//         dispatch({
//           type: LOGIN,
//           payload: {
//             isLoggedIn: true,
//             user: { username },  // Assuming the username is the only user data you have
//           }
//         });
//       } else {
//         console.error('No token received from backend');
//       }
//     } catch (error) {
//       console.error('Login failed:', error);
//       // Handle the error (e.g., show an error message to the user)
//     }
//   };
  
  

//   const register = async (email, password, firstName, lastName) => {
//     const id = chance.bb_pin();
//     const response = await axios.post('http://127.0.0.1:8000/users/', { // Replace with your actual registration endpoint
//       id,
//       email,
//       password,
//       firstName,
//       lastName
//     });
//     let users = response.data;

//     if (window.localStorage.getItem('users') !== undefined && window.localStorage.getItem('users') !== null) {
//       const localUsers = window.localStorage.getItem('users');
//       users = [
//         ...JSON.parse(localUsers),
//         {
//           id,
//           email,
//           password,
//           name: `${firstName} ${lastName}`
//         }
//       ];
//     }

//     window.localStorage.setItem('users', JSON.stringify(users));
//   };

//   const logout = () => {
//     setSession(null);
//     dispatch({ type: LOGOUT });
//   };

//   const resetPassword = async (email) => {
//     const response = await axios.post('http://127.0.0.1:8000/reset-password', { email }); // Replace with your actual password reset endpoint
//     console.log('Password reset request sent for:', email);
//   };

//   const updateProfile = () => {
//     // Implement your profile update logic here
//   };

//   if (!state.isInitialized) {
//     return <Loader />;
//   }

//   return (
//     <JWTContext.Provider value={{ ...state, login, logout, register, resetPassword, updateProfile }}>
//       {children}
//     </JWTContext.Provider>
//   );
// };

// export default JWTContext;


// import { createContext, useEffect, useReducer } from 'react';

// // third-party
// import { Chance } from 'chance';
// import { jwtDecode } from 'jwt-decode';

// // reducer - state management
// import { LOGIN, LOGOUT } from 'store/reducers/actions';
// import authReducer from 'store/reducers/auth';

// // project-imports
// import Loader from 'components/Loader';
// import axios from 'utils/axios';

// const chance = new Chance();

// // constant
// const initialState = {
//   isLoggedIn: false,
//   isInitialized: false,
//   user: null
// };

// const verifyToken = (serviceToken) => {
//   if (!serviceToken) {
//     return false;
//   }
//   const decoded = jwtDecode(serviceToken);
//   return decoded.exp > Date.now() / 1000;
// };

// const setSession = (serviceToken) => {
//   if (serviceToken) {
//     localStorage.setItem('serviceToken', serviceToken);
//     axios.defaults.headers.common.Authorization = `Bearer ${serviceToken}`;
//   } else {
//     localStorage.removeItem('serviceToken');
//     delete axios.defaults.headers.common.Authorization;
//   }
// };

// const JWTContext = createContext(null);

// export const JWTProvider = ({ children }) => {
//   const [state, dispatch] = useReducer(authReducer, initialState);

//   useEffect(() => {
//     const init = async () => {
//       try {
//         const serviceToken = localStorage.getItem('serviceToken');
//         console.log('Service Token:', serviceToken);

//         // Check if the token exists and is valid
//         if (!(serviceToken && verifyToken(serviceToken))) {
//           console.log("No valid token found, logging out");
//           localStorage.removeItem('serviceToken'); // Clear any invalid or expired token
//           dispatch({
//             type: LOGOUT
//           });
//         }else {
//           console.log('Token is valid');
//           setSession(serviceToken);

//           // try {
//           //   console.log("Attempting to fetch user data...");
//           //   const response = await axios.get('http://127.0.0.1:8000/users/get');
//           //   console.log("Service token working, response:", response);

//           //   const user = response.data?.user;

//             // if (user) {
//             //   console.log("User data extracted:", user);
//             //   dispatch({
//             //     type: LOGIN,
//             //     payload: {
//             //       isLoggedIn: true,
//             //       user
//             //     }
//             //   });
//             //   console.log("Dispatched LOGIN action with user data");
//             // } else {
//             //   console.error('User data is missing from the response');
//             //   dispatch({
//             //     type: LOGOUT
//             //   });
//             // }
//           // } catch (apiError) {
//           //   console.error('Error occurred while fetching user data:', apiError.response?.data || apiError.message);
//           //   dispatch({
//           //     type: LOGOUT
//           //   });
//           // }
//         }
//       } catch (err) {
//         console.error('An error occurred during the initialization process:', err);
//         dispatch({
//           type: LOGOUT
//         });
//       }
//     };

//     init();
//   }, []);

//   const login = async (username, password) => {
//     try {
//       const params = new URLSearchParams();
//       params.append('username', username);
//       params.append('password', password);

//       const response = await axios.post('http://127.0.0.1:8000/token', params, {
//         headers: {
//           'Content-Type': 'application/x-www-form-urlencoded'
//         }
//       });

//       console.log("=========================");
//       console.log(response);

//       if (response.data.access_token) {  // Check for access_token instead of serviceToken
//         const { access_token: serviceToken } = response.data;  // Rename access_token to serviceToken
//         setSession(serviceToken);
//         dispatch({
//           type: LOGIN,
//           payload: {
//             isLoggedIn: true,
//             user: { username },  // Assuming the username is the only user data you have
//           }
//         });
//       } else {
//         console.error('No token received from backend');
//       }
//     } catch (error) {
//       console.error('Login failed:', error);
//       // Handle the error (e.g., show an error message to the user)
//     }
//   };

//   // const login = async (username, password) => {
//   //   try {
//   //     const formDetails = new URLSearchParams();
//   //     formDetails.append('username', username);
//   //     formDetails.append('password', password);
  
//   //     const response = await fetch('http://127.0.0.1:8000/token', {
//   //       method: 'POST',
//   //       headers: {
//   //         'Content-Type': 'application/x-www-form-urlencoded',
//   //       },
//   //       body: formDetails,
//   //     });
  
//   //     console.log("=========================");
//   //     console.log(response);
  
//   //     if (response.ok) {
//   //       const data = await response.json();
//   //       const { access_token: serviceToken } = data;
        
//   //       setSession(serviceToken);
//   //       dispatch({
//   //         type: LOGIN,
//   //         payload: {
//   //           isLoggedIn: true,
//   //           user: { username },  // Assuming the username is the only user data you have
//   //         }
//   //       });
  
//   //       console.log("Dispatched LOGIN action with user data");
//   //     } else {
//   //       const errorData = await response.json();
//   //       console.error('Authentication failed:', errorData.detail || 'No specific error provided');
//   //     }
//   //   } catch (error) {
//   //     console.error('Login failed:', error);
//   //     // Handle the error (e.g., show an error message to the user)
//   //   }
//   // };
  

//   const register = async (username, email, password, first_name, last_name, role) => {
//     const id = chance.bb_pin();
//     const response = await axios.post('http://127.0.0.1:8000/users/', {
//       id,
//       username,
//       email,
//       password,
//       first_name,
//       last_name,
//       role
//     });

//     let users = response.data;

//     if (window.localStorage.getItem('users') !== undefined && window.localStorage.getItem('users') !== null) {
//       const localUsers = window.localStorage.getItem('users');
//       users = [
//         ...JSON.parse(localUsers),
//         {
//           id,
//           username,
//           email,
//           password,
//           name: `${first_name} ${last_name}`,
//           role
//         }
//       ];
//     }

//     window.localStorage.setItem('users', JSON.stringify(users));
//   };

//   const logout = () => {
//     setSession(null);
//     dispatch({ type: LOGOUT });
//   };

//   const resetPassword = async (email) => {
//     const response = await axios.post('http://127.0.0.1:8000/reset-password', { email });
//     console.log('Password reset request sent for:', email);
//   };

//   const updateProfile = () => {
//     // Implement your profile update logic here
//   };

//   if (!state.isInitialized) {
//     return <Loader />;
//   }

//   return (
//     <JWTContext.Provider value={{ ...state, login, logout, register, resetPassword, updateProfile }}>
//       {children}
//     </JWTContext.Provider>
//   );
// };

// export default JWTContext;



// import { createContext, useEffect, useReducer } from 'react';

// // third-party
// import { Chance } from 'chance';
// import { jwtDecode } from 'jwt-decode';

// // reducer - state management
// import { LOGIN, LOGOUT } from 'store/reducers/actions';
// import authReducer from 'store/reducers/auth';

// // project-imports
// import Loader from 'components/Loader';
// import axios from 'utils/axios';

// const chance = new Chance();

// // constant
// const initialState = {
//   isLoggedIn: false,
//   isInitialized: false,
//   user: null
// };

// const verifyToken = (serviceToken) => {
//   if (!serviceToken) {
//     return false;
//   }
//   const decoded = jwtDecode(serviceToken);

//   /**
//    * Property 'exp' does not exist on type '<T = unknown>(token: string, options?: JwtDecodeOptions | undefined) => T'.
//    */
//   return decoded.exp > Date.now() / 1000;
// };

// const setSession = (serviceToken) => {
//   if (serviceToken) {
//     localStorage.setItem('serviceToken', serviceToken);
//     axios.defaults.headers.common.Authorization = `Bearer ${serviceToken}`;
//   } else {
//     localStorage.removeItem('serviceToken');
//     delete axios.defaults.headers.common.Authorization;
//   }
// };

// // ==============================|| JWT CONTEXT & PROVIDER ||============================== //

// const JWTContext = createContext(null);

// export const JWTProvider = ({ children }) => {
//   const [state, dispatch] = useReducer(authReducer, initialState);

//   useEffect(() => {
//     const init = async () => {
//       try {
//         const serviceToken = localStorage.getItem('serviceToken');
//         if (serviceToken && verifyToken(serviceToken)) {
//           setSession(serviceToken);
//           const response = await axios.get('/api/account/me');
//           const { user } = response.data;

//           dispatch({
//             type: LOGIN,
//             payload: {
//               isLoggedIn: true,
//               user
//             }
//           });
//         } else {
//           dispatch({
//             type: LOGOUT
//           });
//         }
//       } catch (err) {
//         console.error(err);
//         dispatch({
//           type: LOGOUT
//         });
//       }
//     };

//     init();
//   }, []);

//   // const login = async (email, password) => {
//   //   const response = await axios.post('http://127.0.0.1:8000/auth/token', { email, password });
//   //   const { serviceToken, user } = response.data;
//   //   setSession(serviceToken);
//   //   dispatch({
//   //     type: LOGIN,
//   //     payload: {
//   //       isLoggedIn: true,
//   //       user
//   //     }
//   //   });
//   // };
//   // const login = async (username, password) => {
//   //   const response = await axios.post('http://127.0.0.1:8000/auth/token', { username, password });
//   //   const { accessToken } = response.data;
//   //   setSession(accessToken);
//   //   dispatch({
//   //     type: LOGIN,
//   //     payload: {
//   //       isLoggedIn: true,
//   //       user
//   //     }
//   //   });
//   // };
//   // const login = async (username, password) => {
//   //   const params = new URLSearchParams();
//   //   params.append('username', username);
//   //   params.append('password', password);
  
//   //   const response = await axios.post('http://127.0.0.1:8000/auth/token', params, {
//   //     headers: {
//   //       'Content-Type': 'application/x-www-form-urlencoded'
//   //     }
//   //   });
  
//   //   const { access_token } = response.data;
//   //   setSession(access_token);
//   //   dispatch({
//   //     type: LOGIN,
//   //     payload: {
//   //       isLoggedIn: true,
//   //       user
//   //     }
//   //   });
//   // };

//   // this below is code is working for sign up

// // const login = async (username, password) => {
// //   try {
// //     const params = new URLSearchParams();
// //     params.append('username', username);
// //     params.append('password', password);

// //     const response = await axios.post('http://127.0.0.1:8000/token', params, {
// //       headers: {
// //         'Content-Type': 'application/x-www-form-urlencoded'
// //       }
// //     });

// //     const { access_token } = response.data;
// //     setSession(access_token);

// //     // Assuming `user` is part of the response data
// //     const user = { username }; // Replace this with actual user data if available in response

// //     dispatch({
// //       type: LOGIN,
// //       payload: {
// //         isLoggedIn: true,
// //         user
// //       }
// //     });
// //   } catch (error) {
// //     console.error('Login error:', error);
// //     // Handle error accordingly
// //   }
// // };



// const login = async (username, password) => {
//   try {
//     const params = new URLSearchParams();
//     params.append('username', username);
//     params.append('password', password);

//     const response = await axios.post('http://127.0.0.1:8000/token', params, {
//       headers: {
//         'Content-Type': 'application/x-www-form-urlencoded'
//       }
//     });

//     const { access_token } = response.data;
    
//     // Log the token to the console for inspection
//     console.log('Token received:', access_token);

//     setSession(access_token);
//     const storedToken = localStorage.getItem('serviceToken');
//     console.log('Token after setting in localStorage:', storedToken);
//     // Assuming `user` is part of the response data
//     const user = { username }; // Replace this with actual user data if available in response

//     dispatch({
//       type: LOGIN,
//       payload: {
//         isLoggedIn: true,
//         user
//       }
//     });
//   } catch (error) {
//     console.error('Login error:', error);
//     // Handle error accordingly
//   }
// };


//   // const register = async (email, password, firstName, lastName) => {
//   //   // todo: this flow need to be recode as it not verified
//   //   const id = chance.bb_pin();
//   //   const response = await axios.post('http://127.0.0.1:8000/auth/', {
//   //     id,
//   //     email,
//   //     password,
//   //     firstName,
//   //     lastName
//   //   });
//   //   let users = response.data;

//   //   if (window.localStorage.getItem('users') !== undefined && window.localStorage.getItem('users') !== null) {
//   //     const localUsers = window.localStorage.getItem('users');
//   //     users = [
//   //       ...JSON.parse(localUsers),
//   //       {
//   //         id,
//   //         email,
//   //         password,
//   //         name: `${firstName} ${lastName}`
//   //       }
//   //     ];
//   //   }

//   //   window.localStorage.setItem('users', JSON.stringify(users));
//   // };


//   const register = async (username, email, password, first_name, last_name, role) => {
//     // Generate a unique ID (ensure `chance` is correctly imported and used)
//     const id = chance.bb_pin();
  
//     // Send registration request to the backend
//     const response = await axios.post('http://127.0.0.1:8000/users/', {
//       id,
//       username,   // Added field
//       email,
//       password,
//       first_name,
//       last_name,
//       role         // Added field
//     });

//     let users = response.data;
  
//     // Update local storage with the new user data
//     if (window.localStorage.getItem('users') !== undefined && window.localStorage.getItem('users') !== null) {
//       const localUsers = window.localStorage.getItem('users');
//       users = [
//         ...JSON.parse(localUsers),
//         {
//           id,
//           username,  // Added field
//           email,
//           password,
//           name: `${first_name} ${last_name}`,
//           role       // Added field
//         }
//       ];
//     }
  
//     window.localStorage.setItem('users', JSON.stringify(users));
//   };
  


//   const logout = () => {
//     setSession(null);
//     dispatch({ type: LOGOUT });
//   };

//   const resetPassword = async (email) => {
//     console.log('email - ', email);
//   };

//   const updateProfile = () => {};

//   if (state.isInitialized !== undefined && !state.isInitialized) {
//     return <Loader />;
//   }

//   return <JWTContext.Provider value={{ ...state, login, logout, register, resetPassword, updateProfile }}>{children}</JWTContext.Provider>;
// };

// import { createContext, useEffect, useReducer } from 'react';

// // third-party
// import { Chance } from 'chance';
// import { jwtDecode } from 'jwt-decode'; // Correct the import statement

// // reducer - state management
// import { LOGIN, LOGOUT } from 'store/reducers/actions';
// import authReducer from 'store/reducers/auth';

// // project-imports
// import Loader from 'components/Loader';
// import axios from 'utils/axios';

// const chance = new Chance();

// // constant
// const initialState = {
//   isLoggedIn: false,
//   isInitialized: false,
//   user: null
// };

// const verifyToken = (serviceToken) => {
//   if (!serviceToken) {
//     return false;
//   }
//   const decoded = jwtDecode(serviceToken);
//   return decoded.exp > Date.now() / 1000;
// };

// const setSession = (serviceToken) => {
//   if (serviceToken) {
//     localStorage.setItem('serviceToken', serviceToken);
//     axios.defaults.headers.common.Authorization = `Bearer ${serviceToken}`;
//   } else {
//     localStorage.removeItem('serviceToken');
//     delete axios.defaults.headers.common.Authorization;
//   }
// };

// const JWTContext = createContext(null);

// export const JWTProvider = ({ children }) => {
//   const [state, dispatch] = useReducer(authReducer, initialState);

//   useEffect(() => {
//     const init = async () => {
//       try {
//         const serviceToken = localStorage.getItem('serviceToken');
//         console.log('Service Token:', serviceToken);
  
//         // Check if the token exists and is valid
//         if (serviceToken && verifyToken(serviceToken)) {
//   console.log('Token is valid');
//   setSession(serviceToken);

//   try {
//     console.log("Attempting to fetch user data...");
//     const response = await axios.get('http://127.0.0.1:8000/users/get');
//     console.log("Service token working, response:", response);

//     const user = response.data?.user;

//     if (user) {
//       console.log("User data extracted:", user);
//       dispatch({
//         type: LOGIN,
//         payload: {
//           isLoggedIn: true,
//           user
//         }
//       });
//       console.log("Dispatched LOGIN action with user data");
//     } else {
//       console.error('User data is missing from the response');
//       dispatch({
//         type: LOGOUT
//       });
//     }
//   } catch (apiError) {
//     console.error('Error occurred while fetching user data:', apiError.response?.data || apiError.message);
//     dispatch({
//       type: LOGOUT
//     });
//   }
// } else {
//   console.log("No valid token found, logging out");
//   localStorage.removeItem('serviceToken'); // Clear any invalid or expired token
//   dispatch({
//     type: LOGOUT
//   });
// }

  

//   const login = async (username, password) => {
//     try {
//       const params = new URLSearchParams();
//       params.append('username', username);
//       params.append('password', password);
  
//       const response = await axios.post('http://127.0.0.1:8000/token', params, {
//         headers: {
//           'Content-Type': 'application/x-www-form-urlencoded'
//         }
//       });
  
//       console.log("=========================");
//       console.log(response);
  
//       if (response.data.access_token) {  // Check for access_token instead of serviceToken
//         const { access_token: serviceToken } = response.data;  // Rename access_token to serviceToken
//         setSession(serviceToken);
//         dispatch({
//           type: LOGIN,
//           payload: {
//             isLoggedIn: true,
//             user: { username },  // Assuming the username is the only user data you have
//           }
//         });
//       } else {
//         console.error('No token received from backend');
//       }
//     } catch (error) {
//       console.error('Login failed:', error);
//       // Handle the error (e.g., show an error message to the user)
//     }
//   };
  
  

//   const register = async (email, password, firstName, lastName) => {
//     const id = chance.bb_pin();
//     const response = await axios.post('http://127.0.0.1:8000/users/', { // Replace with your actual registration endpoint
//       id,
//       email,
//       password,
//       firstName,
//       lastName
//     });
//     let users = response.data;

//     if (window.localStorage.getItem('users') !== undefined && window.localStorage.getItem('users') !== null) {
//       const localUsers = window.localStorage.getItem('users');
//       users = [
//         ...JSON.parse(localUsers),
//         {
//           id,
//           email,
//           password,
//           name: `${firstName} ${lastName}`
//         }
//       ];
//     }

//     window.localStorage.setItem('users', JSON.stringify(users));
//   };

//   const logout = () => {
//     setSession(null);
//     dispatch({ type: LOGOUT });
//   };

//   const resetPassword = async (email) => {
//     const response = await axios.post('http://127.0.0.1:8000/reset-password', { email }); // Replace with your actual password reset endpoint
//     console.log('Password reset request sent for:', email);
//   };

//   const updateProfile = () => {
//     // Implement your profile update logic here
//   };

//   if (!state.isInitialized) {
//     return <Loader />;
//   }

//   return (
//     <JWTContext.Provider value={{ ...state, login, logout, register, resetPassword, updateProfile }}>
//       {children}
//     </JWTContext.Provider>
//   );
// };

// export default JWTContext;

// import { createContext, useEffect, useReducer } from 'react';

// // third-party
// import { Chance } from 'chance';
// import { jwtDecode } from 'jwt-decode';

// // reducer - state management
// import { LOGIN, LOGOUT } from 'store/reducers/actions';
// import authReducer from 'store/reducers/auth';

// // project-imports
// import Loader from 'components/Loader';
// import axios from 'utils/axios';

// const chance = new Chance();

// // constant
// const initialState = {
//   isLoggedIn: false,
//   isInitialized: false,
//   user: null
// };

// const verifyToken = (serviceToken) => {
//   if (!serviceToken) {
//     return false;
//   }
//   const decoded = jwtDecode(serviceToken);

//   /**
//    * Property 'exp' does not exist on type '<T = unknown>(token: string, options?: JwtDecodeOptions | undefined) => T'.
//    */
//   return decoded.exp > Date.now() / 1000;
// };

// const setSession = (serviceToken) => {
//   if (serviceToken) {
//     localStorage.setItem('serviceToken', serviceToken);
//     axios.defaults.headers.common.Authorization = `Bearer ${serviceToken}`;
//   } else {
//     localStorage.removeItem('serviceToken');
//     delete axios.defaults.headers.common.Authorization;
//   }
// };

// // ==============================|| JWT CONTEXT & PROVIDER ||============================== //

// const JWTContext = createContext(null);

// export const JWTProvider = ({ children }) => {
//   const [state, dispatch] = useReducer(authReducer, initialState);

//   useEffect(() => {
//     const init = async () => {
//       try {
//         const serviceToken = localStorage.getItem('serviceToken');
//         if (serviceToken && verifyToken(serviceToken)) {
//           setSession(serviceToken);
//           const response = await axios.get('http://127.0.0.1:8000/users/get');
//           const { user } = response.data;

//           dispatch({
//             type: LOGIN,
//             payload: {
//               isLoggedIn: true,
//               user
//             }
//           });
//         } else {
//           dispatch({
//             type: LOGOUT
//           });
//         }
//       } catch (err) {
//         console.error(err);
//         dispatch({
//           type: LOGOUT
//         });
//       }
//     };

//     init();
//   }, []);


  
//   // const login = async (username, password) => {
//   //   try {
//   //     const params = new URLSearchParams();
//   //     params.append('username', username);
//   //     params.append('password', password);
  
//   //     const response = await axios.post('http://127.0.0.1:8000/token', params, {
//   //       headers: {
//   //         'Content-Type': 'application/x-www-form-urlencoded'
//   //       }
//   //     });
  
//   //     console.log("=========================");
//   //     console.log(response);
  
//   //     if (response.data.access_token) {  // Check for access_token instead of serviceToken
//   //       const { access_token: serviceToken } = response.data;  // Rename access_token to serviceToken
//   //       setSession(serviceToken);
//   //       dispatch({
//   //         type: LOGIN,
//   //         payload: {
//   //           isLoggedIn: true,
//   //           user: { username },  // Assuming the username is the only user data you have
//   //         }
//   //       });
//   //     } else {
//   //       console.error('No token received from backend');
//   //     }
//   //   } catch (error) {
//   //     console.error('Login failed:', error);
//   //     // Handle the error (e.g., show an error message to the user)
//   //   }
//   // };

 
//   const login = async (username, password) => {
//     try {
//       const params = new URLSearchParams();
//       params.append('username', username);
//       params.append('password', password);
  
//       const response = await axios.post('http://localhost:8080/auth/login', params, {
//         headers: {
//           'Content-Type': 'application/x-www-form-urlencoded'
//         }
//       });
  
//       // Assuming your FastAPI backend returns 'access_token' in response.data
//       if (response.data.access_token) {
//         const serviceToken = response.data.access_token;
//         setSession(serviceToken);
  
//         // Assuming the response also includes user information, otherwise adjust this
//         const user = response.data.user || { username }; // Fallback to username if user object is not provided
  
//         dispatch({
//           type: LOGIN,
//           payload: {
//             isLoggedIn: true,
//             user
//           }
//         });
//       } else {
//         console.error('No token received from backend');
//         dispatch({
//           type: LOGIN_ERROR,
//           payload: {
//             isLoggedIn: false,
//             error: 'No token received'
//           }
//         });
//       }
//     } catch (error) {
//       console.error('Login failed:', error);
//       dispatch({
//         type: LOGIN_ERROR,
//         payload: {
//           isLoggedIn: false,
//           error: error.message
//         }
//       });
//     }
//   };
  

//   // const register = async (email, password, firstName, lastName) => {
//   //   // todo: this flow need to be recode as it not verified
//   //   const id = chance.bb_pin();
//   //   const response = await axios.post('/api/account/register', {
//   //     id,
//   //     email,
//   //     password,
//   //     firstName,
//   //     lastName
//   //   });
//   //   let users = response.data;

//   //   if (window.localStorage.getItem('users') !== undefined && window.localStorage.getItem('users') !== null) {
//   //     const localUsers = window.localStorage.getItem('users');
//   //     users = [
//   //       ...JSON.parse(localUsers),
//   //       {
//   //         id,
//   //         email,
//   //         password,
//   //         name: `${firstName} ${lastName}`
//   //       }
//   //     ];
//   //   }

//   //   window.localStorage.setItem('users', JSON.stringify(users));
//   // };


//   const register = async (username, email, password, first_name, last_name, role) => {
//     // Generate a unique ID (ensure `chance` is correctly imported and used)
//   const id = chance.bb_pin();
  
//     // Send registration request to the backend
//     const response = await axios.post('http://localhost:8080/auth/register', {
//       id,
//       username,   // Added field
//       email,
//       password,
//       first_name,
//       last_name,
//       role         // Added field
//     });

//     let users = response.data;
  
//     // Update local storage with the new user data
//     if (window.localStorage.getItem('users') !== undefined && window.localStorage.getItem('users') !== null) {
//       const localUsers = window.localStorage.getItem('users');
//       users = [
//         ...JSON.parse(localUsers),
//         {
//           id,
//           username,  // Added field
//           email,
//           password,
//           name: `${first_name} ${last_name}`,
//           role       // Added field
//         }
//       ];
//     }
  
//     window.localStorage.setItem('users', JSON.stringify(users));
//   };
  





//   const logout = () => {
//     setSession(null);
//     dispatch({ type: LOGOUT });
//   };

//   const resetPassword = async (email) => {
//     console.log('email - ', email);
//   };

//   const updateProfile = () => {};

//   if (state.isInitialized !== undefined && !state.isInitialized) {
//     return <Loader />;
//   }

//   return <JWTContext.Provider value={{ ...state, login, logout, register, resetPassword, updateProfile }}>{children}</JWTContext.Provider>;
// };

// export default JWTContext;
import { createContext, useEffect, useReducer, useState } from 'react';
import { Chance } from 'chance';
import { jwtDecode } from 'jwt-decode';
import { LOGIN, LOGOUT } from 'store/reducers/actions';
import authReducer from 'store/reducers/auth';
import Loader from 'components/Loader';
import axios from 'utils/axios';
import Snackbar from '@mui/material/Snackbar';
import Alert from '@mui/material/Alert'; // Import Alert for styled snackbar messages
import LoginSuccessDialog from 'sections/auth/auth-forms/LoginSuccessDialog';
const chance = new Chance();

// constant
const initialState = {
  isLoggedIn: false,
  isInitialized: false,
  user: null
};
const API_URL = import.meta.env.VITE_APP_API_URL;
const verifyToken = (serviceToken) => {
  if (!serviceToken) {
    return false;
  }
  const decoded = jwtDecode(serviceToken);
  return decoded.exp > Date.now() / 1000;
};

const setSession = (serviceToken) => {
  if (serviceToken) {
    localStorage.setItem('serviceToken', serviceToken);
    axios.defaults.headers.common.Authorization = `Bearer ${serviceToken}`;
  } else {
    localStorage.removeItem('serviceToken');
    delete axios.defaults.headers.common.Authorization;
  }
};

const JWTContext = createContext(null);

export const JWTProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);
  console.log("this is state #########", state)
  const [snackbarMessage, setSnackbarMessage] = useState(''); // Message for the Snackbar
  const [snackbarSeverity, setSnackbarSeverity] = useState('success'); // Severity for Snackbar (success, error)
  const [openSnackbar, setOpenSnackbar] = useState(false); // Control for showing the Snackbar
  // const [user, setUser] = useState('');
  // console.log("this is user etails #########", user)

  const handleSnackbarClose = () => {
    setOpenSnackbar(false); // Close the Snackbar
  };

  useEffect(() => {
    const init = async () => {
      try {
        const serviceToken = localStorage.getItem('serviceToken');
        if (serviceToken && verifyToken(serviceToken)) {
          setSession(serviceToken);
          const response = await axios.get(`${API_URL}/individualsRoles/api/account/me`);
          console.log("this is #######repsonse",response.data.user)
          const { user } = response.data;
          // console.log(user.full_name); 
          // setUser(response.data.user)
          dispatch({
            type: LOGIN,
            payload: {
              isLoggedIn: true,
              user
            }
          });
        } else {
          dispatch({
            type: LOGOUT
          });
        }
      } catch (err) {
        console.error(err);
        dispatch({
          type: LOGOUT
        });
      }
    };

    init();
  }, []);

  // LOGIN functionality with Snackbar
  const login = async (username, password) => {
    try {
      const params = new URLSearchParams();
      params.append('username', username);
      params.append('password', password);
  
      const response = await axios.post(`${API_URL}/individualsRoles/login`, params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
  
      if (response.data.access_token) {
        const serviceToken = response.data.access_token;
        setSession(serviceToken);
        
        const user = response.data.user || { username };
        console.log("this is user data ", response.data?.user)
       
        // setUser(response.data)
      


        dispatch({
          type: LOGIN,
          payload: {
            isLoggedIn: true,
            user
          }
        });
        
        
        // Show success message
        setSnackbarMessage('Login successful!');
        setSnackbarSeverity('success');
        setOpenSnackbar(true); // Show the Snackbar
      } else {
        console.error('No token received from backend');
        setSnackbarMessage('Login failed: No token received.');
        setSnackbarSeverity('error');
        setOpenSnackbar(true);
      }
    } catch (error) {
      console.error('Login failed:', error);
      setSnackbarMessage(`Login failed: ${error.message}`);
      setSnackbarSeverity('error');
      setOpenSnackbar(true); // Show error snackbar
    }
  };


  // const login = async (email, password) => {
  //   const response = await axios.post('http://localhost:8080/individualsRoles/login', { username, password });
  //   const { serviceToken, user } = response.data;
  //   setSession(serviceToken);
  //   dispatch({
  //     type: LOGIN,
  //     payload: {
  //       isLoggedIn: true,
  //       user
  //     }
  //   });
  // };

  // REGISTER functionality with Snackbar
  const register = async (username, email, password, first_name, last_name, role) => {
    try {
      const id = chance.bb_pin();
      const response = await axios.post(`${API_URL}/auth/registers`, {
        id,
        username,
        email,
        password,
        first_name,
        last_name,
        role
      });

      const users = response.data;

      window.localStorage.setItem('users', JSON.stringify(users));

      // Show success message for registration
      setSnackbarMessage('Registration successful!');
      setSnackbarSeverity('success');
      setOpenSnackbar(true); // Show success snackbar
    } catch (error) {
      console.error('Registration failed:', error);
      setSnackbarMessage(`Registration failed: ${error.message}`);
      setSnackbarSeverity('error');
      setOpenSnackbar(true); // Show error snackbar
    }
  };

  const resetPassword = async (email) => {
    try {
        console.log("Sending reset request for email:", email);

        const response = await axios.post(`${API_URL}/api/request-reset`, { email });

        console.log("Response:", response.data);
        return response.data; // Return the response
    } catch (error) {
        console.error("Error resetting password:", error);
        throw error; // Ensure error is caught in Formik
    }
};

  // LOGOUT functionality with Snackbar
  const logout = () => {
    setSession(null);
    dispatch({ type: LOGOUT });

    // Show success message for logout
    setSnackbarMessage('Logout successful!');
    setSnackbarSeverity('success');
    setOpenSnackbar(true); // Show success snackbar
  };

  if (state.isInitialized !== undefined && !state.isInitialized) {
    return <Loader />;
  }

  return (
    <JWTContext.Provider value={{ ...state, login, logout, register, resetPassword }}>
      {children}
     
      {/* Snackbar Component */}
      <Snackbar
        open={openSnackbar}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </JWTContext.Provider>
  );
};

export default JWTContext;
