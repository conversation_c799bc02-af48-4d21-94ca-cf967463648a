import Box from "@mui/material/Box";
import MenuItem from "@mui/material/MenuItem";
import InputLabel from "@mui/material/InputLabel";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import { Controller } from "react-hook-form";

const SelectProject = (props) => {
  const {
    id,
    label,
    nameArray,
    register,
    value,
    onChange,
    clearErrors,
    defaultValue,
  } = props;

  const handleOnChange = (event) => {
    onChange(event);
    clearErrors(id);
  };

  return (
    <Box sx={{ "& > *": { mt: 2, mr: 6 } }}>
      <FormControl sx={{ maxWidth: "100%", minWidth: "100%" }}>
        <InputLabel id={id + "-label"}>{label}</InputLabel>
        <Select
          {...register(id, { required: true })}
          label={label}
          defaultValue={defaultValue}
          id={id}
          labelId={id + "-label"}
          value={value}
          onChange={handleOnChange}
        >
          {nameArray?.map((name) => (
            <MenuItem key={name.value} value={name.value}>
              {name.key}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Box>
  );
};

export default SelectProject;
