import { useRef, useState } from 'react';
import { Link ,useLocation} from 'react-router-dom';

// material-ui
import { useTheme } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import CardMedia from '@mui/material/CardMedia';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Popper from '@mui/material/Popper';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import ListSubheader from '@mui/material/ListSubheader';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Settings from '@mui/icons-material/Settings';


// project-imports
import MainCard from 'components/MainCard';
import Dot from 'components/@extended/Dot';
import IconButton from 'components/@extended/IconButton';
import Transitions from 'components/@extended/Transitions';
import AnimateButton from 'components/@extended/AnimateButton';
import { DRAWER_WIDTH, ThemeMode } from 'config';
import GroupIcon from '@mui/icons-material/Group';
import VerifiedUserIcon from '@mui/icons-material/VerifiedUser';
import AccountTreeIcon from '@mui/icons-material/AccountTree';
import { useRBAC } from 'pages/permissions/RBACContext';
import { PERMISSIONS,MENUS,PAGES } from 'constants';

// assets
import { Windows, ArrowRight3 } from 'iconsax-react';
// import cardBack from 'assets/images/widget/img-dropbox-bg.svg';
// import imageChart from 'assets/images/mega-menu/chart.svg';

// ==============================|| HEADER CONTENT - MEGA MENU SECTION ||============================== //

export default function MegaMenuSection() {
  const theme = useTheme();

  const anchorRef = useRef(null);
  const [open, setOpen] = useState(false);

  const [active, setActive] = useState(false);
  const { canMenuPage } = useRBAC();

  
  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
    setActive(true);
  };

  const handleClose = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpen(false);
    setActive(false);
  };
  

  const iconBackColorOpen = theme.palette.mode === ThemeMode.DARK ? 'background.paper' : 'secondary.200';
  const iconBackColor = theme.palette.mode === ThemeMode.DARK ? 'background.default' : 'secondary.100';

  return (
    <Box sx={{ flexShrink: 0, ml: 0.75 }}>
{/* <IconButton
        variant="light"
        aria-label="open profile"
        ref={anchorRef}
        aria-controls={open ? 'profile-grow' : undefined}
        aria-haspopup="true"
        onClick={handleToggle}
        size="large"
        sx={{
          color: active ? '#5085A5' : 'white', 
          backgroundColor: active ? 'white' : 'transparent', 
          ml: { xs: 0, lg: -2 },
          p: 1,
          '&:hover': {
            bgcolor: '#ffffff',
            color: '#5085A5'
          }
        }}
      > */}
      <IconButton
        color="secondary"
        variant="light"
        aria-label="open profile"
        ref={anchorRef}
        aria-controls={open ? 'profile-grow' : undefined}
        aria-haspopup="true"
        onClick={handleToggle}
        size="large"
        sx={{ color: 'secondary.main', bgcolor: open ? iconBackColorOpen : iconBackColor, ml: { xs: 0, lg: -2 }, p: 1 }}
      >
        <Settings />
      </IconButton>



      <Popper
        placement="bottom-start"
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        transition
        disablePortal
        popperOptions={{ modifiers: [{ name: 'offset', options: { offset: [-180, 9] } }] }}
      >
        {({ TransitionProps }) => (
          <Transitions type="grow" position="top" in={open} {...TransitionProps}>
            <Paper
               sx={{
                boxShadow: theme.customShadows.z1,
                minWidth: 100,
                width: 'auto',
                borderRadius: 2,
                maxWidth: 200, 
                [theme.breakpoints.up('md')]: {
                  width: `calc(100vw - 100px)`, 
                },
                [theme.breakpoints.up('lg')]: {
                  width: `calc(100vw - ${DRAWER_WIDTH + 100}px)`, 
                },
                [theme.breakpoints.up('xl')]: {
                  width: `calc(100vw - ${DRAWER_WIDTH + 140}px)`,
                },
              }}
            >
              <ClickAwayListener onClickAway={handleClose}>
                <MainCard elevation={0} border={false} content={false}>
                  <Grid container>
                    <Grid item xs={12}>
                      <Box
                        sx={{
                          p: 2,
                          '& .MuiList-root': { pb: 0 },
                          '& .MuiListSubheader-root': { p: 0, pb: 0.5 },
                          '& .MuiListItemButton-root': {
                            p: 0.2,
                            '&:hover': { bgcolor: 'transparent', '& .MuiTypography-root': { color: 'primary.main' } }
                          },
                          '& .MuiListItemIcon-root': { minWidth: 16 },
                          '& .MuiTypography-root': {
                            whiteSpace: 'nowrap',
                          },
                        }}
                      >
                        <Grid container spacing={20} justifyContent="center">
                          <Grid item xs={4}>
                            <List
                              component="nav"
                              aria-labelledby="nested-list-user"
                              subheader={
                                <ListSubheader id="nested-list-user" >
                                  <Typography variant="subtitle1" color="text.primary">
                                    USER PERMISSION
                                  </Typography>
                                </ListSubheader>
                              }
                            >
                                 {canMenuPage(MENUS.TOP, PAGES.USERS, PERMISSIONS.READ) && (
                              <ListItemButton disableRipple component={Link} to="/user-management/users"  onClick={() => {
                  setActive(true);
                  setOpen(false);
                }}>
                                <ListItemIcon sx={{  mr: 1,color: 'primary.main' }}>
                                {/* <Dot size={6} color="secondary" variant="outlined" /> */}
                                <GroupIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText primary="Users" />
                              </ListItemButton>
                                  )}
                                      {canMenuPage(MENUS.TOP, PAGES.ROLES, PERMISSIONS.READ) && (
                              <ListItemButton disableRipple component={Link} to="/user-management/roles"  onClick={() => {
                  setActive(true);
                  setOpen(false);
                }}>
                                <ListItemIcon sx={{  mr: 1,color: 'primary.main' }}>
                                {/* <Dot size={6} color="secondary" variant="outlined" /> */}
                                <VerifiedUserIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText primary="Roles" />
                              </ListItemButton>
)}
                              {canMenuPage(MENUS.TOP, PAGES.LEVEL_HIERARCHY, PERMISSIONS.READ) && (
                              <ListItemButton disableRipple component={Link} to="/user-management/level-hierarchy"  onClick={() => {
                  setActive(true);
                  setOpen(false);
                }}> <ListItemIcon sx={{  mr: 1 ,color: 'primary.main'}}>
                  {/* <Dot size={6} color="secondary" variant="outlined" /> */}
                <AccountTreeIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Level Hierarchy" /></ListItemButton>
             )}
                             
                            </List>
                          </Grid>
                          <Grid item xs={4}>
                            {/* <List
                              component="nav"
                              aria-labelledby="nested-list-user"
                              subheader={
                                <ListSubheader id="nested-list-user">
                                  <Typography variant="subtitle1" color="text.primary">
                                    STATIC DATA
                                  </Typography>
                                </ListSubheader>
                              }
                            >
                              <ListItemButton disableRipple component={Link} to="#">
                                <ListItemIcon>
                                  <Dot size={6} color="secondary" variant="outlined" />
                                </ListItemIcon>
                                <ListItemText primary="About us" />
                              </ListItemButton>
                              <ListItemButton disableRipple component={Link} to="/contact-us" target="_blank">
                                <ListItemIcon>
                                  <Dot size={6} color="secondary" variant="outlined" />
                                </ListItemIcon>
                                <ListItemText primary="Contact us" />
                              </ListItemButton>
                              <ListItemButton disableRipple component={Link} to="/price/price1">
                                <ListItemIcon>
                                  <Dot size={6} color="secondary" variant="outlined" />
                                </ListItemIcon>
                                <ListItemText primary="Pricing" />
                              </ListItemButton>
                              <ListItemButton disableRipple component={Link} to="/apps/profiles/user/payment">
                                <ListItemIcon>
                                  <Dot size={6} color="secondary" variant="outlined" />
                                </ListItemIcon>
                                <ListItemText primary="Payment" />
                              </ListItemButton>
                              <ListItemButton disableRipple component={Link} target="_blank" to="/maintenance/under-construction">
                                <ListItemIcon>
                                  <Dot size={6} color="secondary" variant="outlined" />
                                </ListItemIcon>
                                <ListItemText primary="Construction" />
                              </ListItemButton>
                              <ListItemButton disableRipple component={Link} target="_blank" to="/maintenance/coming-soon">
                                <ListItemIcon>
                                  <Dot size={6} color="secondary" variant="outlined" />
                                </ListItemIcon>
                                <ListItemText primary="Coming Soon" />
                              </ListItemButton>
                            </List> */}
                          </Grid>
                          
                        </Grid>
                      </Box>
                    </Grid>
                  </Grid>
                </MainCard>
              </ClickAwayListener>
            </Paper>
          </Transitions>
        )}
      </Popper>
    </Box>
  );
}