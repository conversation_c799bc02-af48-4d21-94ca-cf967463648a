// import PropTypes from 'prop-types';
// import { useState } from 'react';

// // material-ui
// import List from '@mui/material/List';
// import ListItemButton from '@mui/material/ListItemButton';
// import ListItemIcon from '@mui/material/ListItemIcon';
// import ListItemText from '@mui/material/ListItemText';

// // assets
// import { Card, Edit2, Logout, Profile, Profile2User } from 'iconsax-react';

// // ==============================|| HEADER PROFILE - PROFILE TAB ||============================== //

// export default function ProfileTab({ handleLogout }) {
//   const [selectedIndex, setSelectedIndex] = useState(0);
//   const handleListItemClick = (event, index) => {
//     setSelectedIndex(index);
//   };

//   return (
//     <List component="nav" sx={{ p: 0, '& .MuiListItemIcon-root': { minWidth: 32 } }}>
//       <ListItemButton selected={selectedIndex === 0} onClick={(event) => handleListItemClick(event, 0)}>
//         <ListItemIcon>
//           <Edit2 variant="Bulk" size={18} />
//         </ListItemIcon>
//         <ListItemText primary="Edit Profile" />
//       </ListItemButton>
//       <ListItemButton selected={selectedIndex === 1} onClick={(event) => handleListItemClick(event, 1)}>
//         <ListItemIcon>
//           <Profile variant="Bulk" size={18} />
//         </ListItemIcon>
//         <ListItemText primary="View Profile" />
//       </ListItemButton>

//       <ListItemButton selected={selectedIndex === 3} onClick={(event) => handleListItemClick(event, 3)}>
//         <ListItemIcon>
//           <Profile2User variant="Bulk" size={18} />
//         </ListItemIcon>
//         <ListItemText primary="Social Profile" />
//       </ListItemButton>
//       <ListItemButton selected={selectedIndex === 4} onClick={(event) => handleListItemClick(event, 4)}>
//         <ListItemIcon>
//           <Card variant="Bulk" size={18} />
//         </ListItemIcon>
//         <ListItemText primary="Billing" />
//       </ListItemButton>
//       <ListItemButton selected={selectedIndex === 2} onClick={handleLogout}>
//         <ListItemIcon>
//           <Logout variant="Bulk" size={18} />
//         </ListItemIcon>
//         <ListItemText primary="Logout" />
//       </ListItemButton>
//     </List>
//   );
// }

// ProfileTab.propTypes = { handleLogout: PropTypes.func };


// import PropTypes from 'prop-types';
// import { useState, lazy } from 'react';
// import Loadable from 'components/Loadable';
// // material-ui
// import List from '@mui/material/List';
// import ListItemButton from '@mui/material/ListItemButton';
// import ListItemIcon from '@mui/material/ListItemIcon';
// import ListItemText from '@mui/material/ListItemText';

// // assets
// import { Card, Edit2, Logout, Profile, Profile2User } from 'iconsax-react';

// // Import the ContactInfo component

// const Profilepage = Loadable(lazy(() => import('pages/profile')));
// // ==============================|| HEADER PROFILE - PROFILE TAB ||============================== //

// export default function ProfileTab({ handleLogout }) {
//   const [selectedIndex, setSelectedIndex] = useState(0);

//   const handleListItemClick = (event, index) => {
//     setSelectedIndex(index);
//   };

//   return (
//     <div>
//       <List component="nav" sx={{ p: 0, '& .MuiListItemIcon-root': { minWidth: 32 } }}>
//         <ListItemButton selected={selectedIndex === 0} onClick={(event) => handleListItemClick(event, 0)}>
//           <ListItemIcon>
//             <Edit2 variant="Bulk" size={18} />
//           </ListItemIcon>
//           <ListItemText primary="Edit Profile" />
//         </ListItemButton>
//         <ListItemButton selected={selectedIndex === 1} onClick={(event) => handleListItemClick(event, 1)}>
//           <ListItemIcon>
//             <Profile variant="Bulk" size={18} />
//           </ListItemIcon>
//           <ListItemText primary="View Profile" />
//         </ListItemButton>

//         <ListItemButton selected={selectedIndex === 3} onClick={(event) => handleListItemClick(event, 3)}>
//           <ListItemIcon>
//             <Profile2User variant="Bulk" size={18} />
//           </ListItemIcon>
//           <ListItemText primary="Social Profile" />
//         </ListItemButton>
//         <ListItemButton selected={selectedIndex === 4} onClick={(event) => handleListItemClick(event, 4)}>
//           <ListItemIcon>
//             <Card variant="Bulk" size={18} />
//           </ListItemIcon>
//           <ListItemText primary="Billing" />
//         </ListItemButton>
//         <ListItemButton selected={selectedIndex === 2} onClick={handleLogout}>
//           <ListItemIcon>
//             <Logout variant="Bulk" size={18} />
//           </ListItemIcon>
//           <ListItemText primary="Logout" />
//         </ListItemButton>
//       </List>

//       {/* Conditionally Render ContactInfo if 'View Profile' is clicked */}
//       {selectedIndex === 1 && <Profilepage />}
//     </div>
//   );
// }

// ProfileTab.propTypes = { handleLogout: PropTypes.func };




import PropTypes from 'prop-types';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom'; // Ensure useNavigate is imported
// material-ui
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';

// assets
import { Card, Edit2, Logout, Profile, Profile2User } from 'iconsax-react';

// ==============================|| HEADER PROFILE - PROFILE TAB ||============================== //

export default function ProfileTab({ handleLogout }) {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const navigate = useNavigate(); // Get the navigate function

  const handleListItemClick = (event, index) => {
    setSelectedIndex(index);

    if (index === 0) {
      console.log('Navigating to /edit-profile');
      navigate('/edit-profile');
    } else if (index === 1) {
      console.log('Navigating to /view-profile');
      navigate('/view-profile');
    } else if (index === 3) {
      console.log('Navigating to /social-profile');
      navigate('/social-profile');
    } else if (index === 4) {
      console.log('Navigating to /billing');
      navigate('/billing');
    }
  };

  return (
    <div>
      <List component="nav" sx={{ p: 0, '& .MuiListItemIcon-root': { minWidth: 32 } }}>
        {/* <ListItemButton selected={selectedIndex === 0} onClick={(event) => handleListItemClick(event, 0)}> */}
          {/* <ListItemIcon>
            <Edit2 variant="Bulk" size={18} />
          </ListItemIcon>
          <ListItemText primary="Edit Profile" /> */}
        {/* </ListItemButton> */}
        <ListItemButton selected={selectedIndex === 1} onClick={(event) => handleListItemClick(event, 1)}>
          <ListItemIcon>
            <Profile variant="Bulk" size={18} />
          </ListItemIcon>
          <ListItemText primary="View Profile" />
        </ListItemButton>

        {/* <ListItemButton selected={selectedIndex === 3} onClick={(event) => handleListItemClick(event, 3)}>
          <ListItemIcon>
            <Profile2User variant="Bulk" size={18} />
          </ListItemIcon>
          <ListItemText primary="Social Profile" />
        </ListItemButton>
        <ListItemButton selected={selectedIndex === 4} onClick={(event) => handleListItemClick(event, 4)}>
          <ListItemIcon>
            <Card variant="Bulk" size={18} />
          </ListItemIcon>
          <ListItemText primary="Billing" />
        </ListItemButton> */}
        <ListItemButton selected={selectedIndex === 2} onClick={handleLogout}>
          <ListItemIcon>
            <Logout variant="Bulk" size={18} />
          </ListItemIcon>
          <ListItemText primary="Logout" />
        </ListItemButton>
      </List>
    </div>
  );
}

ProfileTab.propTypes = { handleLogout: PropTypes.func };
