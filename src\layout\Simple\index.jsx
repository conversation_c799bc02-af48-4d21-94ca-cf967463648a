import PropTypes from 'prop-types';
import { lazy, Suspense } from 'react';
import { Outlet } from 'react-router-dom';

// material-ui
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Toolbar from '@mui/material/Toolbar';

// project imports
import Loader from 'components/Loader';
import Breadcrumbs from 'components/@extended/Breadcrumbs';
import { SimpleLayoutType } from 'config';

const Header = lazy(() => import('./Header'));
const FooterBlock = lazy(() => import('./FooterBlock'));

export default function SimpleLayout({ layout = SimpleLayoutType.SIMPLE }) {
  const container = layout !== SimpleLayoutType.LANDING;

  return (
    <Suspense fallback={<Loader />}>
      <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
        {/* Fixed Header */}
        <Box sx={{ position: 'fixed', top: 0, left: 0, right: 0, zIndex: 1100 }}>
          <Header layout={layout} />
        </Box>

        {/* Main Content */}
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            pt: { xs: 10, sm: 12 }, // space below fixed header
            pb: 2,
            px: { xs: 2, md: 3 },
            display: 'flex',
            flexDirection: 'column',
            flex: 1
          }}
        >
          <Container
            maxWidth={container ? 'xl' : false}
            sx={{
              px: { xs: 0, md: 2 },
              flexGrow: 1,
              display: 'flex',
              flexDirection: 'column'
            }}
          >
            
            <Outlet />
          </Container>
        </Box>

        {/* Footer at bottom */}
        {/* <Box component="footer" sx={{ mt: 'auto' }}>
          <FooterBlock isFull={layout === SimpleLayoutType.LANDING} />
        </Box> */}
      </Box>
    </Suspense>
  );
}

SimpleLayout.propTypes = {
  layout: PropTypes.any
};
