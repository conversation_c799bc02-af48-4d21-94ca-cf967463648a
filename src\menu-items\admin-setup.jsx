// This is example of menu item without group for horizontal layout. There will be no children.

// third-party
import { FormattedMessage } from 'react-intl';

// assets
import { DocumentCode2, Home } from 'iconsax-react';
import AdminPanelSettingsOutlinedIcon from '@mui/icons-material/AdminPanelSettingsOutlined';// type

// icons
const icons = {
  AdminSetup: AdminPanelSettingsOutlinedIcon
};

// ==============================|| MENU ITEMS - SAMPLE PAGE ||============================== //

const AdminSetup = {
  id: 'admin-setup',
  title: <FormattedMessage id="Admin Setup" />,
  type: 'group',
  url: '/admin-setup',
  icon: icons.AdminSetup
};

export default AdminSetup;
