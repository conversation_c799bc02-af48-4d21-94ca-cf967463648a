// This is example of menu item without group for horizontal layout. There will be no children.

// third-party
import { FormattedMessage } from 'react-intl';

// assets
import { DocumentCode2, Graph } from 'iconsax-react';

// type

// icons
const icons = {
  samplePage: Graph
};

// ==============================|| MENU ITEMS - SAMPLE PAGE ||============================== //

const client = {
  id: 'group-inputs',
  title: <FormattedMessage id="Clients" />,
  type: 'group',
  url: '/client',
  // icon: icons.samplePage
};

export default client;
