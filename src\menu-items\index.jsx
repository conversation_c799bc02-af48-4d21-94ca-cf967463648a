

import { PERMISSIONS } from 'constants';

// Import menu files
import samplePage from './sample-page';
import AdminSetup from './admin-setup';

import client_page from './client-page';
import jobrequest from './jobrequest';
import applicant from './applicant-new';
import job_requisition_page from './jobrequsition';
import leads from './leads';
import vendors from './vendor';
import placements from './placements';
import talent_bench from './talent_bench';
import jobtemplate from './job-template';
import AdminSetup_panel from './admin-setup-panel';


const menuItems = {
  items:[samplePage, client_page, jobrequest, applicant, job_requisition_page, leads, vendors, placements, talent_bench, jobtemplate, AdminSetup_panel]
};


// Define access rules for each menu item
// const menuItems = {
//   items: [
//     {
//       ...samplePage,
//       access: { menu: 'Left_Menu', page: 'Dashboard', requiredPermission: PERMISSIONS.READ }
//     },
//     {
//       ...client_page,
//       access: { menu: 'Left_Menu', page: 'Client', requiredPermission: PERMISSIONS.READ }
//     },
//     {
//       ...jobrequest,
//       access: { menu: 'Left_Menu', page: 'Job Request', requiredPermission: PERMISSIONS.READ }
//     },
//     {
//       ...applicant,

//       access: { menu: 'Left_Menu', page: 'Applicants', requiredPermission: PERMISSIONS.READ }
//     },
//     {
//       ...job_requisition_page,
//       access: { menu: 'Left_Menu', page: 'Job_Requisition', requiredPermission: PERMISSIONS.READ }
//     },

//     {
//       ...leads,
//       access: { menu: 'Left_Menu', page: 'Leads', requiredPermission: PERMISSIONS.READ }
//     },
//     {
//       ...vendors,
//       access: { menu: 'Left_Menu', page: 'Vendors', requiredPermission: PERMISSIONS.READ }
//     },
//     {
//       ...placements,
//       access: { menu: 'Left_Menu', page: 'Placements', requiredPermission: PERMISSIONS.READ }
//     },
//     {

//       ...talent_bench,
//       access: { menu: 'Left_Menu', page: 'Talent_Bench', requiredPermission: PERMISSIONS.READ }
//     },
//     {

//       ...jobtemplate,
//       access: { menu: 'Left_Menu', page: 'Job_template', requiredPermission: PERMISSIONS.READ }
//     }

   
//   ]
// };

export default menuItems;

