// This is example of menu item without group for horizontal layout. There will be no children.

// third-party
import { FormattedMessage } from 'react-intl';

// assets
import { DocumentCode2, Home, KyberNetwork } from 'iconsax-react';

// type
// import PeopleAltIcon from '@mui/icons-material/PeopleAlt';
import DocumentScannerIcon from '@mui/icons-material/DocumentScanner';

// icons
const icons = {
  JobTemplate: DocumentScannerIcon
};

// ==============================|| MENU ITEMS - SAMPLE PAGE ||============================== //

const jobtemplate = {
  id: 'jobtemplate',
  title: <FormattedMessage id="Job Template" />,
  type: 'group',
  url: '/jobtemplate',
  icon: icons.JobTemplate
};

export default jobtemplate;
