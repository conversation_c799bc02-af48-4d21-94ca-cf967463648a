// third-party
import { FormattedMessage } from 'react-intl';

// assets
import { DocumentCode2, Home } from 'iconsax-react';

// type

// icons
const icons = {
  profilePage: Home
};

// ==============================|| MENU ITEMS - SAMPLE PAGE ||============================== //

const profilePage = {
  id: 'sample-page',
  title: <FormattedMessage id="Profile" />,
  type: 'group',
  url: '/profile',
  icon: icons.profilePage
};

export default profilePage;
