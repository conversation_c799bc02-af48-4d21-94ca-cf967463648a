// This is example of menu item without group for horizontal layout. There will be no children.

// third-party
import { FormattedMessage } from 'react-intl';

// assets
import { DocumentCode2, Home } from 'iconsax-react';
import DashboardIcon from '@mui/icons-material/Dashboard';
// type

// icons
const icons = {
  samplePage: DashboardIcon
};

// ==============================|| MENU ITEMS - SAMPLE PAGE ||============================== //

const samplePage = {
  id: 'sample-page',
  title: <FormattedMessage id="DashBoard" />,
  type: 'group',
  url: '/sample-page',
  icon: icons.samplePage
};

export default samplePage;
