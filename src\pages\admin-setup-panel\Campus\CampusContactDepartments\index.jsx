import React, { useState } from 'react';
import { <PERSON>po<PERSON>, Box, Button, Grid, Toolt<PERSON>, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddCampusContactDepartmentDialog from './AddCampusContactDepartmentDialog';
import EditCampusContactDepartmentDialog from './EditCampusContactDepartmentDialog';

function CampusContactDepartments() {
  const [departmentDetails, setDepartmentDetails] = useState([
    {
      id: 1,
      department_name: 'Admissions',
      department_code: 'ADM',
      created_by: 'Admin',
      created_on: '2023-01-01',
      modified_by: 'Admin',
      modified_on: '2023-01-01',
      status: 'Active'
    },
    {
      id: 2,
      department_name: 'Financial Aid',
      department_code: 'FA',
      created_by: 'Admin',
      created_on: '2023-01-05',
      modified_by: 'Admin',
      modified_on: '2023-01-05',
      status: 'Active'
    }
  ]);

  const { control } = useForm();

  const [openAddDepartmentDialog, setOpenAddDepartmentDialog] = useState(false);
  const [openEditDepartmentDialog, setOpenEditDepartmentDialog] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState(null);

  const handleAddDepartmentDialogOpen = () => {
    setOpenAddDepartmentDialog(true);
  };

  const handleAddDepartmentDialogClose = () => {
    setOpenAddDepartmentDialog(false);
  };

  const handleAddDepartmentSave = (data) => {
    setDepartmentDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'User',
        modified_on: new Date().toISOString().slice(0, 10),
        status: 'Active'
      }
    ]);
    handleAddDepartmentDialogClose();
  };

  const handleEditDepartmentSave = (updatedDepartment) => {
    setDepartmentDetails((prev) => prev.map((department) => (department.id === updatedDepartment.id ? updatedDepartment : department)));
    setOpenEditDepartmentDialog(false);
    setSelectedDepartment(null);
  };

  const DepartmentActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedDepartment(params.row);
      setOpenEditDepartmentDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete department:', params.row);
      setDepartmentDetails((prev) => prev.filter((department) => department.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const departmentColumns = [
    {
      field: 'department_name',
      headerName: 'CAMPUS CONTACT DEPARTMENT',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_on',
      headerName: 'CREATED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'LAST MODIFIED',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <DepartmentActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <>
      <CustomCardHeader
        // title="Campus Contact Departments"
        secondary={
          <Box display="flex" alignItems="center" gap={1}>
            <Button variant="outlined" size="small">
              Activities
            </Button>
            <Button variant="contained" size="small" color="primary" onClick={handleAddDepartmentDialogOpen}>
              + Add
            </Button>
          </Box>
        }
        sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
      />
      
      <Divider sx={{ mb: 1 }} />
      <Box display="flex" flexDirection={{ xs: 'column', sm: 'row' }} justifyContent="space-between" alignItems={{ xs: 'flex-start', sm: 'center' }} mb={2} mt={2}>
        {/* <Grid item xs={12} sm={3}>
          <CustomNameField
            name="search_department"
            control={control}
            placeholder="Search"
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)', width: { xs: '100%', sm: 'auto' } }}
          />
        </Grid> */}
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%', overflowX: 'auto' }}>
            <CustomDataGrid
              rows={departmentDetails}
              columns={departmentColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={departmentDetails.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddCampusContactDepartmentDialog
        open={openAddDepartmentDialog}
        onClose={handleAddDepartmentDialogClose}
        onSave={handleAddDepartmentSave}
      />
      <EditCampusContactDepartmentDialog
        open={openEditDepartmentDialog}
        onClose={() => setOpenEditDepartmentDialog(false)}
        onSave={handleEditDepartmentSave}
        department={selectedDepartment}
      />
    </>
  );
}

export default CampusContactDepartments;