import React, { useEffect } from 'react';
import { Drawer, Typo<PERSON>, Box, Divider, IconButton, Button, Grid, Stack, InputLabel } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm } from 'react-hook-form';
import CustomN<PERSON>Field from 'components/custom-components/CustomNameField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';

function EditCampusDocumentTypeDialog({ open, onClose, onSave, documentType }) {
  const { control, handleSubmit, reset, setValue } = useForm();

  useEffect(() => {
    if (open && documentType) {
      setValue('document_type_name', documentType.document_type_name);
      setValue('access_roles', documentType.access_roles || []);
    }
  }, [open, documentType, setValue]);

  const handleSave = (data) => {
    onSave({ ...documentType, ...data });
    reset();
  };

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  const accessRolesOptions = [
    { value: 'Administrator' },
    { value: 'Hiring Manager' },
    { value: 'HR Manager' },
    { value: 'Lead Recruiter' },
    { value: 'Technical Recruiter' },
  ];

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box sx={{ width: { xs: '100%', sm: 400 }, height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Sticky Header */}
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">Edit Campus Document Type</Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <Divider />

        {/* Form */}
        <Box component="form" onSubmit={handleSubmit(handleSave)} sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel htmlFor="document_type_name">Document Type *</InputLabel>
                <CustomNameField name="document_type_name" control={control} placeholder="Enter Document Type"
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)'}}
                          rules={{ required: 'Document Type is required' }}
                          />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel htmlFor="access_roles">Access Roles *</InputLabel>
                <CustomDropdownField
                  name="access_roles"
                  control={control}
                  placeholder="Select Access Role"
                  options={accessRolesOptions}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  rules={{ required: 'Access Roles are required' }}
                />
              </Stack>
            </Grid>
          </Grid>
        </Box>

        {/* Footer */}
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            onClick={() => {
              reset();
              onClose();
            }}
            size="small"
            variant="outlined"
          >
            Cancel
          </Button>
          <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(handleSave)}>
            Save
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
}

export default EditCampusDocumentTypeDialog; 