import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Custom<PERSON>ameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddCampusDocumentTypeDialog from './AddCampusDocumentTypeDialog';
import EditCampusDocumentTypeDialog from './EditCampusDocumentTypeDialog';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import Divider from '@mui/material/Divider';

function CampusDocumentTypes() {
  const [documentTypeDetails, setDocumentTypeDetails] = useState([
    {
      id: 1,
      document_type_name: 'Transcript',
      created_by: 'Admin',
      created_on: '2023-01-01',
      modified_by: 'Admin',
      modified_on: '2023-01-01',
      status: 'Active'
    },
    {
      id: 2,
      document_type_name: 'Application Form',
      created_by: 'Admin',
      created_on: '2023-01-05',
      modified_by: 'Admin',
      modified_on: '2023-01-05',
      status: 'Active'
    }
  ]);

  const { control } = useForm();

  const [openAddDocumentTypeDialog, setOpenAddDocumentTypeDialog] = useState(false);
  const [openEditDocumentTypeDialog, setOpenEditDocumentTypeDialog] = useState(false);
  const [selectedDocumentType, setSelectedDocumentType] = useState(null);

  const handleAddDocumentTypeDialogOpen = () => {
    setOpenAddDocumentTypeDialog(true);
  };

  const handleAddDocumentTypeDialogClose = () => {
    setOpenAddDocumentTypeDialog(false);
  };

  const handleAddDocumentTypeSave = (data) => {
    setDocumentTypeDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'User',
        modified_on: new Date().toISOString().slice(0, 10),
        status: 'Active'
      }
    ]);
    handleAddDocumentTypeDialogClose();
  };

  const handleEditDocumentTypeSave = (updatedDocumentType) => {
    setDocumentTypeDetails((prev) => prev.map((documentType) => (documentType.id === updatedDocumentType.id ? updatedDocumentType : documentType)));
    setOpenEditDocumentTypeDialog(false);
    setSelectedDocumentType(null);
  };

  const DocumentTypeActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedDocumentType(params.row);
      setOpenEditDocumentTypeDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete document type:', params.row);
      setDocumentTypeDetails((prev) => prev.filter((documentType) => documentType.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const documentTypeColumns = [
    {
      field: 'document_type_name',
      headerName: 'CAMPUS DOCUMENT TYPE',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'LAST MODIFIED',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <DocumentTypeActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <>
      <CustomCardHeader
        // title="Campus Document Types"
        secondary={
          <Box display="flex" alignItems="center" gap={1}>
            <Button variant="outlined" size="small">
              Activities
            </Button>
            <Button variant="contained" size="small" color="primary" onClick={handleAddDocumentTypeDialogOpen}>
              + Add
            </Button>
          </Box>
        }
        sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
      />
      <Divider sx={{ mb: 1 }} />
      <Box display="flex" flexDirection={{ xs: 'column', sm: 'row' }} justifyContent="space-between" alignItems={{ xs: 'flex-start', sm: 'center' }} mb={2} mt={2}>
        {/* <Grid item xs={12} sm={3}>
          <CustomNameField
            name="search_document_type"
            control={control}
            placeholder="Search"
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)', width: { xs: '100%', sm: 'auto' } }}
          />
        </Grid> */}
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%', overflowX: 'auto' }}>
            <CustomDataGrid
              rows={documentTypeDetails}
              columns={documentTypeColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={documentTypeDetails.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddCampusDocumentTypeDialog
        open={openAddDocumentTypeDialog}
        onClose={handleAddDocumentTypeDialogClose}
        onSave={handleAddDocumentTypeSave}
      />
      <EditCampusDocumentTypeDialog
        open={openEditDocumentTypeDialog}
        onClose={() => setOpenEditDocumentTypeDialog(false)}
        onSave={handleEditDocumentTypeSave}
        documentType={selectedDocumentType}
      />
    </>
  );
}

export default CampusDocumentTypes;