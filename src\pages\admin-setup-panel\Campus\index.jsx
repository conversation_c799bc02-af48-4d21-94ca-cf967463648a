import React, { useState } from 'react';
import { Box, Tabs, Tab, Typography } from '@mui/material';
import MainCard from 'components/MainCard';
import CampusContactDepartments from './CampusContactDepartments';
import CampusDocumentTypes from './CampusDocumentTypes';
import CampusTypes from './CampusTypes';

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`
  };
}

function Campus() {
  const [selectedTab, setSelectedTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  return (
    <MainCard >
      <Tabs value={selectedTab} onChange={handleTabChange} aria-label="lookups" variant="scrollable" scrollButtons="auto"  sx={{
          mt:-2,
          ml: -2,
          alignItems: 'flex-start',
          justifyContent: 'flex-start',
          borderBottom: 0.2,
          borderColor: 'divider'
        }}>
        <Tab label="Campus Contact Departments" {...a11yProps(0)} />
        <Tab label="Campus Document Types" {...a11yProps(1)} />
        <Tab label="Campus Types" {...a11yProps(2)} />
      </Tabs>
      <Box >
        {selectedTab === 0 && <CampusContactDepartments />}
        {selectedTab === 1 && <CampusDocumentTypes />}
        {selectedTab === 2 && <CampusTypes />}
      </Box>
    </MainCard>
  );
}

export default Campus;
