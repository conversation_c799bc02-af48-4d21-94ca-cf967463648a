import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Box,
  Button,
  Grid,
  Stack,
  Checkbox,
  FormControlLabel,
  Divider,IconButton
} from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import CustomN<PERSON>Field from 'components/custom-components/CustomNameField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import ReactDraft<PERSON>uto from './ReactDraftAuto';
import MainCard from 'components/MainCard';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';


function EditApplicantStatus({ open, onClose, onSave, applicantStatus }) {
  const { control, handleSubmit, reset, watch, setValue, formState: { errors } } = useForm();
  const [showMailFields, setShowMailFields] = useState(false);

  const mailingDescriptionTableOptions = [{ value: 'JobSeeker', label: 'Job Seeker' }];
  const mailingDescriptionFieldOptions = [
    { value: 'applicant_id', label: 'Applicant ID' },
    { value: 'first_name', label: 'First Name' },
    { value: 'last_name', label: 'Last Name' },
    { value: 'middle_name', label: 'Middle Name' },
    { value: 'nick_name', label: 'Nick Name' },
    { value: 'consultant_name', label: 'Consultant Name' }
  ];
  const mailingDescriptionSelectOptions = [
    { value: 'subject', label: 'Add to Subject' },
    { value: 'body', label: 'Add to Body' }
  ];

  const handleAddDescription = () => {
    const table = watch('mailing_description_table');
    const field = watch('mailing_description_field');
    const target = watch('mailing_description_select');

    if (table && field && target) {
      const formatted = `{${table}: ${field}}`;
      if (target === 'subject') {
        setValue('mailing_subject_text', (watch('mailing_subject_text') || '') + formatted);
      } else {
        setValue('mailing_body', (watch('mailing_body') || '') + formatted);
      }
    }
  };

  useEffect(() => {
    if (open && applicantStatus) {
      reset({
        name: applicantStatus.name || '',
        is_default: applicantStatus.is_default || false,
        trigger_mail: applicantStatus.trigger_mail || false,
        restrict_submissions: applicantStatus.restrict_submissions || false,
        to: applicantStatus.to || '',
        cc: applicantStatus.cc || '',
        bcc: applicantStatus.bcc || '',
        mailing_subject_text: applicantStatus.mailing_subject_text || '',
        mailing_body: applicantStatus.mailing_body || ''
      });
      setShowMailFields(applicantStatus.trigger_mail || false);
    }
  }, [open, applicantStatus, reset]);

  const onSubmit = (data) => {
    onSave({ ...applicantStatus, ...data });
    reset();
    setShowMailFields(false);
  };

  return (
    <MainCard
      title="Edit Application Status"
      secondary={
        <Box display="flex" alignItems="center" gap={2}>
          <IconButton onClick={onClose} sx={{ color: 'text.secondary' }}>
            <ArrowBackIcon />
          </IconButton>
        </Box>
      }
      sx={{ borderRadius: 0, backgroundColor: 'white', mt: 1 }}
    >
      <Box component="form" onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={2} alignItems="center">

          {/* Applicant Status Name */}
          <Grid item xs={12} container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6}>
              <CustomInputLabel>Applicant Status Name *</CustomInputLabel>
            </Grid>
            <Grid item xs={12} sm={6}>
              <CustomNameField
                name="name"
                control={control}
                placeholder="Enter Status Name"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                rules={{ required: 'This field is required' }}
              />
            </Grid>
          </Grid>

          {/* Is Default */}
          <Grid item xs={12} container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6}>
              <CustomInputLabel>Is Default</CustomInputLabel>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Controller
                    name="is_default"
                    control={control}
                    defaultValue={false}
                    render={({ field }) => <Checkbox {...field} checked={field.value} />}
                  />
                }
                label=""
              />
            </Grid>
          </Grid>

          {/* Trigger Mail */}
          <Grid item xs={12} container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6}>
              <CustomInputLabel>Trigger Mail</CustomInputLabel>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Controller
                    name="trigger_mail"
                    control={control}
                    defaultValue={false}
                    render={({ field }) => (
                      <Checkbox
                        {...field}
                        checked={field.value}
                        onChange={(e) => {
                          field.onChange(e);
                          setShowMailFields(e.target.checked);
                        }}
                      />
                    )}
                  />
                }
                label=""
              />
            </Grid>
          </Grid>

          {/* Mailing Section */}
          {showMailFields && (
            <>
              {/* To */}
              <Grid item xs={12} container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6}>
                  <CustomInputLabel>To *</CustomInputLabel>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <CustomDropdownField
                    name="to"
                    control={control}
                    placeholder="Select"
                    options={[]}
                    sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                    rules={{ required: 'This field is required' }}
                  />
                </Grid>
              </Grid>

              {/* CC */}
              <Grid item xs={12} container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6}>
                  <CustomInputLabel>Cc</CustomInputLabel>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <CustomDropdownField name="cc" control={control} placeholder="Select" options={[]} sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} />
                </Grid>
              </Grid>

              {/* BCC */}
              <Grid item xs={12} container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6}>
                  <CustomInputLabel>Bcc</CustomInputLabel>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <CustomDropdownField name="bcc" control={control} placeholder="Select" options={[]} sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} />
                </Grid>
              </Grid>

              {/* Subject */}
              <Grid item xs={12} container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6}>
                  <CustomInputLabel>Mailing Subject *</CustomInputLabel>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <CustomNameField
                    name="mailing_subject_text"
                    control={control}
                    placeholder=""
                    sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                    rules={{ required: 'This field is required' }}
                  />
                </Grid>
              </Grid>

              {/* Description Add Fields */}
              <Grid item xs={12}>
                <CustomInputLabel>Mailing Description</CustomInputLabel>
                <Stack direction="row" spacing={1}>
                  <CustomDropdownField
                    name="mailing_description_table"
                    control={control}
                    placeholder="Table"
                    options={mailingDescriptionTableOptions}
                    sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  />
                  <CustomDropdownField
                    name="mailing_description_field"
                    control={control}
                    placeholder="Field"
                    options={mailingDescriptionFieldOptions}
                    sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  />
                  <CustomDropdownField
                    name="mailing_description_select"
                    control={control}
                    placeholder="Target"
                    options={mailingDescriptionSelectOptions}
                    sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  />
                  <Button variant="contained" onClick={handleAddDescription}>Add</Button>
                </Stack>
              </Grid>

              {/* Body */}
              <Grid item xs={12} container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6}>
                  <CustomInputLabel>Body *</CustomInputLabel>
                </Grid>
                <Grid item xs={12} sm={12}>
                  <ReactDraftAuto
                    name="mailing_body"
                    control={control}
                    placeholder=""
                    sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                    rules={{ required: 'This field is required' }}
                    mergeField={
                      watch('mailing_description_select') === 'body'
                        ? `{${watch('mailing_description_table')}: ${watch('mailing_description_field')}}`
                        : ''
                    }
                  />
                </Grid>
              </Grid>
            </>
          )}

          {/* Restrict Submissions */}
          <Grid item xs={12} container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6}>
              <CustomInputLabel>Restrict Submissions Under This Status</CustomInputLabel>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Controller
                    name="restrict_submissions"
                    control={control}
                    defaultValue={false}
                    render={({ field }) => <Checkbox {...field} checked={field.value} />}
                  />
                }
                label=""
              />
            </Grid>
          </Grid>

          {/* Buttons */}
          <Grid item xs={12} sx={{ mt: 2 }}>
            <Box display="flex" justifyContent="flex-end" gap={2}>
              <Button onClick={() => { reset(); onClose(); }} size="small" variant="outlined">Cancel</Button>
              <Button type="submit" size="small" variant="contained">Save</Button>
            </Box>
          </Grid>
        </Grid>
      </Box>
    </MainCard>
  );
}

export default EditApplicantStatus;
