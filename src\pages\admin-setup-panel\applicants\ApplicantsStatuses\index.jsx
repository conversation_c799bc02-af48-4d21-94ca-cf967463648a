import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Button, Grid, <PERSON>ltip, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import AddApplicantStatusDialog from './AddApplicantStatusDialog';
import EditApplicantStatusDialog from './EditApplicantStatusDialog';

function ApplicantsStatuses() {
  const [applicantStatuses, setApplicantStatuses] = useState([
    {
      id: 1,
      name: 'Placed',
      created_by: '<PERSON><PERSON><PERSON><PERSON>',
      modified_by: '<PERSON><PERSON>h<PERSON>n<PERSON>ri',
      last_modified: '2023-01-01',
      restrict_submissions: true
    },
    {
      id: 2,
      name: 'Do Not Submit',
      created_by: '<PERSON><PERSON><PERSON><PERSON>',
      modified_by: '<PERSON><PERSON><PERSON><PERSON>',
      last_modified: '2023-01-05',
      restrict_submissions: false
    },
    {
      id: 3,
      name: 'New Lead',
      created_by: '<PERSON><PERSON>hvi <PERSON>nmuri',
      modified_by: '<PERSON>rudhvi <PERSON>nmuri',
      last_modified: '2023-01-01',
      restrict_submissions: true
    },
    {
      id: 4,
      name: 'Out of Market',
      created_by: 'Prudhvi Kanmuri',
      modified_by: 'Prudhvi Kanmuri',
      last_modified: '2023-01-01',
      restrict_submissions: true
    },
    {
      id: 5,
      name: 'Do NOT call',
      created_by: 'Prudhvi Kanmuri',
      modified_by: 'Prudhvi Kanmuri',
      last_modified: '2023-01-01',
      restrict_submissions: true
    }
  ]);

  const [currentView, setCurrentView] = useState('list');
  const [selectedApplicantStatus, setSelectedApplicantStatus] = useState(null);
  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  const handleAdd = () => setCurrentView('add');
  const handleBackToList = () => {
    setCurrentView('list');
    setSelectedApplicantStatus(null);
  };

  const handleAddSave = (data) => {
    setApplicantStatuses((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        modified_by: 'User',
        last_modified: new Date().toISOString().slice(0, 10)
      }
    ]);
    handleBackToList();
  };

  const handleEditSave = (updatedStatus) => {
    setApplicantStatuses((prev) =>
      prev.map((status) => (status.id === updatedStatus.id ? updatedStatus : status))
    );
    handleBackToList();
  };

  const handleDelete = (id) => {
    setApplicantStatuses((prev) => prev.filter((status) => status.id !== id));
  };

  const ActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const openMenu = (e) => {
      e.stopPropagation();
      setAnchorEl(e.currentTarget);
    };

    const closeMenu = () => setAnchorEl(null);

    const edit = () => {
      setSelectedApplicantStatus(params.row);
      setCurrentView('edit');
      closeMenu();
    };

    const del = () => {
      handleDelete(params.row.id);
      closeMenu();
    };

    return (
      <>
        <IconButton size="small" onClick={openMenu}>
          <MoreVertIcon />
        </IconButton>
        <Menu anchorEl={anchorEl} open={open} onClose={closeMenu}>
          <MenuItem onClick={edit}>Edit</MenuItem>
          <MenuItem onClick={del}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const columns = [
    {
      field: 'name',
      headerName: 'NAME',
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography>{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography>{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography>{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'last_modified',
      headerName: 'LAST MODIFIED',
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography>{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'restrict_submissions',
      headerName: 'RESTRICT SUBMISSIONS',
      flex: 1,
      renderCell: (params) => <Typography>{params.value ? 'Yes' : 'No'}</Typography>
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      sortable: false,
      renderCell: (params) => <ActionCell params={params} />
    }
  ];

 if (currentView === 'add') {
  return <AddApplicantStatusDialog onClose={handleBackToList} onSave={handleAddSave} />;
}

if (currentView === 'edit') {
  return (
    <EditApplicantStatusDialog
      open={true}
      onClose={handleBackToList}
      onSave={handleEditSave}
      applicantStatus={selectedApplicantStatus}
    />
  );
}


  
    return (
    <MainCard
      title="Applicant Statuses"
      sx={{ borderRadius: '0%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
    >
      <CustomCardHeader
       
        secondary={
          <Box display="flex" alignItems="center" gap={1}>
            <Button variant="outlined" size="small">Activities</Button>
            <Button variant="contained" size="small" onClick={handleAdd}>+ Add</Button>
          </Box>
        }
        sx={{ mt: -2 }}
      />
     <Divider sx={{ mb: 1 }} />
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            
            <CustomDataGrid
              rows={applicantStatuses}
              columns={columns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={applicantStatuses.length}
            />
          </Box>
        </Grid>
      </Grid>
    </MainCard>
  );
}

export default ApplicantsStatuses;