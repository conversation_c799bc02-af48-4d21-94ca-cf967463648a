import React, { useState } from 'react';
import { Grid, Switch, FormControlLabel, Box, Stack, Checkbox, Button } from '@mui/material';
import MainCard from 'components/MainCard';
import { useForm, Controller } from 'react-hook-form';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomFormHelperText from 'components/custom-components/CustomFormHelperText';

function ApplicantConfiguration() {
  const { control, watch, setValue } = useForm({
    defaultValues: {
      applicant_profile_unique_check_toggle: false,
      applicant_profile_unique_check_field: 'Email ID',
      talent_bench_unique_check_toggle: false,
      talent_bench_email: false,
      talent_bench_pan_card_number: false,
      talent_bench_passport_number: false,
      location_population_toggle: false
    }
  });

  const applicantProfileUniqueCheckToggle = watch('applicant_profile_unique_check_toggle');
  const talentBenchUniqueCheckToggle = watch('talent_bench_unique_check_toggle');

  const [extraApplicantFields, setExtraApplicantFields] = useState([]);

  const applicantProfileUniqueCheckOptions = [
    { value: 'Email ID', label: 'Email ID' },
    { value: 'Passport Number', label: 'Passport Number' },
    { value: 'Pan card Number', label: 'Pan card Number' },
    { value: 'First Name', label: 'First Name' },
    { value: 'Last Name', label: 'Last Name' },
    { value: 'DOB', label: 'DOB' },
    { value: 'Mobile Number', label: 'Mobile Number' }
  ];

  const handleAddApplicantField = () => {
    const newFieldId = `applicant_profile_unique_check_field_${extraApplicantFields.length + 1}`;
    setExtraApplicantFields([...extraApplicantFields, newFieldId]);
    setValue(newFieldId, 'Email ID'); // Set a default value for the new field
  };

  return (
    <MainCard title="Applicant Configuration"
         sx={{ borderRadius: '0%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
>
      <Grid container spacing={2} alignItems="center">
        {/* Applicant Profile Unique Check */}
        <Grid item xs={12}>
          <Box sx={{ p: 2, border: '1px solid #e0e0e0', borderRadius: '4px' }}>
            <Grid container spacing={2} alignItems="center" justifyContent="space-between">
              <Grid item xs={12} sm={8} lg={6} xl={6}>
                <Stack spacing={0.5}>
                  <CustomInputLabel sx={{ whiteSpace: 'normal', textOverflow: 'unset', overflow: 'visible' }}>
                    Applicant Profile Unique Check
                  </CustomInputLabel>
                  <CustomFormHelperText>
                    (If enabled, Applicant Profile uniqueness will be checked based on email address.)
                  </CustomFormHelperText>
                </Stack>
              </Grid>
              <Grid item xs={12} sm={4} lg={6} xl={6} style={{ textAlign: 'left' }}>
                <FormControlLabel
                  control={
                    <Controller
                      name="applicant_profile_unique_check_toggle"
                      control={control}
                      render={({ field }) => (
                        <Switch {...field} checked={field.value} onChange={(e) => field.onChange(e.target.checked)} color="primary" />
                      )}
                    />
                  }
                  label=""
                />
              </Grid>
            </Grid>
            {applicantProfileUniqueCheckToggle && (
              <Grid container spacing={2} alignItems="center" sx={{ mt: 2 }}>
                <Grid item xs={12} sm={6} lg={6} xl={6} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                  <CustomDropdownField
                    name="applicant_profile_unique_check_field"
                    control={control}
                    placeholder="Email ID"
                    options={applicantProfileUniqueCheckOptions}
                    sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} lg={6} xl={6} sx={{ mb: 2 }}>
                  <Button variant="contained" onClick={handleAddApplicantField}>Add</Button>
                </Grid>
                {extraApplicantFields.map((fieldName) => (
                  <Grid item xs={12} sm={6} lg={6} xl={6} key={fieldName} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                    <CustomDropdownField
                      name={fieldName}
                      control={control}
                      placeholder="Email ID"
                      options={applicantProfileUniqueCheckOptions}
                      sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                    />
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        </Grid>

        {/* Talent Bench Unique Check */}
        <Grid item xs={12}>
          <Box sx={{ p: 2, border: '1px solid #e0e0e0', borderRadius: '4px' }}>
            <Grid container spacing={2} alignItems="center" justifyContent="space-between">
              <Grid item xs={12} sm={8} lg={6} xl={6}>
                <Stack spacing={0.5}>
                  <CustomInputLabel sx={{ whiteSpace: 'normal', textOverflow: 'unset', overflow: 'visible' }}>
                    Talent Bench Unique Check
                  </CustomInputLabel>
                  <CustomFormHelperText>
                    (If enabled, Talent Bench applicant profile uniqueness will be checked based on email address.)
                  </CustomFormHelperText>
                </Stack>
              </Grid>
              <Grid item xs={12} sm={4} lg={6} xl={6} style={{ textAlign: 'left' }}>
                <FormControlLabel
                  control={
                    <Controller
                      name="talent_bench_unique_check_toggle"
                      control={control}
                      render={({ field }) => (
                        <Switch {...field} checked={field.value} onChange={(e) => field.onChange(e.target.checked)} color="primary" />
                      )}
                    />
                  }
                  label=""
                />
              </Grid>
            </Grid>
            {talentBenchUniqueCheckToggle && (
              <Grid container spacing={2} alignItems="center" sx={{ mt: 2 }}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Controller
                        name="talent_bench_email"
                        control={control}
                        defaultValue={false}
                        render={({ field }) => <Checkbox {...field} checked={field.value} />}
                      />
                    }
                    label="Email"
                  />
                  <FormControlLabel
                    control={
                      <Controller
                        name="talent_bench_pan_card_number"
                        control={control}
                        defaultValue={false}
                        render={({ field }) => <Checkbox {...field} checked={field.value} />}
                      />
                    }
                    label="Pan Card Number"
                  />
                  <FormControlLabel
                    control={
                      <Controller
                        name="talent_bench_passport_number"
                        control={control}
                        defaultValue={false}
                        render={({ field }) => <Checkbox {...field} checked={field.value} />}
                      />
                    }
                    label="Passport Number"
                  />
                </Grid>
              </Grid>
            )}
          </Box>
        </Grid>

        {/* Location Population */}
        <Grid item xs={12}>
          <Box sx={{ p: 2, border: '1px solid #e0e0e0', borderRadius: '4px' }}>
            <Grid container spacing={2} alignItems="center" justifyContent="space-between">
              <Grid item xs={12} sm={8} lg={6} xl={6}>
                <Stack spacing={0.5}>
                  <CustomInputLabel sx={{ whiteSpace: 'normal', textOverflow: 'unset', overflow: 'visible' }}>
                    Location Population
                  </CustomInputLabel>
                  <CustomFormHelperText>
                    (Populate the most recent project location (City, State) if the applicant doesn&apos;t have an address on their resume.)
                  </CustomFormHelperText>
                </Stack>
              </Grid>
              <Grid item xs={12} sm={4} lg={6} xl={6} style={{ textAlign: 'left' }}>
                <FormControlLabel
                  control={
                    <Controller
                      name="location_population_toggle"
                      control={control}
                      render={({ field }) => (
                        <Switch {...field} checked={field.value} onChange={(e) => field.onChange(e.target.checked)} color="primary" />
                      )}
                    />
                  }
                  label=""
                />
              </Grid>
            </Grid>
          </Box>
        </Grid>
      </Grid>
    </MainCard>
  );
}

export default ApplicantConfiguration;
