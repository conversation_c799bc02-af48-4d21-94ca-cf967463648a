import React from 'react';
import { <PERSON>rid, Switch, FormControlLabel, FormHelperText, Button } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import { useForm, Controller } from 'react-hook-form';
import CustomNameField from 'components/custom-components/CustomNameField';

function ApplicantSettings() {
  const { control, watch, setValue } = useForm();

  const [enableResumeTitleFormat, setEnableResumeTitleFormat] = React.useState(false);
  const [restrictEmailNotification, setRestrictEmailNotification] = React.useState(false);
  const [sendApproveRejectAction, setSendApproveRejectAction] = React.useState(false);
  const [previewResumeTitle, setPreviewResumeTitle] = React.useState('');
  const [selectedFormatElements, setSelectedFormatElements] = React.useState([]);

  const chooseFieldsValue = watch('choose_fields');
  const specialCharactersValue = watch('special_characters');

  React.useEffect(() => {
    if (enableResumeTitleFormat) {
      const combinedElements = selectedFormatElements.join('');
      let currentLivePreview = '';

      if (chooseFieldsValue) {
        currentLivePreview += chooseFieldsValue;
      }
      if (specialCharactersValue) {
        currentLivePreview += specialCharactersValue === 'Space' ? ' ' : specialCharactersValue;
      }
      setPreviewResumeTitle(combinedElements + currentLivePreview);
    } else {
      setPreviewResumeTitle('');
      setSelectedFormatElements([]);
      setValue('choose_fields', '');
      setValue('special_characters', '');
    }
  }, [enableResumeTitleFormat, selectedFormatElements, chooseFieldsValue, specialCharactersValue, setValue]);

  const handleToggleChange = (setter) => (event) => {
    setter(event.target.checked);
  };

  const handleAddFormatElement = () => {
    let newElement = '';
    if (chooseFieldsValue) {
      newElement += chooseFieldsValue;
    }
    if (specialCharactersValue) {
      newElement += specialCharactersValue === 'Space' ? ' ' : specialCharactersValue;
    }

    if (newElement) {
      setSelectedFormatElements((prev) => [...prev, newElement]);
      setValue('choose_fields', '');
      setValue('special_characters', '');
    }
  };

  return (
    <MainCard
      title="Applicant Settings"
     sx={{ borderRadius: '0%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
      secondary={
        <>
          <Button variant="outlined" color="primary" size="small">
            Activities
          </Button>
          <Button sx={{ ml: 2 }} variant="contained" color="primary" size="small">
            Save
          </Button>
        </>
      }
    >
      <Grid container spacing={2} alignItems="center">
        {/* Default Statuses when applicant is Placed */}
        <Grid item xs={12}>
          <Grid container spacing={2} alignItems="center" justifyContent="space-between">
            <Grid item xs={12} sm={8} lg={6} xl={6}>
              <CustomInputLabel sx={{ whiteSpace: 'normal', textOverflow: 'unset', overflow: 'visible' }}>
                Default Statuses when applicant is Placed
              </CustomInputLabel>
            </Grid>
            <Grid item xs={12} sm={4} lg={6} xl={6} style={{ textAlign: 'left' }}>
              <CustomDropdownField
                name="default_status_placed"
                control={control}
                placeholder="Select"
                options={[
                  { value: 'Do NOT call', label: 'Do NOT call (Allowed to submit)' },
                  { value: 'Do Not Submit', label: 'Do Not Submit (Not allowed to submit)' },
                  { value: 'New lead', label: 'New lead (Allowed to submit)' },
                  { value: 'Out of market', label: 'Out of market (Allowed to submit)' },
                  { value: 'Placed', label: 'Placed (Allowed to submit)' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Grid>
          </Grid>
        </Grid>

        {/* Default Statuses when applicant is BlockListed */}
        <Grid item xs={12}>
          <Grid container spacing={2} alignItems="center" justifyContent="space-between">
            <Grid item xs={12} sm={8} lg={6} xl={6}>
              <CustomInputLabel sx={{ whiteSpace: 'normal', textOverflow: 'unset', overflow: 'visible' }}>
                Default Statuses when applicant is BlockListed
              </CustomInputLabel>
            </Grid>
            <Grid item xs={12} sm={4} lg={6} xl={6} style={{ textAlign: 'left' }}>
              <CustomDropdownField
                name="default_status_blocklisted"
                control={control}
                placeholder="Select"
                options={[
                  { value: 'Do NOT call', label: 'Do NOT call (Allowed to submit)' },
                  { value: 'Do Not Submit', label: 'Do Not Submit (Not allowed to submit)' },
                  { value: 'New lead', label: 'New lead (Allowed to submit)' },
                  { value: 'Out of market', label: 'Out of market (Allowed to submit)' },
                  { value: 'Placed', label: 'Placed (Allowed to submit)' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Grid>
          </Grid>
        </Grid>

        {/* Domain-based Restrictions For Creating Applicants */}
        <Grid item xs={12}>
          <Grid container spacing={2} alignItems="center" justifyContent="space-between">
            <Grid item xs={12} sm={8} lg={6} xl={6}>
              <CustomInputLabel sx={{ whiteSpace: 'normal', textOverflow: 'unset', overflow: 'visible' }}>
                Domain-based Restrictions For Creating Applicants
              </CustomInputLabel>
              <FormHelperText>
                (Resumes with emails from the domain(s) set here will be restricted from creating Applicants.)
              </FormHelperText>
            </Grid>
            <Grid item xs={12} sm={4} lg={6} xl={6} style={{ textAlign: 'left' }}>
              <Controller
                name="domain_restrictions"
                control={control}
                defaultValue=""
                render={({ field }) => (
                  <CustomNameField
                    control={control}
                    {...field}
                    fullWidth
                    placeholder=""
                    sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  />
                )}
              />
            </Grid>
          </Grid>
        </Grid>

        {/* Applicant Bulk Parsing Source */}
        <Grid item xs={12}>
          <Grid container spacing={2} alignItems="center" justifyContent="space-between">
            <Grid item xs={12} sm={8} lg={6} xl={6}>
              <CustomInputLabel sx={{ whiteSpace: 'normal', textOverflow: 'unset', overflow: 'visible' }}>
                Applicant Bulk Parsing Source
              </CustomInputLabel>
            </Grid>
            <Grid item xs={12} sm={4} lg={6} xl={6} style={{ textAlign: 'left' }}>
              <CustomDropdownField
                name="bulk_parsing_source"
                control={control}
                placeholder="Select"
                options={[
                  { value: 'Campus Portal', label: 'Campus Portal' },
                  { value: 'Career Builder', label: 'Career Builder' },
                  { value: 'Career Portal', label: 'Career Portal' },
                  { value: 'Craigslist', label: 'Craigslist' },
                  { value: 'Dice', label: 'Dice' },
                  { value: 'Employee Referral', label: 'Employee Referral' },
                  { value: 'Employment Agency', label: 'Employment Agency' },
                  { value: 'Facebook', label: 'Facebook' },
                  { value: 'Gmail', label: 'Gmail' },
                  { value: 'Indeed', label: 'Indeed' },
                  { value: 'Job Fair', label: 'Job Fair' },
                  { value: 'Linked In', label: 'Linked In' },
                  { value: 'Monster', label: 'Monster' },
                  { value: 'Office 365', label: 'Office 365' },
                  { value: 'Others', label: 'Others' },
                  { value: 'Outlook', label: 'Outlook' },
                  { value: 'Resume Inbox', label: 'Resume Inbox' },
                  { value: 'Stack Overflow', label: 'Stack Overflow' },
                  { value: 'Tech Fetch', label: 'Tech Fetch' },
                  { value: 'Twitter', label: 'Twitter' },
                  { value: 'User Referral', label: 'User Referral' },
                  { value: 'Walk In', label: 'Walk In' },
                  { value: 'Windows Plugin', label: 'Windows Plugin' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Grid>
          </Grid>
        </Grid>

        {/* Enable Resume Title Format */}
        <Grid item xs={12}>
          <Grid container spacing={2} alignItems="center" justifyContent="space-between">
            <Grid item xs={12} sm={8} lg={6} xl={6}>
              <CustomInputLabel sx={{ whiteSpace: 'normal', textOverflow: 'unset', overflow: 'visible' }}>
                Enable Resume Title Format
              </CustomInputLabel>
              <FormHelperText>
                (The configured title will be applied to the applicant&apos;s resume title on the submission record.)
              </FormHelperText>
            </Grid>
            <Grid item xs={12} sm={4} lg={6} xl={6} style={{ textAlign: 'left' }}>
              <FormControlLabel
                control={
                  <Switch checked={enableResumeTitleFormat} onChange={handleToggleChange(setEnableResumeTitleFormat)} color="primary" />
                }
                label=""
              />
            </Grid>
          </Grid>
        </Grid>

        {enableResumeTitleFormat && (
          <Grid item xs={12} sx={{ mt: 2, mb: 2 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={11.5} sm={5.5} lg={3.5} xl={5.5}>
                <CustomInputLabel>Choose Fields</CustomInputLabel>
                <CustomDropdownField
                  name="choose_fields"
                  control={control}
                  placeholder="Select"
                  options={[
                    { value: 'First Name', label: 'First Name' },
                    { value: 'Last Name', label: 'Last Name' },
                    { value: 'Job Title', label: 'Job Title' },
                    { value: 'Resume', label: 'Resume' }
                  ]}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>
              <Grid item xs={11.5} sm={5.5} lg={3.5} xl={5.5}>
                <CustomInputLabel>Special Characters</CustomInputLabel>
                <CustomDropdownField
                  name="special_characters"
                  control={control}
                  placeholder="Select"
                  options={[
                    { value: '-', label: '-' },
                    { value: 'Space', label: 'Space' },
                    { value: 'End', label: 'End' },
                    { value: '_', label: '_' }
                  ]}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>
              <Grid item sx={{ mt: 2 }} xs={0.5}>
                <Button variant="contained" color="primary" fullWidth onClick={handleAddFormatElement}>
                  +Add
                </Button>
              </Grid>
            </Grid>
            {previewResumeTitle && (
              <Grid item xs={12} sx={{ mt: 2 }}>
                <CustomInputLabel>
                  Preview Resume Title : <span style={{ fontWeight: 'bold' }}>{previewResumeTitle}</span>
                </CustomInputLabel>
              </Grid>
            )}
          </Grid>
        )}

        {/* Restrict Email Notification to confirmed and Placed Candidates */}
        <Grid item xs={12}>
          <Grid container spacing={2} alignItems="center" justifyContent="space-between">
            <Grid item xs={12} sm={8} lg={6} xl={6}>
              <CustomInputLabel sx={{ whiteSpace: 'normal', textOverflow: 'unset', overflow: 'visible' }}>
                Restrict Email Notification to confirmed and Placed Candidates
              </CustomInputLabel>
              <FormHelperText>(If it is enabled, The system will restrict all email notifications to placed candidates )</FormHelperText>
            </Grid>
            <Grid item xs={12} sm={4} lg={6} xl={6} style={{ textAlign: 'left' }}>
              <FormControlLabel
                control={
                  <Switch checked={restrictEmailNotification} onChange={handleToggleChange(setRestrictEmailNotification)} color="primary" />
                }
                label=""
              />
            </Grid>
          </Grid>
        </Grid>

        {/* Send Approve, Reject Action in the internal submission email notification */}
        <Grid item xs={12}>
          <Grid container spacing={2} alignItems="center" justifyContent="space-between">
            <Grid item xs={12} sm={8} lg={6} xl={6}>
              <CustomInputLabel sx={{ whiteSpace: 'normal', textOverflow: 'unset', overflow: 'visible' }}>
                Send Approve, Reject Action in the internal submission email notification
              </CustomInputLabel>
            </Grid>
            <Grid item xs={12} sm={4} lg={6} xl={6} style={{ textAlign: 'left' }}>
              <FormControlLabel
                control={
                  <Switch checked={sendApproveRejectAction} onChange={handleToggleChange(setSendApproveRejectAction)} color="primary" />
                }
                label=""
              />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </MainCard>
  );
}
export default ApplicantSettings;
