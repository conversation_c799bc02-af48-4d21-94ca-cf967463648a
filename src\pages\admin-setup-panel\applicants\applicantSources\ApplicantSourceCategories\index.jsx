import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Button, Grid, <PERSON>ltip, IconButton, TextField, Menu, MenuItem, Divider } from '@mui/material';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import AddApplicantSourceCategoryDialog from './AddApplicantSourceCategoryDialog';
import EditApplicantSourceCategoryDialog from './EditApplicantSourceCategoryDialog';

function ApplicantSourceCategories() {
  const [applicantSourceCategories, setApplicantSourceCategories] = useState([
    {
      id: 1,
      name: 'Resume Inbox',
      created_by: '<PERSON>rudh<PERSON> Kan<PERSON>ri',
      modified_by: '<PERSON><PERSON><PERSON><PERSON>n<PERSON>ri',
      last_modified: '2023-01-01'
    },
    {
      id: 2,
      name: 'Others',
      created_by: '<PERSON><PERSON><PERSON><PERSON>',
      modified_by: '<PERSON><PERSON><PERSON><PERSON>',
      last_modified: '2023-01-05'
    },
    {
      id: 3,
      name: 'Technical Form',
      created_by: '<PERSON><PERSON><PERSON>vi <PERSON>nmuri',
      modified_by: '<PERSON><PERSON>hvi <PERSON>nmuri',
      last_modified: '2023-01-01'
    },
    {
      id: 4,
      name: 'Referrals',
      created_by: 'Prudhvi <PERSON>nmuri',
      modified_by: 'Prudhvi <PERSON>nmuri',
      last_modified: '2023-01-01'
    },
    {
      id: 5,
      name: 'Direct Sourcing',
      created_by: 'Prudhvi <PERSON>nmuri',
      modified_by: 'Prudhvi Kanmuri',
      last_modified: '2023-01-01'
    },
    {
      id: 6,
      name: 'CEIPAL TalentHire Career Portal',
      created_by: 'Prudhvi Kanmuri',
      modified_by: 'Prudhvi Kanmuri',
      last_modified: '2023-01-01'
    },
    {
      id: 7,
      name: 'Social Networks',
      created_by: 'Prudhvi Kanmuri',
      modified_by: 'Prudhvi Kanmuri',
      last_modified: '2023-01-01'
    },
    {
      id: 8,
      name: 'Job Boards',
      created_by: 'Prudhvi Kanmuri',
      modified_by: 'Prudhvi Kanmuri',
      last_modified: '2023-01-01'
    }
  ]);

  const [openAddApplicantSourceCategoryDialog, setOpenAddApplicantSourceCategoryDialog] = useState(false);
  const [openEditApplicantSourceCategoryDialog, setOpenEditApplicantSourceCategoryDialog] = useState(false);
  const [selectedApplicantSourceCategory, setSelectedApplicantSourceCategory] = useState(null);

  const handleAddApplicantSourceCategoryDialogOpen = () => {
    setOpenAddApplicantSourceCategoryDialog(true);
  };

  const handleAddApplicantSourceCategoryDialogClose = () => {
    setOpenAddApplicantSourceCategoryDialog(false);
  };

  const handleAddApplicantSourceCategorySave = (data) => {
    setApplicantSourceCategories((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        modified_by: 'User',
        last_modified: new Date().toISOString().slice(0, 10)
      }
    ]);
    handleAddApplicantSourceCategoryDialogClose();
  };

  const handleEditApplicantSourceCategorySave = (updatedCategory) => {
    setApplicantSourceCategories((prev) => prev.map((cat) => (cat.id === updatedCategory.id ? updatedCategory : cat)));
    setOpenEditApplicantSourceCategoryDialog(false);
    setSelectedApplicantSourceCategory(null);
  };

  const ApplicantSourceCategoryActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedApplicantSourceCategory(params.row);
      setOpenEditApplicantSourceCategoryDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete applicant source category:', params.row);
      setApplicantSourceCategories((prev) => prev.filter((cat) => cat.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const applicantSourceCategoriesColumns = [
    {
      field: 'name',
      headerName: 'NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'last_modified',
      headerName: 'LAST MODIFIED',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <ApplicantSourceCategoryActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <>
      <CustomCardHeader
        secondary={
          <Box display="flex" alignItems="center" gap={1}>
            <Button variant="outlined" size="small">
              Activities
            </Button>
            {/* <Button variant="contained" size="small" color="primary" onClick={handleAddApplicantSourceCategoryDialogOpen}>
              + Add
            </Button> */}
          </Box>
        }
      />
      <Divider sx={{ mb: 1 }} />
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={applicantSourceCategories}
              columns={applicantSourceCategoriesColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={applicantSourceCategories.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddApplicantSourceCategoryDialog
        open={openAddApplicantSourceCategoryDialog}
        onClose={handleAddApplicantSourceCategoryDialogClose}
        onSave={handleAddApplicantSourceCategorySave}
      />
      <EditApplicantSourceCategoryDialog
        open={openEditApplicantSourceCategoryDialog}
        onClose={() => setOpenEditApplicantSourceCategoryDialog(false)}
        onSave={handleEditApplicantSourceCategorySave}
        applicantSourceCategory={selectedApplicantSourceCategory}
      />
    </>
  );
}

export default ApplicantSourceCategories;