import React, { useEffect } from 'react';
import { Drawer, Typography, Box, Divider, IconButton, Button, Grid, Stack } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm } from 'react-hook-form';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';

function EditApplicantSourceDialog({ open, onClose, onSave, applicantSource }) {
  const { control, handleSubmit, reset, setValue } = useForm();

  useEffect(() => {
    if (open && applicantSource) {
      setValue('name', applicantSource.name);
      setValue('category', applicantSource.category);
    }
  }, [open, applicantSource, setValue]);

  const handleSave = (data) => {
    onSave({ ...applicantSource, ...data });
    reset();
  };

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box sx={{ width: { xs: 300, sm: 400 }, height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Sticky Header */}
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">Edit Applicant Source</Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <Divider />

        {/* Form */}
        <Box component="form" onSubmit={handleSubmit(handleSave)} sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="name">Applicant Source Name</CustomInputLabel>
                <CustomNameField name="name" control={control} placeholder="Enter Source Name"
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)'}} />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="category">Category</CustomInputLabel>
                <CustomDropdownField
                  name="category"
                  control={control}
                  placeholder="Select Category"
                  options={[
                    { value: 'Web', label: 'Web' },
                    { value: 'Social Media', label: 'Social Media' },
                    { value: 'Referral', label: 'Referral' },
                    { value: 'Job Board', label: 'Job Board' }
                  ]} // Example options, adjust as needed
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>
          </Grid>
        </Box>

        {/* Footer */}
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            onClick={() => {
              reset();
              onClose();
            }}
            size="small"
            variant="outlined"
          >
            Cancel
          </Button>
          <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(handleSave)}>
            Save
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
}

export default EditApplicantSourceDialog; 