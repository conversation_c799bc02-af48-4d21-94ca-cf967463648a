import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, TextField, Menu, MenuItem, Divider } from '@mui/material';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import AddApplicantSourceDialog from './AddApplicantSourceDialog';
import EditApplicantSourceDialog from './EditApplicantSourceDialog';

function ApplicantSource() {
  const [applicantSources, setApplicantSources] = useState([
    {
      id: 1,
      name: 'Campus Portal',
      category: 'Others',
      created_by: '<PERSON><PERSON><PERSON><PERSON>',
      modified_by: '<PERSON><PERSON>h<PERSON>nmu<PERSON>',
      last_modified: '2023-01-10'
    },
    {
      id: 2,
      name: 'Referral Portal',
      category: 'Others',
      created_by: '<PERSON><PERSON><PERSON><PERSON>',
      modified_by: '<PERSON><PERSON><PERSON><PERSON>',
      last_modified: '2023-01-10'
    },
    {
      id: 3,
      name: '<PERSON><PERSON>',
      category: 'Others',
      created_by: '<PERSON><PERSON><PERSON><PERSON>',
      modified_by: 'Prudhvi Kanmuri',
      last_modified: '2023-01-10'
    },
    {
      id: 4,
      name: 'Windows Plugin',
      category: 'Others',
      created_by: 'Prudhvi Kanmuri',
      modified_by: 'Prudhvi Kanmuri',
      last_modified: '2023-01-10'
    },
    {
      id: 5,
      name: 'Office 365',
      category: 'Others',
      created_by: 'Prudhvi Kanmuri',
      modified_by: 'Prudhvi Kanmuri',
      last_modified: '2023-01-10'
    },
    {
      id: 6,
      name: 'Employee Referral',
      category: 'Referrals',
      created_by: 'Prudhvi Kanmuri',
      modified_by: 'Prudhvi Kanmuri',
      last_modified: '2023-01-10'
    },
    {
      id: 7,
      name: 'Outlook',
      category: 'Others',
      created_by: 'Prudhvi Kanmuri',
      modified_by: 'Prudhvi Kanmuri',
      last_modified: '2023-01-10'
    },
    {
      id: 8,
      name: 'Resume Inbox',
      category: 'Resume Inbox',
      created_by: 'Prudhvi Kanmuri',
      modified_by: 'Prudhvi Kanmuri',
      last_modified: '2023-01-10'
    },
    {
      id: 9,
      name: 'Indeed',
      category: 'Default',
      created_by: 'Prudhvi Kanmuri',
      modified_by: 'Prudhvi Kanmuri',
      last_modified: '2023-01-10'
    },
    {
      id: 10,
      name: 'Career Portal',
      category: 'CEIPAL TalentHIre Career Portal',
      created_by: 'Prudhvi Kanmuri',
      modified_by: 'Prudhvi Kanmuri',
      last_modified: '2023-01-10'
    },
  ]);

  const [openAddApplicantSourceDialog, setOpenAddApplicantSourceDialog] = useState(false);
  const [openEditApplicantSourceDialog, setOpenEditApplicantSourceDialog] = useState(false);
  const [selectedApplicantSource, setSelectedApplicantSource] = useState(null);

  const handleAddApplicantSourceDialogOpen = () => {
    setOpenAddApplicantSourceDialog(true);
  };

  const handleAddApplicantSourceDialogClose = () => {
    setOpenAddApplicantSourceDialog(false);
  };

  const handleAddApplicantSourceSave = (data) => {
    setApplicantSources((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        modified_by: 'User',
        last_modified: new Date().toISOString().slice(0, 10)
      }
    ]);
    handleAddApplicantSourceDialogClose();
  };

  const handleEditApplicantSourceSave = (updatedSource) => {
    setApplicantSources((prev) => prev.map((source) => (source.id === updatedSource.id ? updatedSource : source)));
    setOpenEditApplicantSourceDialog(false);
    setSelectedApplicantSource(null);
  };

  const ApplicantSourceActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedApplicantSource(params.row);
      setOpenEditApplicantSourceDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete applicant source:', params.row);
      setApplicantSources((prev) => prev.filter((source) => source.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const applicantSourcesColumns = [
    {
      field: 'name',
      headerName: 'NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'category',
      headerName: 'CATEGORY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'last_modified',
      headerName: 'LAST MODIFIED',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <ApplicantSourceActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <>
      <CustomCardHeader
        secondary={
          <Box display="flex" alignItems="center" gap={1}>
            <Button variant="outlined" size="small">
              Activities
            </Button>
            <Button variant="contained" size="small" color="primary" onClick={handleAddApplicantSourceDialogOpen}>
              + Add
            </Button>
          </Box>
        }
      />
      <Divider sx={{ mb: 1 }} />
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={applicantSources}
              columns={applicantSourcesColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={applicantSources.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddApplicantSourceDialog
        open={openAddApplicantSourceDialog}
        onClose={handleAddApplicantSourceDialogClose}
        onSave={handleAddApplicantSourceSave}
      />
      <EditApplicantSourceDialog
        open={openEditApplicantSourceDialog}
        onClose={() => setOpenEditApplicantSourceDialog(false)}
        onSave={handleEditApplicantSourceSave}
        applicantSource={selectedApplicantSource}
      />
    </>
  );
}

export default ApplicantSource;
