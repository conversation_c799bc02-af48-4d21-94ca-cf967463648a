import React, { useState } from 'react';
import { Box, Tabs, Tab } from '@mui/material';
import MainCard from 'components/MainCard';
import ApplicantSourceCategories from './ApplicantSourceCategories';
import ApplicantSource from './ApplicantSources';

function ApplicantSources() {
  const [selectedTab, setSelectedTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  return (
    <MainCard sx={{ borderRadius: 0, backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 0.2 }}>
      <Tabs
        value={selectedTab}
        onChange={handleTabChange}
        aria-label="interview settings tabs"
        sx={{
          mt: -2,
          ml: -2,
          alignItems: 'flex-start',
          justifyContent: 'flex-start',
          borderBottom: 0.2,
          borderColor: 'divider'
        }}
      >
        <Tab label="Applicant Source Categories" />
        <Tab label="Applicant Sources" />
      </Tabs>
      <Box sx={{  display: selectedTab === 0 ? 'block' : 'none' }}>
        <ApplicantSourceCategories />
      </Box>
      <Box sx={{  display: selectedTab === 1 ? 'block' : 'none' }}>
        <ApplicantSource />
      </Box>
    </MainCard>
  );
}

export default ApplicantSources;
