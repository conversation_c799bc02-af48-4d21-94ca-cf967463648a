import React, { useState } from 'react';
import { <PERSON>po<PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddApplicantFunctionDialog from './AddApplicantFunctionDialog';
import EditApplicantFunctionDialog from './EditApplicantFunctionDialog';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';

function ApplicantFunctions() {
  const [applicantFunctionsDetails, setApplicantFunctionsDetails] = useState([
    {
      id: 1,
      function_name: 'Recruitment',
      function_code: 'REC',
      created_by: 'Admin',
      created_on: '2023-01-01',
      modified_by: 'Admin',
      modified_on: '2023-01-01',
      status: 'Active'
    },
    {
      id: 2,
      function_name: 'Onboarding',
      function_code: 'ONB',
      created_by: 'Admin',
      created_on: '2023-01-05',
      modified_by: 'Admin',
      modified_on: '2023-01-05',
      status: 'Active'
    }
  ]);

  const { control } = useForm();

  const [openAddApplicantFunctionDialog, setOpenAddApplicantFunctionDialog] = useState(false);
  const [openEditApplicantFunctionDialog, setOpenEditApplicantFunctionDialog] = useState(false);
  const [selectedApplicantFunction, setSelectedApplicantFunction] = useState(null);

  const handleAddApplicantFunctionDialogOpen = () => {
    setOpenAddApplicantFunctionDialog(true);
  };

  const handleAddApplicantFunctionDialogClose = () => {
    setOpenAddApplicantFunctionDialog(false);
  };

  const handleAddApplicantFunctionSave = (data) => {
    setApplicantFunctionsDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'User',
        modified_on: new Date().toISOString().slice(0, 10),
        status: 'Active'
      }
    ]);
    handleAddApplicantFunctionDialogClose();
  };

  const handleEditApplicantFunctionSave = (updatedFunction) => {
    setApplicantFunctionsDetails((prev) => prev.map((func) => (func.id === updatedFunction.id ? updatedFunction : func)));
    setOpenEditApplicantFunctionDialog(false);
    setSelectedApplicantFunction(null);
  };

  const ApplicantFunctionActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedApplicantFunction(params.row);
      setOpenEditApplicantFunctionDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete applicant function:', params.row);
      setApplicantFunctionsDetails((prev) => prev.filter((func) => func.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const applicantFunctionColumns = [
    {
      field: 'function_name',
      headerName: 'APPLICANT FUNCTION NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    // {
    //   field: 'function_code',
    //   headerName: 'FUNCTION CODE',
    //   flex: 1,
    //   minWidth: 120,
    //   renderCell: (params) => (
    //     <Tooltip title={params.value}>
    //       <Typography variant="body2">{params.value}</Typography>
    //     </Tooltip>
    //   )
    // },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_on',
      headerName: 'CREATED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <ApplicantFunctionActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <>
      <CustomCardHeader
        secondary={
        <Box display="flex" alignItems="center" gap={1}>
          <Button variant="outlined" size="small">
            Activities
          </Button>
          <Button variant="contained" size="small" color="primary" onClick={handleAddApplicantFunctionDialogOpen}>
            + Add
          </Button>
        </Box>
        }
      />
      <Divider sx={{ mb: 1 }} />
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={applicantFunctionsDetails}
              columns={applicantFunctionColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={applicantFunctionsDetails.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddApplicantFunctionDialog
        open={openAddApplicantFunctionDialog}
        onClose={handleAddApplicantFunctionDialogClose}
        onSave={handleAddApplicantFunctionSave}
      />
      <EditApplicantFunctionDialog
        open={openEditApplicantFunctionDialog}
        onClose={() => setOpenEditApplicantFunctionDialog(false)}
        onSave={handleEditApplicantFunctionSave}
        applicantFunction={selectedApplicantFunction}
      />
    </>
  );
}

export default ApplicantFunctions;