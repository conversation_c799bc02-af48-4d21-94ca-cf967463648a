import React, { useEffect } from 'react';
import { Drawer, Typography, Box, Divider, IconButton, Button, Grid, Stack } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm } from 'react-hook-form';
import CustomName<PERSON>ield from 'components/custom-components/CustomNameField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';

function EditApplicantTechnologyDialog({ open, onClose, onSave, applicantTechnology }) {
  const { control, handleSubmit, reset, setValue } = useForm();

  useEffect(() => {
    if (open && applicantTechnology) {
      setValue('technology_name', applicantTechnology.technology_name);
    }
  }, [open, applicantTechnology, setValue]);

  const handleSave = (data) => {
    onSave({ ...applicantTechnology, ...data });
    reset();
  };

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box sx={{ width: { xs: '100%', sm: 400 }, height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Sticky Header */}
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">Edit Applicant Technology</Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <Divider />

        {/* Form */}
        <Box component="form" onSubmit={handleSubmit(handleSave)} sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="technology_name">Applicant Technology</CustomInputLabel>
                <CustomNameField name="technology_name" control={control} placeholder="Enter Technology Name"
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)'}} />
              </Stack>
            </Grid>
          </Grid>
        </Box>

        {/* Footer */}
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            onClick={() => {
              reset();
              onClose();
            }}
            size="small"
            variant="outlined"
          >
            Cancel
          </Button>
          <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(handleSave)}>
            Save
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
}

export default EditApplicantTechnologyDialog; 