import React, { useState } from 'react';
import { <PERSON>po<PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Custom<PERSON>ameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddApplicantTechnologyDialog from './AddApplicantTechnologyDialog';
import EditApplicantTechnologyDialog from './EditApplicantTechnologyDialog';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';

function ApplicantTechnologies() {
  const [applicantTechnologiesDetails, setApplicantTechnologiesDetails] = useState([
    {
      id: 1,
      technology_name: 'React',
      created_by: 'Admin',
      created_on: '2023-01-01',
      modified_by: 'Admin',
      modified_on: '2023-01-01',
      status: 'Active'
    },
    {
      id: 2,
      technology_name: 'Node.js',
      created_by: 'Admin',
      created_on: '2023-01-05',
      modified_by: 'Admin',
      modified_on: '2023-01-05',
      status: 'Active'
    }
  ]);

  const { control } = useForm();

  const [openAddApplicantTechnologyDialog, setOpenAddApplicantTechnologyDialog] = useState(false);
  const [openEditApplicantTechnologyDialog, setOpenEditApplicantTechnologyDialog] = useState(false);
  const [selectedApplicantTechnology, setSelectedApplicantTechnology] = useState(null);

  const handleAddApplicantTechnologyDialogOpen = () => {
    setOpenAddApplicantTechnologyDialog(true);
  };

  const handleAddApplicantTechnologyDialogClose = () => {
    setOpenAddApplicantTechnologyDialog(false);
  };

  const handleAddApplicantTechnologySave = (data) => {
    setApplicantTechnologiesDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'User',
        modified_on: new Date().toISOString().slice(0, 10),
        status: 'Active'
      }
    ]);
    handleAddApplicantTechnologyDialogClose();
  };

  const handleEditApplicantTechnologySave = (updatedTechnology) => {
    setApplicantTechnologiesDetails((prev) => prev.map((tech) => (tech.id === updatedTechnology.id ? updatedTechnology : tech)));
    setOpenEditApplicantTechnologyDialog(false);
    setSelectedApplicantTechnology(null);
  };

  const ApplicantTechnologyActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedApplicantTechnology(params.row);
      setOpenEditApplicantTechnologyDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete applicant technology:', params.row);
      setApplicantTechnologiesDetails((prev) => prev.filter((tech) => tech.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const applicantTechnologyColumns = [
    {
      field: 'technology_name',
      headerName: 'NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_on',
      headerName: 'CREATED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <ApplicantTechnologyActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <>
      <CustomCardHeader
        secondary={
        <Box display="flex" alignItems="center" gap={1}>
          <Button variant="outlined" size="small">
            Activities
          </Button>
          <Button variant="contained" size="small" color="primary" onClick={handleAddApplicantTechnologyDialogOpen}>
            + Add
          </Button>
        </Box>
        }
      />
      <Divider sx={{ mb: 1 }} />
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={applicantTechnologiesDetails}
              columns={applicantTechnologyColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={applicantTechnologiesDetails.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddApplicantTechnologyDialog
        open={openAddApplicantTechnologyDialog}
        onClose={handleAddApplicantTechnologyDialogClose}
        onSave={handleAddApplicantTechnologySave}
      />
      <EditApplicantTechnologyDialog
        open={openEditApplicantTechnologyDialog}
        onClose={() => setOpenEditApplicantTechnologyDialog(false)}
        onSave={handleEditApplicantTechnologySave}
        applicantTechnology={selectedApplicantTechnology}
      />
    </>
  );
}

export default ApplicantTechnologies;