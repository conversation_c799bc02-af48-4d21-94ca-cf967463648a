import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Toolt<PERSON>, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Custom<PERSON><PERSON>Field from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddClarenceTypeDialog from './AddClarenceTypeDialog';
import EditClarenceTypeDialog from './EditClarenceTypeDialog';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';

function ClarenceType() {
  const [clarenceTypeDetails, setClarenceTypeDetails] = useState([
    {
      id: 1,
      clearance_type: 'Secret',
      created_by: 'Admin',
      created_on: '2023-01-01',
      modified_by: 'Admin',
      modified_on: '2023-01-01',
      status: 'Active'
    },
    {
      id: 2,
      clearance_type: 'Top Secret',
      created_by: 'Admin',
      created_on: '2023-01-05',
      modified_by: 'Admin',
      modified_on: '2023-01-05',
      status: 'Active'
    }
  ]);

  const { control } = useForm();

  const [openAddClarenceTypeDialog, setOpenAddClarenceTypeDialog] = useState(false);
  const [openEditClarenceTypeDialog, setOpenEditClarenceTypeDialog] = useState(false);
  const [selectedClarenceType, setSelectedClarenceType] = useState(null);

  const handleAddClarenceTypeDialogOpen = () => {
    setOpenAddClarenceTypeDialog(true);
  };

  const handleAddClarenceTypeDialogClose = () => {
    setOpenAddClarenceTypeDialog(false);
  };

  const handleAddClarenceTypeSave = (data) => {
    setClarenceTypeDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'User',
        modified_on: new Date().toISOString().slice(0, 10),
        status: 'Active'
      }
    ]);
    handleAddClarenceTypeDialogClose();
  };

  const handleEditClarenceTypeSave = (updatedType) => {
    setClarenceTypeDetails((prev) => prev.map((type) => (type.id === updatedType.id ? updatedType : type)));
    setOpenEditClarenceTypeDialog(false);
    setSelectedClarenceType(null);
  };

  const ClarenceTypeActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedClarenceType(params.row);
      setOpenEditClarenceTypeDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete clearance type:', params.row);
      setClarenceTypeDetails((prev) => prev.filter((type) => type.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const clarenceTypeColumns = [
    {
      field: 'clearance_type',
      headerName: 'CLEARANCE TYPE',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_on',
      headerName: 'CREATED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <ClarenceTypeActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <>
      <CustomCardHeader
        secondary={
        <Box display="flex" alignItems="center" gap={1}>
          <Button variant="outlined" size="small">
            Activities
          </Button>
          <Button variant="contained" size="small" color="primary" onClick={handleAddClarenceTypeDialogOpen}>
            + Add
          </Button>
        </Box>
        }
      />
      <Divider sx={{ mb: 1 }} />
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={clarenceTypeDetails}
              columns={clarenceTypeColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={clarenceTypeDetails.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddClarenceTypeDialog
        open={openAddClarenceTypeDialog}
        onClose={handleAddClarenceTypeDialogClose}
        onSave={handleAddClarenceTypeSave}
      />
      <EditClarenceTypeDialog
        open={openEditClarenceTypeDialog}
        onClose={() => setOpenEditClarenceTypeDialog(false)}
        onSave={handleEditClarenceTypeSave}
        clarenceType={selectedClarenceType}
      />
    </>
  );
}

export default ClarenceType;