import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddDegreeDialog from './AddDegreeDialog';
import EditDegreeDialog from './EditDegreeDialog';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';

function Degrees() {
  const [degreeDetails, setDegreeDetails] = useState([
    {
      id: 1,
      degree_name: 'Bachelor of Science',
      specialization: 'Computer Science',
      created_by: 'Admin',
      created_on: '2023-01-01',
      modified_by: 'Admin',
      modified_on: '2023-01-01',
      status: 'Active'
    },
    {
      id: 2,
      degree_name: 'Master of Arts',
      specialization: 'History',
      created_by: 'Admin',
      created_on: '2023-01-05',
      modified_by: 'Admin',
      modified_on: '2023-01-05',
      status: 'Active'
    }
  ]);

  const { control } = useForm();

  const [openAddDegreeDialog, setOpenAddDegreeDialog] = useState(false);
  const [openEditDegreeDialog, setOpenEditDegreeDialog] = useState(false);
  const [selectedDegree, setSelectedDegree] = useState(null);

  const handleAddDegreeDialogOpen = () => {
    setOpenAddDegreeDialog(true);
  };

  const handleAddDegreeDialogClose = () => {
    setOpenAddDegreeDialog(false);
  };

  const handleAddDegreeSave = (data) => {
    setDegreeDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'User',
        modified_on: new Date().toISOString().slice(0, 10),
        status: 'Active'
      }
    ]);
    handleAddDegreeDialogClose();
  };

  const handleEditDegreeSave = (updatedDegree) => {
    setDegreeDetails((prev) => prev.map((deg) => (deg.id === updatedDegree.id ? updatedDegree : deg)));
    setOpenEditDegreeDialog(false);
    setSelectedDegree(null);
  };

  const DegreeActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedDegree(params.row);
      setOpenEditDegreeDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete degree:', params.row);
      setDegreeDetails((prev) => prev.filter((deg) => deg.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const degreeColumns = [
    {
      field: 'degree_name',
      headerName: 'DEGREE NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'specialization',
      headerName: 'SPECIALIZATION',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_on',
      headerName: 'CREATED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <DegreeActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <>
      <CustomCardHeader
        secondary={
        <Box display="flex" alignItems="center" gap={1}>
          <Button variant="outlined" size="small">
            Activities
          </Button>
          <Button variant="contained" size="small" color="primary" onClick={handleAddDegreeDialogOpen}>
            + Add
          </Button>
        </Box>
        }
      />
      <Divider sx={{ mb: 1 }} />
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={degreeDetails}
              columns={degreeColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={degreeDetails.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddDegreeDialog
        open={openAddDegreeDialog}
        onClose={handleAddDegreeDialogClose}
        onSave={handleAddDegreeSave}
      />
      <EditDegreeDialog
        open={openEditDegreeDialog}
        onClose={() => setOpenEditDegreeDialog(false)}
        onSave={handleEditDegreeSave}
        degree={selectedDegree}
      />
    </>
  );
}

export default Degrees;
