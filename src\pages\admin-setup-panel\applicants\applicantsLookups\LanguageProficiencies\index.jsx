import React, { useState } from 'react';
import { <PERSON>po<PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddLanguageProficiencyDialog from './AddLanguageProficiencyDialog';
import EditLanguageProficiencyDialog from './EditLanguageProficiencyDialog';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';

function LanguageProficiencies() {
  const [languageProficiencyDetails, setLanguageProficiencyDetails] = useState([
    {
      id: 1,
      language: 'English',
      proficiency_level: 'Native',
      created_by: 'Admin',
      created_on: '2023-01-01',
      modified_by: 'Admin',
      modified_on: '2023-01-01',
      status: 'Active'
    },
    {
      id: 2,
      language: 'Spanish',
      proficiency_level: 'Fluent',
      created_by: 'Admin',
      created_on: '2023-01-05',
      modified_by: 'Admin',
      modified_on: '2023-01-05',
      status: 'Active'
    }
  ]);

  const { control } = useForm();

  const [openAddLanguageProficiencyDialog, setOpenAddLanguageProficiencyDialog] = useState(false);
  const [openEditLanguageProficiencyDialog, setOpenEditLanguageProficiencyDialog] = useState(false);
  const [selectedLanguageProficiency, setSelectedLanguageProficiency] = useState(null);

  const handleAddLanguageProficiencyDialogOpen = () => {
    setOpenAddLanguageProficiencyDialog(true);
  };

  const handleAddLanguageProficiencyDialogClose = () => {
    setOpenAddLanguageProficiencyDialog(false);
  };

  const handleAddLanguageProficiencySave = (data) => {
    setLanguageProficiencyDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'User',
        modified_on: new Date().toISOString().slice(0, 10),
        status: 'Active'
      }
    ]);
    handleAddLanguageProficiencyDialogClose();
  };

  const handleEditLanguageProficiencySave = (updatedProficiency) => {
    setLanguageProficiencyDetails((prev) => prev.map((prof) => (prof.id === updatedProficiency.id ? updatedProficiency : prof)));
    setOpenEditLanguageProficiencyDialog(false);
    setSelectedLanguageProficiency(null);
  };

  const LanguageProficiencyActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedLanguageProficiency(params.row);
      setOpenEditLanguageProficiencyDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete language proficiency:', params.row);
      setLanguageProficiencyDetails((prev) => prev.filter((prof) => prof.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const languageProficiencyColumns = [
    {
      field: 'language',
      headerName: 'LANGUAGE',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'proficiency_level',
      headerName: 'PROFICIENCY LEVEL',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_on',
      headerName: 'CREATED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <LanguageProficiencyActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <>
      <CustomCardHeader
        secondary={
        <Box display="flex" alignItems="center" gap={1}>
          <Button variant="outlined" size="small">
            Activities
          </Button>
          <Button variant="contained" size="small" color="primary" onClick={handleAddLanguageProficiencyDialogOpen}>
            + Add
          </Button>
        </Box>
        }
      />
      <Divider sx={{ mb: 1 }} />
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={languageProficiencyDetails}
              columns={languageProficiencyColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={languageProficiencyDetails.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddLanguageProficiencyDialog
        open={openAddLanguageProficiencyDialog}
        onClose={handleAddLanguageProficiencyDialogClose}
        onSave={handleAddLanguageProficiencySave}
      />
      <EditLanguageProficiencyDialog
        open={openEditLanguageProficiencyDialog}
        onClose={() => setOpenEditLanguageProficiencyDialog(false)}
        onSave={handleEditLanguageProficiencySave}
        languageProficiency={selectedLanguageProficiency}
      />
    </>
  );
}

export default LanguageProficiencies; 