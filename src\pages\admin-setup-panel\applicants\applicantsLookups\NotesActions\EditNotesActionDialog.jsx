import React, { useEffect } from 'react';
import { <PERSON>er, Typo<PERSON>, Box, Divider, IconButton, Button, Grid, Stack, Checkbox, FormControlLabel, TextField } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm, Controller } from 'react-hook-form';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';

function EditNotesActionDialog({ open, onClose, onSave, notesAction }) {
  const { control, handleSubmit, reset, setValue, watch } = useForm({
    defaultValues: {
      action_name: '',
      applicable_to_applicant_talentbench: false,
      applicable_to_client_vendor: false,
      default_for_applicant_talentbench: false,
      default_for_client_vendor: false,
      description: ''
    }
  });

  const applicableToApplicantTalentbench = watch('applicable_to_applicant_talentbench');
  const applicableToClientVendor = watch('applicable_to_client_vendor');

  useEffect(() => {
    if (open && notesAction) {
      setValue('action_name', notesAction.note_action_name);
      setValue('applicable_to_applicant_talentbench', notesAction.applicable_to.includes('Applicants'));
      setValue('applicable_to_client_vendor', notesAction.applicable_to.includes('Job Postings'));
      setValue('default_for_applicant_talentbench', notesAction.default_for_applicant_talentbench || false);
      setValue('default_for_client_vendor', notesAction.default_for_client_vendor || false);
      setValue('description', notesAction.description || '');
    }
  }, [open, notesAction, setValue]);

  const handleSave = (data) => {
    // Combine checkbox values into a single string for applicable_to, or handle as needed for API
    const applicable_to_values = [];
    if (data.applicable_to_applicant_talentbench) {
      applicable_to_values.push('Applicants');
    }
    if (data.applicable_to_client_vendor) {
      applicable_to_values.push('Job Postings'); // Assuming 'Job Postings' maps to Client & Vendor for simplicity based on previous data
    }
    onSave({ 
      ...notesAction, 
      ...data, 
      applicable_to: applicable_to_values.join(', '),
      default_for_applicant_talentbench: data.default_for_applicant_talentbench,
      default_for_client_vendor: data.default_for_client_vendor,
      description: data.description
    });
    reset();
  };

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box sx={{ width: { xs: 300, sm: 400 }, height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Sticky Header */}
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">Edit Note Action</Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <Divider />

        {/* Form */}
        <Box component="form" onSubmit={handleSubmit(handleSave)} sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="action_name">Action Name</CustomInputLabel>
                <CustomNameField name="action_name" control={control} placeholder="Enter Action Name"
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)'}} />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <Typography variant="subtitle1" sx={{ mb: 1 }}>Applicable To:</Typography>
                <Controller
                  name="applicable_to_applicant_talentbench"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={<Checkbox {...field} checked={field.value} name="applicable_to_applicant_talentbench" />}
                      label="Applicant & Talentbench"
                    />
                  )}
                />
                <Controller
                  name="applicable_to_client_vendor"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={<Checkbox {...field} checked={field.value} name="applicable_to_client_vendor" />}
                      label="Client & Vendor"
                    />
                  )}
                />
              </Stack>
            </Grid>
            {applicableToApplicantTalentbench && (
              <>
                <Grid item xs={12}>
                  <Stack spacing={1}>
                    <Controller
                      name="default_for_applicant_talentbench"
                      control={control}
                      render={({ field }) => (
                        <FormControlLabel
                          control={<Checkbox {...field} checked={field.value} />}
                          label="Default for Applicant & TalentBench"
                        />
                      )}
                    />
                  </Stack>
                </Grid>
                <Grid item xs={12}>
                  <Stack spacing={1}>
                    <CustomInputLabel htmlFor="description">Description</CustomInputLabel>
                    <Controller
                      name="description"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          multiline
                          rows={4}
                          placeholder="Description will auto populate in notes section while creating notes in Applicants & Talent Bench"
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)'}}
                        />
                      )}
                    />
                  </Stack>
                </Grid>
              </>
            )}
            {applicableToClientVendor && (
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <Controller
                    name="default_for_client_vendor"
                    control={control}
                    render={({ field }) => (
                      <FormControlLabel
                        control={<Checkbox {...field} checked={field.value} />}
                        label="Default for Client & Vendor"
                      />
                    )}
                  />
                </Stack>
              </Grid>
            )}
          </Grid>
        </Box>

        {/* Footer */}
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            onClick={() => {
              reset();
              onClose();
            }}
            size="small"
            variant="outlined"
          >
            Cancel
          </Button>
          <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(handleSave)}>
            Save
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
}

export default EditNotesActionDialog; 