import React, { useState } from 'react';
import { Typo<PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddNotesActionDialog from './AddNotesActionDialog';
import EditNotesActionDialog from './EditNotesActionDialog';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';

function NotesActions() {
  const [notesActionDetails, setNotesActionDetails] = useState([
    {
      id: 1,
      action_name: 'Follow Up',
      applicable_to: 'Applicants',
      created_by: 'Admin',
      created_on: '2023-01-01',
      modified_by: 'Admin',
      modified_on: '2023-01-01',
      status: 'Active'
    },
    {
      id: 2,
      action_name: 'Send Email',
      applicable_to: 'Job Postings',
      created_by: 'Admin',
      created_on: '2023-01-05',
      modified_by: 'Admin',
      modified_on: '2023-01-05',
      status: 'Active'
    }
  ]);

  const { control } = useForm();

  const [openAddNotesActionDialog, setOpenAddNotesActionDialog] = useState(false);
  const [openEditNotesActionDialog, setOpenEditNotesActionDialog] = useState(false);
  const [selectedNotesAction, setSelectedNotesAction] = useState(null);

  const handleAddNotesActionDialogOpen = () => {
    setOpenAddNotesActionDialog(true);
  };

  const handleAddNotesActionDialogClose = () => {
    setOpenAddNotesActionDialog(false);
  };

  const handleAddNotesActionSave = (data) => {
    const applicable_to_values = [];
    if (data.applicable_to_applicant_talentbench) {
      applicable_to_values.push('Applicants');
    }
    if (data.applicable_to_client_vendor) {
      applicable_to_values.push('Job Postings');
    }
    setNotesActionDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        action_name: data.action_name,
        applicable_to: applicable_to_values.join(', '),
        created_by: 'User',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'User',
        modified_on: new Date().toISOString().slice(0, 10),
        status: 'Active'
      }
    ]);
    handleAddNotesActionDialogClose();
  };

  const handleEditNotesActionSave = (updatedAction) => {
    setNotesActionDetails((prev) => prev.map((action) => (action.id === updatedAction.id ? updatedAction : action)));
    setOpenEditNotesActionDialog(false);
    setSelectedNotesAction(null);
  };

  const NotesActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedNotesAction(params.row);
      setOpenEditNotesActionDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete note action:', params.row);
      setNotesActionDetails((prev) => prev.filter((action) => action.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const notesActionColumns = [
    {
      field: 'action_name',
      headerName: 'Name',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'applicable_to',
      headerName: 'Applicable To',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'Created By',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'Modified By',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'Last Modified',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <NotesActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <>
      <CustomCardHeader
        secondary={
        <Box display="flex" alignItems="center" gap={1}>
          <Button variant="outlined" size="small">
            Activities
          </Button>
          <Button variant="contained" size="small" color="primary" onClick={handleAddNotesActionDialogOpen}>
            + Add
          </Button>
        </Box>
        }
      />
      <Divider sx={{ mb: 1 }} />
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={notesActionDetails}
              columns={notesActionColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={notesActionDetails.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddNotesActionDialog
        open={openAddNotesActionDialog}
        onClose={handleAddNotesActionDialogClose}
        onSave={handleAddNotesActionSave}
      />
      <EditNotesActionDialog
        open={openEditNotesActionDialog}
        onClose={() => setOpenEditNotesActionDialog(false)}
        onSave={handleEditNotesActionSave}
        notesAction={selectedNotesAction}
      />
    </>
  );
}

export default NotesActions;