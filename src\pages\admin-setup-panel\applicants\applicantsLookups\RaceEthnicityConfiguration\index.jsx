import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddRaceEthnicityConfigurationDialog from './AddRaceEthnicityConfigurationDialog';
import EditRaceEthnicityConfigurationDialog from './EditRaceEthnicityConfigurationDialog';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';

function RaceEthnicityConfiguration() {
  const [raceEthnicityDetails, setRaceEthnicityDetails] = useState([
    {
      id: 1,
      race_ethnicity_name: 'Asian',
      created_by: 'Admin',
      created_on: '2023-01-01',
      modified_by: 'Admin',
      modified_on: '2023-01-01',
      status: 'Active'
    },
    {
      id: 2,
      race_ethnicity_name: 'White',
      created_by: 'Admin',
      created_on: '2023-01-05',
      modified_by: 'Admin',
      modified_on: '2023-01-05',
      status: 'Active'
    }
  ]);

  const { control } = useForm();

  const [openAddRaceEthnicityConfigurationDialog, setOpenAddRaceEthnicityConfigurationDialog] = useState(false);
  const [openEditRaceEthnicityConfigurationDialog, setOpenEditRaceEthnicityConfigurationDialog] = useState(false);
  const [selectedRaceEthnicity, setSelectedRaceEthnicity] = useState(null);

  const handleAddRaceEthnicityConfigurationDialogOpen = () => {
    setOpenAddRaceEthnicityConfigurationDialog(true);
  };

  const handleAddRaceEthnicityConfigurationDialogClose = () => {
    setOpenAddRaceEthnicityConfigurationDialog(false);
  };

  const handleAddRaceEthnicityConfigurationSave = (data) => {
    setRaceEthnicityDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'User',
        modified_on: new Date().toISOString().slice(0, 10),
        status: 'Active'
      }
    ]);
    handleAddRaceEthnicityConfigurationDialogClose();
  };

  const handleEditRaceEthnicityConfigurationSave = (updatedRaceEthnicity) => {
    setRaceEthnicityDetails((prev) => prev.map((re) => (re.id === updatedRaceEthnicity.id ? updatedRaceEthnicity : re)));
    setOpenEditRaceEthnicityConfigurationDialog(false);
    setSelectedRaceEthnicity(null);
  };

  const RaceEthnicityActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedRaceEthnicity(params.row);
      setOpenEditRaceEthnicityConfigurationDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete race/ethnicity configuration:', params.row);
      setRaceEthnicityDetails((prev) => prev.filter((re) => re.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const raceEthnicityColumns = [
    {
      field: 'race_ethnicity_name',
      headerName: 'RACE/ETHNICITY NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_on',
      headerName: 'CREATED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <RaceEthnicityActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <>
      <CustomCardHeader
        secondary={
        <Box display="flex" alignItems="center" gap={1}>
          <Button variant="outlined" size="small">
            Activities
          </Button>
          <Button variant="contained" size="small" color="primary" onClick={handleAddRaceEthnicityConfigurationDialogOpen}>
            + Add
          </Button>
        </Box>
        }
      />
      <Divider sx={{ mb: 1 }} />
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={raceEthnicityDetails}
              columns={raceEthnicityColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={raceEthnicityDetails.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddRaceEthnicityConfigurationDialog
        open={openAddRaceEthnicityConfigurationDialog}
        onClose={handleAddRaceEthnicityConfigurationDialogClose}
        onSave={handleAddRaceEthnicityConfigurationSave}
      />
      <EditRaceEthnicityConfigurationDialog
        open={openEditRaceEthnicityConfigurationDialog}
        onClose={() => setOpenEditRaceEthnicityConfigurationDialog(false)}
        onSave={handleEditRaceEthnicityConfigurationSave}
        raceEthnicity={selectedRaceEthnicity}
      />
    </>
  );
}

export default RaceEthnicityConfiguration; 