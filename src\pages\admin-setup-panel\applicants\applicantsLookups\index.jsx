import React, { useState } from 'react';
import { Box, Typography, Tabs, Tab, Divider } from '@mui/material';
import MainCard from 'components/MainCard';
import ApplicantFunctions from './ApplicantFunctions';
import ApplicantTechnologies from './ApplicantTechnologies';
import ClarenceType from './ClarenceType';
import Degrees from './Degrees';
import LanguageProficiencies from './LanguageProficiencies';
import NotesActions from './NotesActions';
import RaceEthnicityConfiguration from './RaceEthnicityConfiguration';

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`
  };
}

function ApplicantsLookups() {
  const [selectedTab, setSelectedTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  return (
    <MainCard sx={{ borderRadius: 0, backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 0.2 }}>
      <Box>
        <Tabs 
          value={selectedTab} 
          onChange={handleTabChange} variant="scrollable" scrollButtons="auto"
          aria-label="interview settings tabs"
          sx={{
            mt: -2,
            ml: -2,
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            borderBottom: 0.2,
            borderColor: 'divider'
          }}
        >
          <Tab label="Applicant Functions" {...a11yProps(0)} />
          <Tab label="Applicant Technologies" {...a11yProps(1)} />
          <Tab label="Clarence Type" {...a11yProps(2)} />
          <Tab label="Degrees" {...a11yProps(3)} />
          <Tab label="Language Proficiencies" {...a11yProps(4)} />
          <Tab label="Notes Actions" {...a11yProps(5)} />
          <Tab label="Race/ Ethnicity Configuration" {...a11yProps(6)} />
        </Tabs>
      </Box>
  
      <Box role="tabpanel" hidden={selectedTab !== 0}>
        {selectedTab === 0 && <ApplicantFunctions />}
      </Box>
      <Box role="tabpanel" hidden={selectedTab !== 1}>
        {selectedTab === 1 && <ApplicantTechnologies />}
      </Box>
      <Box role="tabpanel" hidden={selectedTab !== 2}>
        {selectedTab === 2 && <ClarenceType />}
      </Box>
      <Box role="tabpanel" hidden={selectedTab !== 3}>
        {selectedTab === 3 && <Degrees />}
      </Box>
      <Box role="tabpanel" hidden={selectedTab !== 4}>
        {selectedTab === 4 && <LanguageProficiencies />}
      </Box>
      <Box role="tabpanel" hidden={selectedTab !== 5}>
        {selectedTab === 5 && <NotesActions />}
      </Box>
      <Box role="tabpanel" hidden={selectedTab !== 6}>
        {selectedTab === 6 && <RaceEthnicityConfiguration />}
      </Box>
    </MainCard>
  );
}

export default ApplicantsLookups;
