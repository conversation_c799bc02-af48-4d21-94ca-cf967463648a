import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Custom<PERSON>ameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
// Import dialogs - these will be created in separate steps
import AddApplicationStatusCategoryDialog from './AddApplicationStatusCategoryDialog';
import EditApplicationStatusCategoryDialog from './EditApplicationStatusCategoryDialog';

function ApplicationsStatusesCategories() {
  const [applicationStatusCategoryDetails, setApplicationStatusCategoryDetails] = useState([
    {
      id: 1,
      name: 'Applied',
      created_by: 'Admin',
      created_on: '2023-01-01',
      modified_by: 'Admin',
      modified_on: '2023-01-01',
    },
    {
      id: 2,
      name: 'Interview',
      created_by: 'Admin',
      created_on: '2023-01-05',
      modified_by: 'Admin',
      modified_on: '2023-01-05',
    },
  ]);

  const { control: searchControl, watch: watchSearch } = useForm();
  const searchQuery = watchSearch('search', '');

  const [openAddCategoryDialog, setOpenAddCategoryDialog] = useState(false);
  const [openEditCategoryDialog, setOpenEditCategoryDialog] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);

  const handleAddCategoryDialogClose = () => {
    setOpenAddCategoryDialog(false);
  };

  const handleAddCategorySave = (data) => {
    setApplicationStatusCategoryDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'User',
        modified_on: new Date().toISOString().slice(0, 10),
      },
    ]);
    handleAddCategoryDialogClose();
  };

  const handleEditCategorySave = (updatedCategory) => {
    setApplicationStatusCategoryDetails((prev) =>
      prev.map((category) => (category.id === updatedCategory.id ? updatedCategory : category))
    );
    setOpenEditCategoryDialog(false);
    setSelectedCategory(null);
  };

  const CategoryActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedCategory(params.row);
      setOpenEditCategoryDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete category:', params.row);
      setApplicationStatusCategoryDetails((prev) => prev.filter((category) => category.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const applicationStatusCategoryColumns = [
    {
      field: 'name',
      headerName: 'NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      ),
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      ),
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      ),
    },
    {
      field: 'modified_on',
      headerName: 'LAST MODIFIED',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      ),
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <CategoryActionCell params={params} />,
    },
  ];

  const filteredCategories = applicationStatusCategoryDetails.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <>
      <CustomCardHeader
        secondary={
          <Box display="flex" alignItems="center" gap={1}>
            <Button variant="outlined" size="small">
              Activities
            </Button>
          </Box>
        }
      />
      <Divider sx={{ mb: 1 }} />
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 400, width: '100%' }}>
            <CustomDataGrid
              rows={filteredCategories}
              columns={applicationStatusCategoryColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={filteredCategories.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddApplicationStatusCategoryDialog
        open={openAddCategoryDialog}
        onClose={handleAddCategoryDialogClose}
        onSave={handleAddCategorySave}
      />
      <EditApplicationStatusCategoryDialog
        open={openEditCategoryDialog}
        onClose={() => setOpenEditCategoryDialog(false)}
        onSave={handleEditCategorySave}
        category={selectedCategory}
      />
    </>
  );
}

export default ApplicationsStatusesCategories;
