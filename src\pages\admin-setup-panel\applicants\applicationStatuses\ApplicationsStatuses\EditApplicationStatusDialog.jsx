import React, { useEffect, useState } from 'react';
import { <PERSON>po<PERSON>, <PERSON>, Divider, Button, Grid, Stack, FormControlLabel, Checkbox, IconButton } from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import Custom<PERSON><PERSON><PERSON>ield from 'components/custom-components/CustomNameField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import MainCard from 'components/MainCard';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useNavigate } from 'react-router-dom';

function EditApplicationStatus({ onClose, onSave, applicationStatus }) {
  const { control, handleSubmit, reset, setValue } = useForm();
  const [allSubmissionStatuses, setAllSubmissionStatuses] = useState(false);
  const [allNextStepStatuses, setAllNextStepStatuses] = useState(false);
  const navigate = useNavigate();

  const categoryOptions = [
    { value: 'Waiting for Evaluation', label: 'Waiting for Evaluation' },
    { value: 'Submitted', label: 'Submitted' },
    { value: 'Internal Select', label: 'Internal Select' },
    { value: 'Internal Reject', label: 'Internal Reject' },
    { value: 'Placed', label: 'Placed' },
    { value: 'Others', label: 'Others' },
    { value: 'Internal Interview', label: 'Internal Interview' },
  ];

  const submissionStatusOptions = [
    { value: 'Waiting for Evaluation', label: 'Waiting for Evaluation' },
    { value: 'Internal Interview', label: 'Internal Interview' },
    { value: 'Selected Internally', label: 'Selected Internally' },
    { value: 'Rejected Internally', label: 'Rejected Internally' },
    { value: 'Withdrawn', label: 'Withdrawn' },
    { value: 'Internal Disqualified', label: 'Internal Disqualified' },
    { value: 'Offer Sent', label: 'Offer Sent' },
  ];

  const nextStepOptions = [
    { value: 'Archived', label: 'Archived' },
    { value: 'Keep Hold', label: 'Keep Hold' },
    { value: 'Negotiation', label: 'Negotiation' },
    { value: 'Submitted', label: 'Submitted' },
    { value: 'Project Completed', label: 'Project Completed' },
    { value: 'Submitted To Client', label: 'Submitted To Client' },
    { value: 'Submitted To Vendor', label: 'Submitted To Vendor' },
    { value: 'Rejected By Client', label: 'Rejected By Client' },
    { value: 'Selected but no response from the client', label: 'Selected but no response from the client' },
    { value: 'Rejected after Client Selection', label: 'Rejected after Client Selection' },
    { value: 'Submitted to End Client', label: 'Submitted to End Client' },
    { value: 'Vendor Interview', label: 'Vendor Interview' },
    { value: 'Client Interview', label: 'Client Interview' },
  ];

  useEffect(() => {
    if (applicationStatus) {
      setValue('status_name', applicationStatus.name);
      setValue('category', applicationStatus.category);
      setValue('display_submission_status', applicationStatus.display_submission_status);
      setValue('display_next_step_status', applicationStatus.display_next_step_status);
      setValue('description', applicationStatus.description);
      setValue('document_required', applicationStatus.document_required);
      setValue('send_sms_to_applicant', applicationStatus.send_sms_to_applicant);
      setValue('add_to_no_submission_list', applicationStatus.add_to_no_submission_list);
      setValue('no_submission_list', applicationStatus.no_submission_list);
      setAllSubmissionStatuses(applicationStatus.display_submission_status === 'All Statuses');
      setAllNextStepStatuses(applicationStatus.display_next_step_status === 'All Statuses');
    }
  }, [applicationStatus, setValue]);

  const handleSave = (data) => {
    onSave({ ...applicationStatus, ...data });
    reset();
  };

  useEffect(() => {
    if (allSubmissionStatuses) {
      setValue('display_submission_status', 'All Statuses');
    }
  }, [allSubmissionStatuses, setValue]);

  useEffect(() => {
    if (allNextStepStatuses) {
      setValue('display_next_step_status', 'All Statuses');
    }
  }, [allNextStepStatuses, setValue]);

  return (
    <MainCard
      title="Edit Application Statuses"
      secondary={
        <Box display="flex" alignItems="center" gap={2}>
          <IconButton onClick={() => navigate('/admin-setup-panel/applicants/application-statuses?tab=1')} sx={{ color: 'text.secondary' }}>
          <ArrowBackIcon />
          </IconButton>
        </Box>
      }
      sx={{ borderRadius: 0, backgroundColor: 'white', mt: 0 }}
    >
      <Box component="form" onSubmit={handleSubmit(handleSave)} sx={{ p: { xs: 1, sm: 1 }, width: '100%' }}>
              <Grid container spacing={2} alignItems="center" sx={{ mt: 0}}>
              <Grid item xs={12} sm={3} lg={6} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
              <CustomInputLabel htmlFor="status_name">Status Name<span style={{ color: 'red' }}>*</span></CustomInputLabel>
              </Grid>
              <Grid item xs={12} sm={9} lg={6}>             
              <CustomNameField name="status_name" control={control} placeholder="Enter Status Name"
                        sx={{ backgroundColor: 'rgba(248, 249, 250, 1)'}}
                        rules={{ required: 'Status Name is required' }}
               />
              </Grid> 
              </Grid>
             
              <Grid container spacing={2} alignItems="center" sx={{ mt: 1 }}>
              <Grid item xs={12} sm={3} lg={6} sx={{ pt: { xs: 2, sm: '0 !important' } }}>            
              <CustomInputLabel htmlFor="category">Category<span style={{ color: 'red' }}>*</span></CustomInputLabel>
              </Grid>
              <Grid item xs={12} sm={9} lg={6}>
              <CustomDropdownField
                name="category"
                control={control}
                placeholder="Select Category"
                options={categoryOptions}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                rules={{ required: 'Category is required' }}
               />
              </Grid> 
              </Grid>
              <Grid container spacing={2} alignItems="center" sx={{ mt: 1 }}>
              <Grid item xs={12} sm={3} lg={6} sx={{ pt: { xs: 2, sm: '0 !important' } }}>           
              <CustomInputLabel htmlFor="display_submission_status">
                Display When Submission Status Is:
                <Typography variant="caption" display="block" sx={{ mt: 0.5, color: 'text.secondary' }}>
                  ( will be displayed when the current submission status is one of the selected statuses.)
                </Typography>
              </CustomInputLabel>
              </Grid>
              <Grid item xs={12} sm={9} lg={6}>
              <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, alignItems: { sm: 'center' }, gap: 1 }}>
                <CustomDropdownField
                  name="display_submission_status"
                  control={control}
                  placeholder="Select"
                  options={submissionStatusOptions}
                  disabled={allSubmissionStatuses}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)', flexGrow: 1 }}
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={allSubmissionStatuses}
                      onChange={(e) => setAllSubmissionStatuses(e.target.checked)}
                    />
                  }
                  label="All Statuses"
                  sx={{ ml: { xs: 0, sm: -1.25 } }}
                />
              </Box>
              </Grid> 
              </Grid>
              <Grid container spacing={2} alignItems="center" sx={{ mt: 1 }}>
              <Grid item xs={12} sm={3} lg={6} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
              <CustomInputLabel htmlFor="display_next_step_status">
                Display These Statuses As Next Step Options When the Submission Is
                <Typography variant="caption" display="block" sx={{ mt: 0.5, color: 'text.secondary' }}>
                  (When the submission status is , these statuses will be displayed in the dropdown
                  as potential next step statuses.)
                </Typography>
              </CustomInputLabel>
              </Grid>
              <Grid item xs={12} sm={9} lg={6}>
              <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, alignItems: { sm: 'center' }, gap: 1 }}>
                <CustomDropdownField
                  name="display_next_step_status"
                  control={control}
                  placeholder="Select"
                  options={nextStepOptions}
                  disabled={allNextStepStatuses}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)', flexGrow: 1 }
                  }
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={allNextStepStatuses}
                      onChange={(e) => setAllNextStepStatuses(e.target.checked)}
                    />
                  }
                  label="All Statuses"
                  sx={{ ml: { xs: 0, sm: -1.25 } }}
                />
              </Box>
          </Grid>
          </Grid>
          <Grid container spacing={2} alignItems="center" sx={{ mt: 1 }}>
          <Grid item xs={12} sm={3} lg={6} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
              <CustomInputLabel htmlFor="description">Description<span style={{ color: 'red' }}>*</span></CustomInputLabel>
              </Grid>
              <Grid item xs={12} sm={9} lg={6}>
              <CustomNameField
                name="description"
                control={control}
                placeholder="Enter Description"
                multiline
                rows={4}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                rules={{ required: 'Description is required' }}
              />
            </Grid>
          </Grid>
          <Grid container spacing={2} alignItems="center" sx={{ mt: 1 }}>
            <Grid item xs={12} sm={3} lg={6} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
              <Stack spacing={0.5}>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  Document Required
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                  (When checked, the user needs to upload a document while setting this submission status.)
                </Typography>
              </Stack>
            </Grid>
            <Grid item xs={12} sm={9} lg={6}>
              <Controller
                name="document_required"
                control={control}
                defaultValue={false}
                render={({ field }) => <Checkbox {...field} checked={field.value || false} sx={{ color: 'rgba(99, 114, 131, 1)' }} />}
              />
            </Grid>
          </Grid>
          <Grid container spacing={2} alignItems="center" sx={{ mt: 1 }}>
            <Grid item xs={12} sm={3} lg={6} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
              <Stack spacing={0.5}>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  Send SMS to Applicant
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                  (A message will be sent to the applicant when this submission status is selected.)
                </Typography>
              </Stack>
            </Grid>
            <Grid item xs={12} sm={9} lg={6}>
              <Controller
                name="send_sms_to_applicant"
                control={control}
                defaultValue={false}
                render={({ field }) => <Checkbox {...field} checked={field.value || false} sx={{ color: 'rgba(99, 114, 131, 1)' }} />}
              />
            </Grid>
          </Grid>
          <Grid container spacing={2} alignItems="center" sx={{ mt: 1 }}>
            <Grid item xs={12} sm={3} lg={6} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
              <Stack spacing={0.5}>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  Add This Status to the No Submission List
                </Typography>
              </Stack>
            </Grid>
            <Grid item xs={12} sm={9} lg={6}>
              <Controller
                name="add_to_no_submission_list"
                control={control}
                defaultValue={false}
                render={({ field }) => <Checkbox {...field} checked={field.value || false} sx={{ color: 'rgba(99, 114, 131, 1)' }} />}
              />
            </Grid>
          </Grid>
          <Grid container spacing={2} alignItems="center" sx={{ mt: 1 }}>
            <Grid item xs={12} sm={3} lg={6} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
              <CustomInputLabel htmlFor="no_submission_list">
                No Submission List
                <Typography variant="caption" display="block" sx={{ mt: 0.5, color: 'text.secondary' }}>
                  (Select the statuses in which users cannot complete submissions.)
                </Typography>
              </CustomInputLabel>
            </Grid>
            <Grid item xs={12} sm={9} lg={6}>
              <CustomDropdownField
                name="no_submission_list"
                control={control}
                placeholder="Select"
                options={submissionStatusOptions}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Grid>
          </Grid>
        </Box>

        {/* Footer */}
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            onClick={() => {
              reset();
              onClose();
            }}
            size="small"
            variant="outlined"
          >
            Cancel
          </Button>
          <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(handleSave)}>
            Save
          </Button>
        </Box>
    </MainCard>
  );
}

export default EditApplicationStatus;
