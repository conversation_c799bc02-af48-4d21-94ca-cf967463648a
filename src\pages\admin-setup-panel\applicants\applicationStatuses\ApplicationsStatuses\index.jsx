import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
// Import dialogs - these will be created in separate steps
import AddApplicationStatusDialog from './AddApplicationStatusDialog';
import EditApplicationStatusDialog from './EditApplicationStatusDialog';
import { useNavigate, useLocation } from 'react-router-dom';

function ApplicationsStatuses() {
  const [applicationStatusDetails, setApplicationStatusDetails] = useState([
    {
      id: 1,
      name: 'New Application',
      status_selection_restrictions: 'None',
      category: 'Applied',
      created_by: 'Admin',
      created_on: '2023-01-01',
      modified_by: 'Admin',
      modified_on: '2023-01-01',
    },
    {
      id: 2,
      name: 'Screening',
      status_selection_restrictions: 'Restrict to internal only',
      category: 'Interview',
      created_by: 'Admin',
      created_on: '2023-01-05',
      modified_by: 'Admin',
      modified_on: '2023-01-05',
    },
  ]);

  const { control: searchControl, watch: watchSearch } = useForm();
  const searchQuery = watchSearch('search', '');

  const navigate = useNavigate();
  const location = useLocation();
  const isAdd = location.pathname.endsWith('/add');
  const isEdit = location.pathname.endsWith('/edit');
  const selectedStatus = location.state?.applicationStatus || null;

  const handleAdd = () => {
    navigate('/admin-setup-panel/applicants/application-statuses/add');
  };

  const handleBackToList = () => {
    navigate('/admin-setup-panel/applicants/application-statuses');
  };

  const handleAddStatusSave = (data) => {
    setApplicationStatusDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        name: data.status_name,
        status_selection_restrictions: data.display_submission_status && data.display_submission_status !== 'All Statuses' ? data.display_submission_status : 'None',
        created_by: 'User',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'User',
        modified_on: new Date().toISOString().slice(0, 10),
      },
    ]);
    handleBackToList();
  };

  const handleEditStatusSave = (updatedStatus) => {
    setApplicationStatusDetails((prev) =>
      prev.map((status) =>
        status.id === updatedStatus.id
          ? {
              ...updatedStatus,
              name: updatedStatus.status_name,
              status_selection_restrictions: updatedStatus.display_submission_status && updatedStatus.display_submission_status !== 'All Statuses' ? updatedStatus.display_submission_status : 'None',
            }
          : status
      )
    );
    handleBackToList();
  };

  const StatusActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      navigate('/admin-setup-panel/applicants/application-statuses/edit', { state: { applicationStatus: params.row } });
      handleClose();
    };

    const handleDelete = () => {
      setApplicationStatusDetails((prev) => prev.filter((status) => status.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const applicationStatusColumns = [
    {
      field: 'name',
      headerName: 'NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      ),
    },
    {
      field: 'status_selection_restrictions',
      headerName: 'STATUS SELECTION RESTRICTIONS',
      flex: 1,
      minWidth: 200,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      ),
    },
    {
      field: 'category',
      headerName: 'CATEGORY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      ),
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      ),
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      ),
    },
    {
      field: 'modified_on',
      headerName: 'LAST MODIFIED',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      ),
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <StatusActionCell params={params} />,
    },
  ];

  const filteredStatuses = applicationStatusDetails.filter(status =>
    (status.name || '').toLowerCase().includes(searchQuery.toLowerCase())
  );

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  // Render different views based on route
  if (isAdd) {
    return <AddApplicationStatusDialog onClose={handleBackToList} onSave={handleAddStatusSave} />;
  }

  if (isEdit) {
    return <EditApplicationStatusDialog onClose={handleBackToList} onSave={handleEditStatusSave} applicationStatus={selectedStatus} />;
  }

  // Default list view
  return (
    <>
      <CustomCardHeader
        secondary={
          <Box display="flex" alignItems="center" gap={1}>
            <Button variant="outlined" size="small">
              Activities
            </Button>
            <Button variant="contained" size="small" color="primary" onClick={handleAdd}>
              + Add
            </Button>
          </Box>
        }
      />
      <Divider sx={{ mb: 1 }} />
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 400, width: '100%' }}>
            <CustomDataGrid
              rows={filteredStatuses}
              columns={applicationStatusColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={filteredStatuses.length}
            />
          </Box>
        </Grid>
      </Grid>
    </>
  );
}

export default ApplicationsStatuses;
