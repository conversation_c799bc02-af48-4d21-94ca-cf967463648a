import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Box, Tabs, Tab } from '@mui/material';
import MainCard from 'components/MainCard';
import ApplicationsStatusesCategories from './ApplicationsStatusesCategories';
import ApplicationsStatuses from './ApplicationsStatuses';

function ApplicationStatuses() {
  const location = useLocation();
  const navigate = useNavigate();
  const [selectedTab, setSelectedTab] = useState(0);

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const tab = searchParams.get('tab');
    if (tab && !isNaN(tab)) {
      const tabIndex = parseInt(tab, 10);
      setSelectedTab(tabIndex);
    } else {
      setSelectedTab(0);
    }
  }, [location.search]);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
    navigate(`/admin-setup-panel/applicants/application-statuses?tab=${newValue}`);
  };

  return (
    <MainCard sx={{ borderRadius: 0 }}>
      <Tabs
        value={selectedTab}
        onChange={handleTabChange}
        aria-label="talent bench lookups tabs"
        sx={{
          mt: -2,
          ml: -2,
          alignItems: 'flex-start',
          justifyContent: 'flex-start',
          borderBottom: 0.2,
          borderColor: 'divider'
        }}
      >
        <Tab label="Applications Statuses Categories" />
        <Tab label="Applications Statuses" />
      </Tabs>
      <Box sx={{ display: selectedTab === 0 ? 'block' : 'none' }}>
        <ApplicationsStatusesCategories />
      </Box>
      <Box sx={{ display: selectedTab === 1 ? 'block' : 'none' }}>
        <ApplicationsStatuses />
      </Box>
    </MainCard>
  );
}

export default ApplicationStatuses;
