import React, { useEffect, useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  Divider,
  IconButton,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Tabs,
  Tab,
  AppBar,
  Slide
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm } from 'react-hook-form';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import MainCard from 'components/MainCard';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

// Icons for navigation
import TuneIcon from '@mui/icons-material/Tune'; // Portal Details
import WebAssetIcon from '@mui/icons-material/WebAsset'; // Logo
import DnsIcon from '@mui/icons-material/Dns'; // DNS Masking
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered'; // Job Detail List & Sort Order
import ShareIcon from '@mui/icons-material/Share'; // Social Media
import FolderIcon from '@mui/icons-material/Folder'; // Submission Settings
import CodeIcon from '@mui/icons-material/Code'; // Embed Code
import SettingsIcon from '@mui/icons-material/Settings'; // Portal Settings

import PortalDetailsForm from './renderContent/PortalDetailsForm';
import LogoForm from './renderContent/LogoForm';
import DNSMaskingForm from './renderContent/DNSMaskingForm';
import JobDetailListSortOrderForm from './renderContent/JobDetailListSortOrderForm';
import SocialMediaForm from './renderContent/SocialMediaForm';
import SubmissionSettingsForm from './renderContent/SubmissionSettingsForm';
import EmbedCodeForm from './renderContent/EmbedCodeForm';
import PortalSettingsForm from './renderContent/PortalSettingsForm';

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function AddCareerSite({ onSave }) {
  const { control, handleSubmit, reset } = useForm();
  const [selectedSection, setSelectedSection] = useState('portal_details'); // Default to Portal Details
  const theme = useTheme();
  const isMobileOrTablet = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();

  const handleClose = () => {
    navigate('/admin-setup-panel/applicants/career-sites');
  };

  const handleSave = (data) => {
    onSave({ ...data, id: Date.now() });
    reset();
    handleClose();
  };

  const renderContent = () => {
    switch (selectedSection) {
      case 'portal_details':
        return <PortalDetailsForm control={control} />;
      case 'logo':
        return <LogoForm control={control} />;
      case 'dns_masking':
        return <DNSMaskingForm control={control} />;
      case 'job_detail_list_sort_order':
        return <JobDetailListSortOrderForm control={control} />;
      case 'social_media':
        return <SocialMediaForm control={control} />;
      case 'submission_settings':
        return <SubmissionSettingsForm control={control} />;
      case 'embed_code':
        return <EmbedCodeForm />;
      case 'portal_settings':
        return <PortalSettingsForm control={control} />;
      default:
        return null;
    }
  };

  const sectionMap = {
    'portal_details': 0,
    'logo': 1,
    'dns_masking': 2,
    'job_detail_list_sort_order': 3,
    'social_media': 4,
    'submission_settings': 5,
    'embed_code': 6,
    'portal_settings': 7,
  };

  const a11yProps = (index) => {
    return {
      id: `simple-tab-${index}`,
      'aria-controls': `simple-tabpanel-${index}`,
    };
  };

  const handleTabChange = (event, newValue) => {
    const sectionName = Object.keys(sectionMap).find(key => sectionMap[key] === newValue);
    setSelectedSection(sectionName);
  };

  return (
    <MainCard
      title="Add Career Site"
      secondary={
        <Box display="flex" alignItems="center" gap={2}>
          <IconButton onClick={handleClose} sx={{ color: 'text.secondary' }}>
            <ArrowBackIcon />
          </IconButton>
        </Box>
      }
      sx={{ borderRadius: 0, backgroundColor: 'white', mt: 1 }}
    >
      <Divider />
      <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, minHeight: 500 }}>
        {/* Left Navigation or Tabs */}
        {isMobileOrTablet ? (
          <AppBar position="static" color="default">
            <Tabs
              value={sectionMap[selectedSection]}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              variant="scrollable"
              scrollButtons="auto"
              aria-label="career site sections tabs"
            >
              <Tab label="Portal Details" {...a11yProps(0)} />
              <Tab label="Logo" {...a11yProps(1)} />
              <Tab label="DNS Masking" {...a11yProps(2)} />
              <Tab label="Job Detail List & Sort Order" {...a11yProps(3)} />
              <Tab label="Social Media" {...a11yProps(4)} />
              <Tab label="Submission Settings" {...a11yProps(5)} />
              <Tab label="Embed Code" {...a11yProps(6)} />
              <Tab label="Portal Settings" {...a11yProps(7)} />
            </Tabs>
          </AppBar>
        ) : (
          <Box sx={{ width: '250px', borderRight: '1px solid #e0e0e0', flexShrink: 0 }}>
            <List component="nav">
              <ListItem button selected={selectedSection === 'portal_details'} onClick={() => setSelectedSection('portal_details')}>
                <ListItemIcon>
                  <TuneIcon />
                </ListItemIcon>
                <ListItemText primary="Portal Details" />
              </ListItem>
              <ListItem button selected={selectedSection === 'logo'} onClick={() => setSelectedSection('logo')}>
                <ListItemIcon>
                  <WebAssetIcon />
                </ListItemIcon>
                <ListItemText primary="Logo" />
              </ListItem>
              <ListItem button selected={selectedSection === 'dns_masking'} onClick={() => setSelectedSection('dns_masking')}>
                <ListItemIcon>
                  <DnsIcon />
                </ListItemIcon>
                <ListItemText primary="DNS Masking" />
              </ListItem>
              <ListItem button selected={selectedSection === 'job_detail_list_sort_order'} onClick={() => setSelectedSection('job_detail_list_sort_order')}>
                <ListItemIcon>
                  <FormatListNumberedIcon />
                </ListItemIcon>
                <ListItemText primary="Job Detail List & Sort Order" />
              </ListItem>
              <ListItem button selected={selectedSection === 'social_media'} onClick={() => setSelectedSection('social_media')}>
                <ListItemIcon>
                  <ShareIcon />
                </ListItemIcon>
                <ListItemText primary="Social Media" />
              </ListItem>
              <ListItem button selected={selectedSection === 'submission_settings'} onClick={() => setSelectedSection('submission_settings')}>
                <ListItemIcon>
                  <FolderIcon />
                </ListItemIcon>
                <ListItemText primary="Submission Settings" />
              </ListItem>
              <ListItem button selected={selectedSection === 'embed_code'} onClick={() => setSelectedSection('embed_code')}>
                <ListItemIcon>
                  <CodeIcon />
                </ListItemIcon>
                <ListItemText primary="Embed Code" />
              </ListItem>
              <ListItem button selected={selectedSection === 'portal_settings'} onClick={() => setSelectedSection('portal_settings')}>
                <ListItemIcon>
                  <SettingsIcon />
                </ListItemIcon>
                <ListItemText primary="Portal Settings" />
              </ListItem>
            </List>
          </Box>
        )}
        {/* Main Content */}
        <Box sx={{ flexGrow: 1, p: { xs: 2, md: 4 } }}>
          <form onSubmit={handleSubmit(handleSave)}>
            {renderContent()}
            <Divider sx={{ my: 3 }} />
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
              <Button variant="outlined" color="secondary" onClick={handleClose}>Cancel</Button>
              <Button variant="contained" color="primary" type="submit">Save</Button>
            </Box>
          </form>
        </Box>
      </Box>
    </MainCard>
  );
}

export default AddCareerSite;
