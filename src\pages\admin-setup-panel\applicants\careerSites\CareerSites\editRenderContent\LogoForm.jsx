import React from 'react';
import { Grid, Stack } from '@mui/material';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import ApplicantDropzonePage from 'pages/jobrequest/UploadDocument';

const LogoForm = ({ control }) => {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Stack spacing={1}>
          <CustomInputLabel htmlFor="logo">Logo</CustomInputLabel>
          <ApplicantDropzonePage
            name="logo"
            control={control}
            accept={{
              'image/*': ['.jpeg', '.jpg', '.png', '.gif']
            }}
            maxFiles={1}
            maxSize={5242880} // 5MB
          />
        </Stack>
      </Grid>
    </Grid>
  );
};

export default LogoForm; 