import React from 'react';
import { Grid, Stack } from '@mui/material';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';

function PortalDetailsForm({ control }) {
  const businessUnitOptions = [
    { value: 'HR', label: 'HR' },
    { value: 'Finance', label: 'Finance' },
    { value: 'Sales', label: 'Sales' },
    { value: 'Marketing', label: 'Marketing' },
  ];

  const countryOptions = [
    { value: 'USA', label: 'United States' },
    { value: 'CAN', label: 'Canada' },
    { value: 'GBR', label: 'United Kingdom' },
    { value: 'IND', label: 'India' },
  ];

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Stack spacing={1}>
          <CustomInputLabel htmlFor="portal_title">
            Portal Title<span style={{ color: 'red' }}>*</span>
          </CustomInputLabel>
          <CustomNameField
            name="portal_title"
            control={control}
            placeholder="Enter Portal Title"
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            rules={{ required: 'Portal Title is required' }}
          />
        </Stack>
      </Grid>
      <Grid item xs={12}>
        <Stack spacing={1}>
          <CustomInputLabel htmlFor="business_units">
            Business Units<span style={{ color: 'red' }}>*</span>
          </CustomInputLabel>
          <CustomDropdownField
            name="business_units"
            control={control}
            placeholder="Select Business Units"
            options={businessUnitOptions}
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            rules={{ required: 'Business Units are required' }}
          />
        </Stack>
      </Grid>
      <Grid item xs={12}>
        <Stack spacing={1}>
          <CustomInputLabel htmlFor="default_country">Default Country</CustomInputLabel>
          <CustomDropdownField
            name="default_country"
            control={control}
            placeholder="Select Default Country"
            options={countryOptions}
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
          />
        </Stack>
      </Grid>
    </Grid>
  );
}

export default PortalDetailsForm; 