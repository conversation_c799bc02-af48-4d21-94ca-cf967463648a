import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Stack,
  FormControlLabel,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Paper,
  Switch
} from '@mui/material';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import DoubleArrowIcon from '@mui/icons-material/DoubleArrow';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import KeyboardDoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';

import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';

const initialSourceFields = [
  { id: 'middle_name', name: 'Middle Name' },
  { id: 'linkedin_profile', name: 'LinkedIn Profile' },
  { id: 'skype_id', name: 'Skype ID' },
  { id: 'skills', name: 'Skills' },
  { id: 'experience', name: 'Experience' },
  { id: 'gender', name: 'Gender' },
  { id: 'race_ethnicity', name: 'Race/Ethnicity' },
  { id: 'veteran_status', name: 'Veteran Status' }
];

const initialTargetFields = [
  { id: 'first_name', name: 'First Name' },
  { id: 'last_name', name: 'Last Name' },
  { id: 'email', name: 'Email' },
  { id: 'mobile_number', name: 'Mobile Number' },
  { id: 'work_authorization', name: 'Work Authorization' },
  { id: 'address', name: 'Address' },
  { id: 'city', name: 'City' },
  { id: 'state', name: 'State' }
];

const SubmissionSettingsForm = ({ control }) => {
  const [sourceFields, setSourceFields] = useState(initialSourceFields);
  const [targetFields, setTargetFields] = useState(initialTargetFields);
  const [selectedSource, setSelectedSource] = useState(null);
  const [selectedTarget, setSelectedTarget] = useState(null);
  const [referenceDetails, setReferenceDetails] = useState(false);

  const handleMoveToTarget = () => {
    if (selectedSource) {
      setTargetFields((prev) => [...prev, selectedSource]);
      setSourceFields((prev) => prev.filter((item) => item.id !== selectedSource.id));
      setSelectedSource(null);
    }
  };

  const handleMoveAllToTarget = () => {
    setTargetFields((prev) => [...prev, ...sourceFields]);
    setSourceFields([]);
    setSelectedSource(null);
  };

  const handleMoveToSource = () => {
    if (selectedTarget) {
      setSourceFields((prev) => [...prev, selectedTarget]);
      setTargetFields((prev) => prev.filter((item) => item.id !== selectedTarget.id));
      setSelectedTarget(null);
    }
  };

  const handleMoveAllToSource = () => {
    setSourceFields((prev) => [...prev, ...targetFields]);
    setTargetFields([]);
    setSelectedTarget(null);
  };

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        Submission Settings
      </Typography>
      <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
        Choose the required fields for the submission.
      </Typography>

      <Grid container spacing={3} alignItems="center">
        <Grid item xs={5}>
          <Paper variant="outlined" sx={{ height: 300, overflow: 'auto' }}>
            <Box sx={{ p: 1, borderBottom: '1px solid #e0e0e0' }}>
              <Typography variant="subtitle1">Source List</Typography>
            </Box>
            <List dense component="div" role="list">
              {sourceFields.map((item) => (
                <ListItem
                  key={item.id}
                  button
                  onClick={() => setSelectedSource(item)}
                  selected={selectedSource && selectedSource.id === item.id}
                >
                  <ListItemText primary={item.name} />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>
        <Grid item xs={2}>
          <Stack direction="column" spacing={1} alignItems="center">
            <IconButton onClick={handleMoveToTarget} disabled={!selectedSource}>
              <ChevronRightIcon />
            </IconButton>
            <IconButton onClick={handleMoveAllToTarget} disabled={sourceFields.length === 0}>
              <DoubleArrowIcon />
            </IconButton>
            <IconButton onClick={handleMoveToSource} disabled={!selectedTarget}>
              <ChevronLeftIcon />
            </IconButton>
            <IconButton onClick={handleMoveAllToSource} disabled={targetFields.length === 0}>
              <KeyboardDoubleArrowLeftIcon />
            </IconButton>
          </Stack>
        </Grid>
        <Grid item xs={5}>
          <Paper variant="outlined" sx={{ height: 300, overflow: 'auto' }}>
            <Box sx={{ p: 1, borderBottom: '1px solid #e0e0e0' }}>
              <Typography variant="subtitle1">Target List</Typography>
            </Box>
            <List dense component="div" role="list">
              {targetFields.map((item) => (
                <ListItem
                  key={item.id}
                  button
                  onClick={() => setSelectedTarget(item)}
                  selected={selectedTarget && selectedTarget.id === item.id}
                >
                  <ListItemText primary={item.name} />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>
      </Grid>

      <Grid container spacing={2} sx={{ mt: 3 }} alignItems="center">
        <Grid item xs={12} display="flex" alignItems="center">
          <Typography variant="body1" sx={{ mr: 2 }}>
            Reference Details
          </Typography>
          <FormControlLabel
            control={<Switch checked={referenceDetails} onChange={(e) => setReferenceDetails(e.target.checked)} />}
            label=""
          />
        </Grid>
      </Grid>

      {referenceDetails && (
        <Grid container spacing={2} sx={{ mt: 3 }}>
          <Grid item xs={12}>
            <Stack spacing={1}>
              <CustomInputLabel htmlFor="num_references_required">Number of References Required</CustomInputLabel>
              <CustomDropdownField
                name="num_references_required"
                control={control}
                placeholder="Select"
                options={[]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default SubmissionSettingsForm;
