import React, { useState } from 'react';
import { Ty<PERSON>graphy, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { useForm } from 'react-hook-form';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import AddCareerSite from './AddCareerSiteDialog';
import EditCareerSite from './EditCareerSiteDialog';

function CareerSites() {
  const [careerSiteDetails, setCareerSiteDetails] = useState([
    {
      id: 1,
      portal_title: 'Default Career Portal',
      applicable_business_units: 'Chidhagni',
      modified_by: 'Admin',
      last_modified: '2023-01-01',
    }
  ]);

  const { control: searchControl, watch: watchSearch } = useForm();
  const searchQuery = watchSearch('search', '');
  const navigate = useNavigate();
  const location = useLocation();
  const isAdd = location.pathname.endsWith('/add');
  const isEdit = location.pathname.endsWith('/edit');

  const handleAdd = () => {
    navigate('/admin-setup-panel/applicants/career-sites/add');
  };

  const handleAddCareerSite = (newSite) => {
    setCareerSiteDetails(prev => [
      ...prev,
      { ...newSite, id: Date.now() }
    ]);
  };

  const handleEditCareerSite = (updatedSite) => {
    setCareerSiteDetails(prev =>
      prev.map(site => site.id === updatedSite.id ? updatedSite : site)
    );
  };

  const CareerSiteActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      navigate('/admin-setup-panel/applicants/career-sites/edit', { state: { careerSite: params.row } });
      handleClose();
    };

    const handleDelete = () => {
      setCareerSiteDetails((prev) => prev.filter((site) => site.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const careerSiteColumns = [
    {
      field: 'portal_title',
      headerName: 'PORTAL TITLE',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      ),
    },
    {
      field: 'applicable_business_units',
      headerName: 'APPLICABLE BUSINESS UNITS',
      flex: 1,
      minWidth: 200,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      ),
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      ),
    },
    {
      field: 'last_modified',
      headerName: 'LAST MODIFIED',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      ),
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <CareerSiteActionCell params={params} />,
    },
  ];

  const filteredCareerSites = careerSiteDetails.filter(site =>
    (site.portal_title || '').toLowerCase().includes(searchQuery.toLowerCase())
  );

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <>
      <CustomCardHeader
        secondary={
          <Box display="flex" alignItems="center" gap={1}>
            <Button variant="outlined" size="small">
              Activities
            </Button>
            <Button variant="contained" size="small" onClick={handleAdd}>
              + Add
            </Button>
          </Box>
        }
      />
      <Divider sx={{ mb: 1 }} />
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 400, width: '100%' }}>
            <CustomDataGrid
              rows={filteredCareerSites}
              columns={careerSiteColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={filteredCareerSites.length}
            />
          </Box>
        </Grid>
      </Grid>
      {isAdd && (
        <AddCareerSite onSave={handleAddCareerSite} />
      )}
      {isEdit && (
        <EditCareerSite onSave={handleEditCareerSite} />
      )}
    </>
  );
}

export default CareerSites;