import React from 'react';
import { <PERSON>, Typography, Grid, Stack, Button } from '@mui/material';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomFormHelperText from 'components/custom-components/CustomFormHelperText';

const DNSMaskingForm = ({ control }) => {
  return (
    <Box>
      <CustomInputLabel>DNS Masking</CustomInputLabel>
      <CustomFormHelperText variant="body2" color="textSecondary" sx={{ mb: 3 }}>
        Domain masking requires you to set up a CNAME record with your DNS provider, such as GoDaddy or Network Solutions.
      </CustomFormHelperText>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Stack spacing={1}>
            <CustomInputLabel htmlFor="domain">Domain</CustomInputLabel>
            <CustomNameField
              name="domain"
              control={control}
              placeholder="Enter a domain name (like 'careers.mysite.com')"
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Stack>
        </Grid>
      </Grid>

      {/* <Box sx={{ mt: 3 }}>
        <Button type="submit" size="small" variant="contained" color="primary">
          Save Changes
        </Button>
      </Box> */}
    </Box>
  );
};

export default DNSMaskingForm;
