import React from 'react';
import { Box, Typography, TextField, Stack } from '@mui/material';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';

const EmbedCodeForm = () => {
  const embedCodeSnippet = `<!-- Career Site Embed Code -->
<div id="my-career-site"></div>
<script src="https://your-career-site-url.com/embed.js"></script>`;

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        Embed Code
      </Typography>
      <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
        Copy the following code and paste it into your website's HTML where you want the career site to appear.
      </Typography>
      <Stack spacing={1}>
        <CustomInputLabel htmlFor="embed_code_snippet">Embed Code</CustomInputLabel>
        <TextField
          id="embed_code_snippet"
          multiline
          rows={8}
          fullWidth
          value={embedCodeSnippet}
          InputProps={{
            readOnly: true,
          }}
          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
        />
      </Stack>
    </Box>
  );
};

export default EmbedCodeForm; 