import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Paper,
  Stack
} from '@mui/material';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import DoubleArrowIcon from '@mui/icons-material/DoubleArrow';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import KeyboardDoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';

import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';

const initialSourceItems = [
  { id: 'end_client', name: 'End Client' },
  { id: 'priority', name: 'Priority' },
  { id: 'duration', name: 'Duration' },
  { id: 'zip_code', name: 'Zip Code' },
  { id: 'work_authorization', name: 'Work Authorization' },
  { id: 'primary_recruiter', name: 'Primary Recruiter' },
  { id: 'job_start_date', name: 'Job Start Date' },
  { id: 'job_end_date', name: 'Job End Date' },
];

const sortByOptions = [
  { value: 'modified_on', label: 'Modified On' },
  { value: 'created_on', label: 'Created On' },
  { value: 'career_portal_published_date', label: 'Career Portal Published Date' },
];

const sortOrderOptions = [
  { value: 'asc', label: 'ASC' },
  { value: 'desc', label: 'DESC' },
];

const JobDetailListSortOrderForm = ({ control }) => {
  const [sourceItems, setSourceItems] = useState(initialSourceItems);
  const [targetItems, setTargetItems] = useState([]);
  const [selectedSource, setSelectedSource] = useState(null);
  const [selectedTarget, setSelectedTarget] = useState(null);

  const handleMoveToTarget = () => {
    if (selectedSource) {
      setTargetItems((prev) => [...prev, selectedSource]);
      setSourceItems((prev) => prev.filter((item) => item.id !== selectedSource.id));
      setSelectedSource(null);
    }
  };

  const handleMoveAllToTarget = () => {
    setTargetItems((prev) => [...prev, ...sourceItems]);
    setSourceItems([]);
    setSelectedSource(null);
  };

  const handleMoveToSource = () => {
    if (selectedTarget) {
      setSourceItems((prev) => [...prev, selectedTarget]);
      setTargetItems((prev) => prev.filter((item) => item.id !== selectedTarget.id));
      setSelectedTarget(null);
    }
  };

  const handleMoveAllToSource = () => {
    setSourceItems((prev) => [...prev, ...targetItems]);
    setTargetItems([]);
    setSelectedTarget(null);
  };

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        Job Detail List & Sort Order
      </Typography>
      <Grid container spacing={3} alignItems="center">
        <Grid item xs={5}>
          <Paper variant="outlined" sx={{ height: 300, overflow: 'auto' }}>
            <Box sx={{ p: 1, borderBottom: '1px solid #e0e0e0' }}>
              <Typography variant="subtitle1">Source List</Typography>
            </Box>
            <List dense component="div" role="list">
              {sourceItems.map((item) => (
                <ListItem
                  key={item.id}
                  button
                  onClick={() => setSelectedSource(item)}
                  selected={selectedSource && selectedSource.id === item.id}
                >
                  <ListItemText primary={item.name} />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>
        <Grid item xs={2}>
          <Stack direction="column" spacing={1} alignItems="center">
            <IconButton onClick={handleMoveToTarget} disabled={!selectedSource}>
              <ChevronRightIcon />
            </IconButton>
            <IconButton onClick={handleMoveAllToTarget} disabled={sourceItems.length === 0}>
              <DoubleArrowIcon />
            </IconButton>
            <IconButton onClick={handleMoveToSource} disabled={!selectedTarget}>
              <ChevronLeftIcon />
            </IconButton>
            <IconButton onClick={handleMoveAllToSource} disabled={targetItems.length === 0}>
              <KeyboardDoubleArrowLeftIcon />
            </IconButton>
          </Stack>
        </Grid>
        <Grid item xs={5}>
          <Paper variant="outlined" sx={{ height: 300, overflow: 'auto' }}>
            <Box sx={{ p: 1, borderBottom: '1px solid #e0e0e0' }}>
              <Typography variant="subtitle1">Target List</Typography>
            </Box>
            <List dense component="div" role="list">
              {targetItems.map((item) => (
                <ListItem
                  key={item.id}
                  button
                  onClick={() => setSelectedTarget(item)}
                  selected={selectedTarget && selectedTarget.id === item.id}
                >
                  <ListItemText primary={item.name} />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>
      </Grid>

      <Grid container spacing={2} sx={{ mt: 3 }} alignItems="center">
        <Grid item xs={11}>
          <Stack spacing={1} sx={{ mt: 2 }}>
            <CustomInputLabel htmlFor="jobs_sort_order">Jobs Sort Order</CustomInputLabel>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <CustomDropdownField
                  name="job_sort_order_1"
                  control={control}
                  placeholder="Select"
                  options={sortByOptions}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>
              <Grid item xs={6}>
                <CustomDropdownField
                  name="job_sort_order_2"
                  control={control}
                  placeholder="Select"
                  options={sortOrderOptions}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>
            </Grid>
          </Stack>
        </Grid>
      </Grid>
    </Box>
  );
};

export default JobDetailListSortOrderForm; 