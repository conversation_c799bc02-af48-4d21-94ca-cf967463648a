import React from 'react';
import { Grid, Stack } from '@mui/material';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';

const PortalDetailsForm = ({ control }) => {
  
  const businessUnitOptions = [{ value: 'Chidhagni', label: 'HR' }];

  const countryOptions = [
    { label: 'AF', value: 'Afghanistan' },
    { label: 'AL', value: 'Albania' },
    { label: 'DZ', value: 'Algeria' },
    { label: 'AS', value: 'American Samoa' },
    { label: 'AD', value: 'Andorra' },
    { label: 'AO', value: 'Angola' },
    { label: 'AI', value: 'Anguilla' },
    { label: 'AQ', value: 'Antarctica' },
    { label: 'AG', value: 'Antigua and Barbuda' },
    { label: 'AR', value: 'Argentina' },
    { label: 'AM', value: 'Armenia' },
    { label: 'AW', value: 'Aruba' },
    { label: 'AU', value: 'Australia' },
    { label: 'AT', value: 'Austria' },
    { label: 'AZ', value: 'Azerbaijan' },
    { label: 'BS', value: 'Bahamas' },
    { label: 'BH', value: 'Bahrain' },
    { label: 'BD', value: 'Bangladesh' },
    { label: 'BB', value: 'Barbados' },
    { label: 'BY', value: 'Belarus' },
    { label: 'BE', value: 'Belgium' },
    { label: 'BZ', value: 'Belize' },
    { label: 'BJ', value: 'Benin' },
    { label: 'BM', value: 'Bermuda' },
    { label: 'BT', value: 'Bhutan' },
    { label: 'BO', value: 'Bolivia' },
    { label: 'BA', value: 'Bosnia and Herzegovina' },
    { label: 'BW', value: 'Botswana' },
    { label: 'BR', value: 'Brazil' },
    { label: 'IO', value: 'British Indian Ocean Territory' },
    { label: 'VG', value: 'British Virgin Islands' },
    { label: 'BN', value: 'Brunei Darussalam' },
    { label: 'BG', value: 'Bulgaria' },
    { label: 'BF', value: 'Burkina Faso' },
    { label: 'BI', value: 'Burundi' },
    { label: 'KH', value: 'Cambodia' },
    { label: 'CM', value: 'Cameroon' },
    { label: 'CA', value: 'Canada' },
    { label: 'CV', value: 'Cape Verde' },
    { label: 'KY', value: 'Cayman Islands' },
    { label: 'CF', value: 'Central African Republic' },
    { label: 'TD', value: 'Chad' },
    { label: 'CL', value: 'Chile' },
    { label: 'CN', value: 'China' },
    { label: 'CX', value: 'Christmas Island' },
    { label: 'CC', value: 'Cocos (Keeling) Islands' },
    { label: 'CO', value: 'Colombia' },
    { label: 'KM', value: 'Comoros' },
    { label: 'CK', value: 'Cook Islands' },
    { label: 'CR', value: 'Costa Rica' },
    { label: 'HR', value: 'Croatia' },
    { label: 'CU', value: 'Cuba' },
    { label: 'CW', value: 'Cura\u00e7ao' },
    { label: 'CY', value: 'Cyprus' },
    { label: 'CZ', value: 'Czech Republic' },
    { label: 'CD', value: 'Democratic Republic of the Congo' },
    { label: 'DK', value: 'Denmark' },
    { label: 'DJ', value: 'Djibouti' },
    { label: 'DM', value: 'Dominica' },
    { label: 'DO', value: 'Dominican Republic' },
    { label: 'TL', value: 'East Timor' },
    { label: 'EC', value: 'Ecuador' },
    { label: 'EG', value: 'Egypt' },
    { label: 'SV', value: 'El Salvador' },
    { label: 'GQ', value: 'Equatorial Guinea' },
    { label: 'ER', value: 'Eritrea' },
    { label: 'EE', value: 'Estonia' },
    { label: 'ET', value: 'Ethiopia' },
    { label: 'FK', value: 'Falkland Islands (Malvinas)' },
    { label: 'FO', value: 'Faroe Islands' },
    { label: 'FJ', value: 'Fiji' },
    { label: 'FI', value: 'Finland' },
    { label: 'FR', value: 'France' },
    { label: 'GF', value: 'French Guiana' },
    { label: 'PF', value: 'French Polynesia' },
    { label: 'TF', value: 'French Southern Territories' },
    { label: 'GA', value: 'Gabon' },
    { label: 'GM', value: 'Gambia' },
    { label: 'GE', value: 'Georgia' },
    { label: 'DE', value: 'Germany' },
    { label: 'GH', value: 'Ghana' },
    { label: 'GI', value: 'Gibraltar' },
    { label: 'GR', value: 'Greece' },
    { label: 'GL', value: 'Greenland' },
    { label: 'GD', value: 'Grenada' },
    { label: 'GP', value: 'Guadeloupe' },
    { label: 'GU', value: 'Guam' },
    { label: 'GT', value: 'Guatemala' },
    { label: 'GG', value: 'Guernsey' },
    { label: 'GN', value: 'Guinea' },
    { label: 'GW', value: 'Guinea-Bissau' },
    { label: 'GY', value: 'Guyana' },
    { label: 'HT', value: 'Haiti' },
    { label: 'HN', value: 'Honduras' },
    { label: 'HK', value: 'Hong Kong' },
    { label: 'HU', value: 'Hungary' },
    { label: 'IS', value: 'Iceland' },
    { label: 'IN', value: 'India' },
    { label: 'ID', value: 'Indonesia' },
    { label: 'IR', value: 'Iran' },
    { label: 'IQ', value: 'Iraq' },
    { label: 'IE', value: 'Ireland' },
    { label: 'IL', value: 'Israel' },
    { label: 'IT', value: 'Italy' },
    { label: 'JM', value: 'Jamaica' },
    { label: 'JP', value: 'Japan' },
    { label: 'JE', value: 'Jersey' },
    { label: 'JO', value: 'Jordan' },
    { label: 'KZ', value: 'Kazakhstan' },
    { label: 'KE', value: 'Kenya' },
    { label: 'KI', value: 'Kiribati' },
    { label: 'KR', value: 'Korea (Republic of)' },
    { label: 'KW', value: 'Kuwait' },
    { label: 'KG', value: 'Kyrgyzstan' },
    { label: 'LA', value: "Lao People's Democratic Republic" },
    { label: 'LV', value: 'Latvia' },
    { label: 'LB', value: 'Lebanon' },
    { label: 'LS', value: 'Lesotho' },
    { label: 'LR', value: 'Liberia' },
    { label: 'LY', value: 'Libya' },
    { label: 'LI', value: 'Liechtenstein' },
    { label: 'LT', value: 'Lithuania' },
    { label: 'LU', value: 'Luxembourg' },
    { label: 'MO', value: 'Macao' },
    { label: 'MK', value: 'Macedonia (the former Yugoslav Republic of)' },
    { label: 'MG', value: 'Madagascar' },
    { label: 'MW', value: 'Malawi' },
    { label: 'MY', value: 'Malaysia' },
    { label: 'MV', value: 'Maldives' },
    { label: 'ML', value: 'Mali' },
    { label: 'MT', value: 'Malta' },
    { label: 'MH', value: 'Marshall Islands' },
    { label: 'MQ', value: 'Martinique' },
    { label: 'MR', value: 'Mauritania' },
    { label: 'MU', value: 'Mauritius' },
    { label: 'YT', value: 'Mayotte' },
    { label: 'MX', value: 'Mexico' },
    { label: 'FM', value: 'Micronesia (Federated States of)' },
    { label: 'MD', value: 'Moldova (Republic of)' },
    { label: 'MC', value: 'Monaco' },
    { label: 'MN', value: 'Mongolia' },
    { label: 'ME', value: 'Montenegro' },
    { label: 'MS', value: 'Montserrat' },
    { label: 'MA', value: 'Morocco' },
    { label: 'MZ', value: 'Mozambique' },
    { label: 'MM', value: 'Myanmar' },
    { label: 'NA', value: 'Namibia' },
    { label: 'NR', value: 'Nauru' },
    { label: 'NP', value: 'Nepal' },
    { label: 'NL', value: 'Netherlands' },
    { label: 'NC', value: 'New Caledonia' },
    { label: 'NZ', value: 'New Zealand' },
    { label: 'NI', value: 'Nicaragua' },
    { label: 'NE', value: 'Niger' },
    { label: 'NG', value: 'Nigeria' },
    { label: 'NU', value: 'Niue' }
  ];
  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Stack spacing={1}>
          <CustomInputLabel htmlFor="portal_title">
            Portal Title<span style={{ color: 'red' }}>*</span>
          </CustomInputLabel>
          <CustomNameField
            name="portal_title"
            control={control}
            placeholder="Enter Portal Title"
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            rules={{ required: 'Portal Title is required' }}
          />
        </Stack>
      </Grid>
      <Grid item xs={12}>
        <Stack spacing={1}>
          <CustomInputLabel htmlFor="business_units">
            Business Units<span style={{ color: 'red' }}>*</span>
          </CustomInputLabel>
          <CustomDropdownField
            name="business_units"
            control={control}
            placeholder="Select Business Units"
            options={businessUnitOptions}
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            rules={{ required: 'Business Units are required' }}
          />
        </Stack>
      </Grid>
      <Grid item xs={12}>
        <Stack spacing={1}>
          <CustomInputLabel htmlFor="default_country">Default Country</CustomInputLabel>
          <CustomDropdownField
            name="default_country"
            control={control}
            placeholder="Select Default Country"
            options={countryOptions}
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
          />
        </Stack>
      </Grid>
    </Grid>
  );
};

export default PortalDetailsForm; 