import React, { useState } from 'react';
import { Box, Typography, Grid, Stack, FormControlLabel, Switch } from '@mui/material';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomFormHelperText from 'components/custom-components/CustomFormHelperText';

const PortalSettingsForm = () => {
  const [hideSignup, setHideSignup] = useState(false);
  const [hideWithdraw, setHideWithdraw] = useState(false);
  const [skipSecurityQuestions, setSkipSecurityQuestions] = useState(false);
  const [displayJobsAfterSignIn, setDisplayJobsAfterSignIn] = useState(false);

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        Portal Settings
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Stack spacing={1}>
            <FormControlLabel
              control={<Switch checked={hideSignup} onChange={(e) => setHideSignup(e.target.checked)} />}
              label={
                <Box>
                  <CustomInputLabel>Hide signup button on Career Portal</CustomInputLabel>
                  <CustomFormHelperText variant="caption" color="textSecondary">
                    (If it is turned on. Signup option will be hidden for the candidates)
                  </CustomFormHelperText>
                </Box>
              }
            />
          </Stack>
        </Grid>
        <Grid item xs={12}>
          <Stack spacing={1}>
            <FormControlLabel
              control={<Switch checked={hideWithdraw} onChange={(e) => setHideWithdraw(e.target.checked)} />}
              label={
                <Box>
                  <CustomInputLabel>Hide &quot;Withdraw&quot; option for candidates on Career Portal</CustomInputLabel>
                  <CustomFormHelperText variant="caption" color="textSecondary">
                    (If it is turned on. Withdraw button will be hidden for the candidates in Career Portal)
                  </CustomFormHelperText>
                </Box>
              }
            />
          </Stack>
        </Grid>
        <Grid item xs={12}>
          <Stack spacing={1}>
            <FormControlLabel
              control={<Switch checked={skipSecurityQuestions} onChange={(e) => setSkipSecurityQuestions(e.target.checked)} />}
              label={
                <Box>
                  <CustomInputLabel>Skip security questions for the candidate while unblocking career portal account</CustomInputLabel>
                  <CustomFormHelperText variant="caption" color="textSecondary">
                    (If it is turned on. Candidate can unblock by entering email ID.An email with password reset link will be sent to the candidate)
                  </CustomFormHelperText>
                </Box>
              }
            />
          </Stack>
        </Grid>
        <Grid item xs={12}>
          <Stack spacing={1}>
            <FormControlLabel
              control={<Switch checked={displayJobsAfterSignIn} onChange={(e) => setDisplayJobsAfterSignIn(e.target.checked)} />}
              label={
                <Box>
                  <CustomInputLabel>Display Jobs Only after Sign In to the Career portal</CustomInputLabel>
                  <CustomFormHelperText variant="caption" color="textSecondary">
                    (If this setting is enabled,Jobs will be displayed only when candidates Sign In)
                  </CustomFormHelperText>
                </Box>
              }
            />
          </Stack>
        </Grid>
      </Grid>
    </Box>
  );
};

export default PortalSettingsForm; 