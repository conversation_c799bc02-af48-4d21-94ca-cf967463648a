import React from 'react';
import { Box, Typography, Grid, Stack } from '@mui/material';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomNameField from 'components/custom-components/CustomNameField';

const SocialMediaForm = ({ control }) => {
  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        Social Media
      </Typography>
      <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
        Add links to your company's social media profiles.
      </Typography>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Stack spacing={1}>
            <CustomInputLabel htmlFor="twitter_url">Twitter</CustomInputLabel>
            <CustomNameField
              name="twitter_url"
              control={control}
              placeholder="e.g. http://twitter.com"
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Stack>
        </Grid>
        <Grid item xs={12}>
          <Stack spacing={1}>
            <CustomInputLabel htmlFor="facebook_url">Facebook</CustomInputLabel>
            <CustomNameField
              name="facebook_url"
              control={control}
              placeholder="e.g. http://facebook.com"
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Stack>
        </Grid>
        <Grid item xs={12}>
          <Stack spacing={1}>
            <CustomInputLabel htmlFor="linkedin_url">LinkedIn</CustomInputLabel>
            <CustomNameField
              name="linkedin_url"
              control={control}
              placeholder="e.g. http://linkedin.com"
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Stack>
        </Grid>
        <Grid item xs={12}>
          <Stack spacing={1}>
            <CustomInputLabel htmlFor="glassdoor_url">Glassdoor</CustomInputLabel>
            <CustomNameField
              name="glassdoor_url"
              control={control}
              placeholder="e.g. http://glassdoor.com"
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Stack>
        </Grid>
        <Grid item xs={12}>
          <Stack spacing={1}>
            <CustomInputLabel htmlFor="google_plus_url">Google+</CustomInputLabel>
            <CustomNameField
              name="google_plus_url"
              control={control}
              placeholder="e.g. http://www.xxxxxxxx.com"
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Stack>
        </Grid>
        <Grid item xs={12}>
          <Stack spacing={1}>
            <CustomInputLabel htmlFor="blog_url">Blog</CustomInputLabel>
            <CustomNameField
              name="blog_url"
              control={control}
              placeholder="e.g. http://blog.xxxx.com"
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Stack>
        </Grid>
        <Grid item xs={12}>
          <Stack spacing={1}>
            <CustomInputLabel htmlFor="website_url">Website</CustomInputLabel>
            <CustomNameField
              name="website_url"
              control={control}
              placeholder="e.g. http://www.xxxxxxxx.com"
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Stack>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SocialMediaForm; 