import React, { useState } from 'react';
import { Grid, Checkbox, FormControlLabel, Button, Divider } from '@mui/material';
import ReactDraftAuto from 'src/pages/admin-setup-panel/JobPosting/companyOverview/ReactDraftAuto';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomFormHelperText from 'components/custom-components/CustomFormHelperText';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';

function TermsAndCondition() {
  const [showOnSubmit, setShowOnSubmit] = useState(false);

  const handleCheckboxChange = (event) => {
    setShowOnSubmit(event.target.checked);
  };

  return (
    <>
      <CustomCardHeader
        secondary={
          <Button variant="outlined" size="small">
            Activities
          </Button>
        }
      />
      <Divider sx={{ mb: 1 }} />
      <Grid container alignItems="center" justifyContent="space-between">
        <Grid item>
          <CustomInputLabel>Terms and Conditions</CustomInputLabel>
        </Grid>
        <Grid item></Grid>
      </Grid>
      <CustomFormHelperText>
        Please write your own terms and conditions that will be displayed to job seekers who are applying through the Career Portal.
      </CustomFormHelperText>
      <Grid container spacing={2} alignItems="center" sx={{ mt: 1 }}>
        <Grid item xs={12}>
          <ReactDraftAuto />
        </Grid>
        <Grid item xs={12}>
          <FormControlLabel
            control={<Checkbox checked={showOnSubmit} onChange={handleCheckboxChange} color="primary" />}
            label="Display the given Terms and condition on Submit Resume Form"
          />
        </Grid>
      </Grid>
    </>
  );
}

export default TermsAndCondition;
