import React, { useState } from 'react';
import { Box, Tabs, Tab } from '@mui/material';
import MainCard from 'components/MainCard';
import CareerSite from './CareerSites';
import TermsAndCondition from './TermsAndCondition';

function CareerSites() {
  const [selectedTab, setSelectedTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  return (
    <MainCard sx={{ borderRadius: 0 }}>
      <Tabs
        value={selectedTab}
        onChange={handleTabChange}
        aria-label="talent bench lookups tabs"
        sx={{
          mt: -2,
          ml: -2,
          alignItems: 'flex-start',
          justifyContent: 'flex-start',
          borderBottom: 0.2,
          borderColor: 'divider'
        }}
      >
        <Tab label="Career Sites" />
        <Tab label="Terms and Condition" />
      </Tabs>
      <Box sx={{ display: selectedTab === 0 ? 'block' : 'none' }}>
        <CareerSite />
      </Box>
      <Box sx={{  display: selectedTab === 1 ? 'block' : 'none' }}>
        <TermsAndCondition />
      </Box>
    </MainCard>
  );
}

export default CareerSites;
