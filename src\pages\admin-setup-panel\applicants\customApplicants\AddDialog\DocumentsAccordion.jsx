import React, { useState } from 'react';
import { Accordion, AccordionSummary, AccordionDetails, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Switch, IconButton } from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';

const initialFields = [
  { name: 'Resume', show: false, mandatory: false },
  { name: 'Transcripts', show: false, mandatory: false },
  { name: 'Driving License', show: false, mandatory: false },
  { name: 'Passport', show: false, mandatory: false },
  { name: 'SSN', show: false, mandatory: false },
  { name: 'EML File', show: false, mandatory: false }
];

function DocumentsAccordion() {
  const [fields, setFields] = useState(initialFields);

  const handleSwitchChange = (index, key) => (event) => {
    const updated = [...fields];
    updated[index][key] = event.target.checked;
    setFields(updated);
  };

  const moveRow = (from, to) => {
    if (to < 0 || to >= fields.length) return;
    const updated = [...fields];
    const [moved] = updated.splice(from, 1);
    updated.splice(to, 0, moved);
    setFields(updated);
  };

  return (
    <Accordion>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography variant="h6">Documents</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <TableContainer component={Paper}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell><b>FIELD NAME</b></TableCell>
                <TableCell align="center"><b>SHOW / HIDE</b></TableCell>
                <TableCell align="center"><b>MANDATORY</b></TableCell>
                <TableCell align="center"></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {fields.map((row, idx) => (
                <TableRow key={row.name}>
                  <TableCell>{row.name}</TableCell>
                  <TableCell align="center">
                    <Switch checked={row.show} onChange={handleSwitchChange(idx, 'show')} color="primary" />
                  </TableCell>
                  <TableCell align="center">
                    <Switch checked={row.mandatory} onChange={handleSwitchChange(idx, 'mandatory')} color="primary" />
                  </TableCell>
                  <TableCell align="center">
                    <IconButton size="small" onClick={() => moveRow(idx, idx - 1)} disabled={idx === 0}>
                      <ArrowUpwardIcon fontSize="inherit" />
                    </IconButton>
                    <IconButton size="small" onClick={() => moveRow(idx, idx + 1)} disabled={idx === fields.length - 1}>
                      <ArrowDownwardIcon fontSize="inherit" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </AccordionDetails>
    </Accordion>
  );
}

export default DocumentsAccordion; 