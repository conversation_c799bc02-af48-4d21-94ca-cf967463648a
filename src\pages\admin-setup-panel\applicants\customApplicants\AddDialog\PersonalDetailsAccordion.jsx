import React, { useState } from 'react';
import { Accordion, AccordionSummary, AccordionDetails, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Switch, Link } from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const initialFields = [
  { name: 'First Name', show: true, mandatory: false, link: true },
  { name: 'Last Name', show: false, mandatory: false },
  { name: 'Email', show: true, mandatory: true },
  { name: 'Mobile Number', show: false, mandatory: false },
  { name: 'Nickname', show: false, mandatory: false },
  { name: 'Middle Name', show: false, mandatory: false },
  { name: 'Alternate Email Address', show: false, mandatory: false },
  { name: 'Twitter Profile', show: false, mandatory: false },
  { name: 'Video Reference', show: false, mandatory: false },
  { name: 'Work Authorization', show: false, mandatory: false },
  { name: 'Technology', show: false, mandatory: false },
  { name: 'Country', show: false, mandatory: false },
  { name: 'Address', show: false, mandatory: false },
  { name: 'State', show: false, mandatory: false },
  { name: 'City', show: false, mandatory: false },
  { name: 'Zip Code', show: false, mandatory: false },
  { name: 'Notice Period', show: false, mandatory: false },
  { name: 'Industry', show: false, mandatory: false },
  { name: 'Current CTC', show: false, mandatory: false },
  { name: 'Function', show: false, mandatory: false },
  { name: 'Current Company', show: false, mandatory: false },
  { name: 'Work Authorization Expiry', show: false, mandatory: false },
  { name: 'Pan Card Number', show: false, mandatory: false },
  { name: 'Clearance', show: false, mandatory: false },
  { name: 'GPA', show: false, mandatory: false }
];

function PersonalDetailsAccordion() {
  const [fields, setFields] = useState(initialFields);

  const handleSwitchChange = (index, key) => (event) => {
    const updated = [...fields];
    updated[index][key] = event.target.checked;
    setFields(updated);
  };

  return (
    <Accordion>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography variant="h6">Personal Details</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <TableContainer component={Paper}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell><b>FIELD NAME</b></TableCell>
                <TableCell align="center"><b>SHOW / HIDE</b></TableCell>
                <TableCell align="center"><b>MANDATORY</b></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {fields.map((row, idx) => (
                <TableRow key={row.name}>
                  <TableCell>
                    {row.link ? (
                      <Link href="#" underline="hover">{row.name}</Link>
                    ) : (
                      row.name
                    )}
                  </TableCell>
                  <TableCell align="center">
                    <Switch checked={row.show} onChange={handleSwitchChange(idx, 'show')} color="primary" />
                  </TableCell>
                  <TableCell align="center">
                    <Switch checked={row.mandatory} onChange={handleSwitchChange(idx, 'mandatory')} color="primary" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </AccordionDetails>
    </Accordion>
  );
}

export default PersonalDetailsAccordion; 