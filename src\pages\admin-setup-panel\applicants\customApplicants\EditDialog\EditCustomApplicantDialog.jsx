import React, { useEffect, useState } from 'react';
import { Box, Button, Grid, Typography, Divider, IconButton, Stack, Radio, RadioGroup, FormControlLabel } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useForm } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomNameField from 'components/custom-components/CustomNameField';
import ReactDraft from 'pages/client-page/component/ReactDraft';
import PersonalDetailsAccordion from './PersonalDetailsAccordion';
import DocumentsAccordion from './DocumentsAccordion';
import SubmissionFieldsAccordion from './SubmissionFieldsAccordion';
import MainCard from 'components/MainCard';

const useForOptions = [
  { value: 'job_application', label: 'Job application (easy apply for career portal)' },
  { value: 'auto_sourcing', label: 'Auto sourcing application form' },
  { value: 'referral_portal', label: 'Referal portal submission form' },
  { value: 'submit_resume', label: 'Submit resume in career portal' }
];

const defaultForOptions = [
  { value: 'job_application', label: 'Job application (easy apply for career portal)' },
  { value: 'referral_portal', label: 'Referal portal submission form' }
];

function EditCustomApplicantDialog({ onClose, onSave, initialData = {} }) {
  const { control, handleSubmit, reset } = useForm({
    defaultValues: {
      name: '',
      use_for: '',
      default_for: '',
      // description: '',
    }
  });
  const [category, setCategory] = useState('submission');

  useEffect(() => {
    if (initialData) {
      reset({
        name: initialData.application_name || '',
        use_for: initialData.use_for || '',
        default_for: initialData.default_for || '',
        // description: initialData.description || '',
      });
      setCategory(initialData.category || 'submission');
    }
  }, [initialData, reset]);

  const handleSave = (data) => {
    onSave({ ...data, category });
    reset();
  };

  return (
    <MainCard
      title="Edit Custom Application"
      secondary={
        <Box display="flex" alignItems="center" gap={2}>
          <IconButton onClick={onClose} sx={{ color: 'text.secondary' }}>
            <ArrowBackIcon />
          </IconButton>
        </Box>
      }
      sx={{ borderRadius: 0, backgroundColor: 'white', mt: 1 }}
    >
      <Box component="form" onSubmit={handleSubmit(handleSave)} sx={{ p: 1 }}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
          <Grid container spacing={2} alignItems="center" sx={{ mt: 0}}>
          <Grid item xs={12} sm={3} lg={6} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
          <CustomInputLabel htmlFor="name">Name</CustomInputLabel>
              </Grid>
              <Grid item xs={12} sm={9} lg={6}>             
                <CustomNameField name="name" control={control} placeholder="" sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} />
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12}>
          <Grid container spacing={2} alignItems="center" sx={{ mt: 0}}>
          <Grid item xs={12} sm={3} lg={6} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                 <CustomInputLabel>Application Category</CustomInputLabel>
              </Grid>
              <Grid item xs={12} sm={9} lg={6}>             
                <RadioGroup row value={category} onChange={(e) => setCategory(e.target.value)}>
                  <FormControlLabel value="submission" control={<Radio />} label="Submission Form" />
                </RadioGroup>
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12}>
          <Grid container spacing={2} alignItems="center" sx={{ mt: 0}}>
          <Grid item xs={12} sm={3} lg={6} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                <CustomInputLabel>Use This Form For</CustomInputLabel>
              </Grid>
              <Grid item xs={12} sm={9} lg={6}>             
                <CustomDropdownField
                  name="use_for"
                  control={control}
                  placeholder="Select"
                  options={useForOptions}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12}>
          <Grid container spacing={2} alignItems="center" sx={{ mt: 0}}>
          <Grid item xs={12} sm={3} lg={6} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                <CustomInputLabel>Make This Default For</CustomInputLabel>
              </Grid>
             <Grid item xs={12} sm={9} lg={6}>             

                <CustomDropdownField
                  name="default_for"
                  control={control}
                  placeholder="Select"
                  options={defaultForOptions}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12}>
            <Stack spacing={1}>
              <CustomInputLabel>Description</CustomInputLabel>
              <ReactDraft />
            </Stack>
          </Grid>
          <Grid item xs={12}>
            <PersonalDetailsAccordion />
          </Grid>
          <Grid item xs={12}>
            <DocumentsAccordion />
          </Grid>
          <Grid item xs={12}>
            <SubmissionFieldsAccordion />
          </Grid>
        </Grid>
        <Divider sx={{ my: 2 }} />
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            onClick={() => {
              reset();
              onClose();
            }}
            size="small"
            variant="outlined"
          >
            Cancel
          </Button>
          <Button type="submit" size="small" variant="contained" color="primary">
            Save
          </Button>
        </Box>
        
      </Box>
    </MainCard>
    
  );
}

export default EditCustomApplicantDialog;
