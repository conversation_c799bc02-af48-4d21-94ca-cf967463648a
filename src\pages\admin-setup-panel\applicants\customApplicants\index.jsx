import React, { useState } from 'react';
import { Box, Button, Grid, IconButton, Menu, MenuItem, Divider, Typography, Tooltip } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import AddCustomApplicantDialog from './AddDialog/AddCustomApplicantDialog';
import EditCustomApplicantDialog from './EditDialog/EditCustomApplicantDialog';
import MoreVertIcon from '@mui/icons-material/MoreVert';

function CustomApplicants() {
  const [customApplications, setCustomApplications] = useState([
    {
      id: 1,
      application_name: 'Sample Application',
      created_by: 'Admin',
      modified_by: 'Admin',
      modified_on: '2023-01-01'
    }
  ]);
  const [search, setSearch] = useState('');
  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });
  const [currentView, setCurrentView] = useState('list'); // 'list', 'add', 'edit'
  const [selectedRow, setSelectedRow] = useState(null);
  const [anchorEl, setAnchorEl] = useState(null);

  const filteredRows = customApplications.filter((app) => app.application_name.toLowerCase().includes(search.toLowerCase()));

  const handleMenuOpen = (event, row) => {
    setAnchorEl(event.currentTarget);
    setSelectedRow(row);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEditClick = () => {
    setCurrentView('edit');
    setAnchorEl(null);
  };

  const handleAddClick = () => {
    setCurrentView('add');
  };

  const handleBackToList = () => {
    setCurrentView('list');
    setSelectedRow(null);
  };

  const handleAddSave = (data) => {
    const newApp = {
      id: customApplications.length + 1,
      ...data,
      created_by: 'Admin',
      modified_by: 'Admin',
      modified_on: new Date().toISOString().slice(0, 10),
      application_name: data.name || 'Untitled Application'
    };
    setCustomApplications([...customApplications, newApp]);
    setCurrentView('list');
  };

  const handleEditSave = (data) => {
    setCustomApplications(customApplications.map(app =>
      app.id === selectedRow.id
        ? { ...app, ...data, modified_on: new Date().toISOString().slice(0, 10), application_name: data.name || app.application_name }
        : app
    ));
    setCurrentView('list');
    setSelectedRow(null);
  };

  const handleDelete = (id) => {
    setCustomApplications(customApplications.filter(app => app.id !== id));
  };

  const ActionCell = ({ params }) => {
    const [menuAnchor, setMenuAnchor] = useState(null);
    const open = Boolean(menuAnchor);
    const handleClick = (event) => setMenuAnchor(event.currentTarget);
    const handleClose = () => setMenuAnchor(null);
    const handleEdit = () => {
      const row = params.row;
      setSelectedRow({
        ...row,
        name: row.application_name || '',
        use_for: row.use_for || '',
        default_for: row.default_for || '',
        category: row.category || 'submission',
      });
      setCurrentView('edit');
      handleClose();
    };
    const handleDeleteClick = () => {
      handleDelete(params.row.id);
      handleClose();
    };
    return (
      <>
        <Tooltip title="More actions">
          <IconButton size="small" onClick={handleClick}>
            <MoreVertIcon />
          </IconButton>
        </Tooltip>
        <Menu anchorEl={menuAnchor} open={open} onClose={handleClose}>
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDeleteClick}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const columns = [
    {
      field: 'application_name',
      headerName: 'APPLICATION NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <ActionCell params={params} />
    }
  ];

  if (currentView === 'add') {
    return <AddCustomApplicantDialog onClose={handleBackToList} onSave={handleAddSave} />;
  }

  if (currentView === 'edit') {
    return <EditCustomApplicantDialog onClose={handleBackToList} onSave={handleEditSave} initialData={selectedRow} />;
  }

  return (
    <MainCard
      title="Custom Applications"
      sx={{ borderRadius: '0%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
    >
      <CustomCardHeader
        secondary={
          <Box display="flex" alignItems="center" gap={1}>
            <Button variant="outlined" size="small">
              Activities
            </Button>
            <Button variant="contained" size="small" color="primary" onClick={handleAddClick}>
              + Add
            </Button>
          </Box>
        }
        sx={{ mt: -2 }}
      />
      <Divider sx={{ mb: 1 }} />
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={filteredRows}
              columns={columns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={filteredRows.length}
            />
          </Box>
        </Grid>
      </Grid>
    </MainCard>
  );
}

export default CustomApplicants;
