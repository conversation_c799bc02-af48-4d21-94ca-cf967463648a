import React, { useEffect } from 'react';
import { Drawer, Typo<PERSON>, Box, Divider, IconButton, Button, Grid, Stack, FormControlLabel, Checkbox } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm, Controller } from 'react-hook-form';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';

const rolesOptions = [
  { key: 'Administrator', value: 'Administrator' },
  { key: 'Hiring Manager', value: 'Hiring Manager' },
  { key: 'HR Manager', value: 'HR Manager' },
  { key: 'Lead Recruiter', value: 'Lead Recruiter' },
  { key: 'Technical Recruiter', value: 'Technical Recruiter' },
];

function EditDocumentTypeDialog({ open, onClose, onSave, documentTypeData }) {
  const { control, handleSubmit, reset, setValue } = useForm();

  useEffect(() => {
    if (open && documentTypeData) {
      setValue('documentType', documentTypeData.documentType || '');
      setValue('addToEForms', documentTypeData.addToEForms || false);
      setValue('doesExpire', documentTypeData.doesExpire || false);
      setValue('roles', documentTypeData.roles || '');
    }
  }, [open, documentTypeData, setValue]);

  useEffect(() => { if (!open) reset(); }, [open, reset]);

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box sx={{ width: { xs: 300, sm: 400 }, height: '100%', display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">Edit Document Type</Typography>
            <IconButton onClick={onClose}><CloseIcon /></IconButton>
          </Box>
        </Box>
        <Divider />
        <Box component="form" onSubmit={handleSubmit((data) => { onSave({ ...documentTypeData, ...data }); reset(); })} sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="documentType">Document Type *</CustomInputLabel>
                <CustomNameField
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} name="documentType" control={control} placeholder="Aadhar Card" required />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Controller
                name="addToEForms"
                control={control}
                defaultValue={false}
                render={({ field }) => (
                  <FormControlLabel
                    control={<Checkbox {...field} checked={field.value || false} sx={{ color: 'rgba(99, 114, 131, 1)' }} />}
                    label={<Typography>When Checked, the Document Type will be Added to eForms</Typography>}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                name="doesExpire"
                control={control}
                defaultValue={false}
                render={({ field }) => (
                  <FormControlLabel
                    control={<Checkbox {...field} checked={field.value || false} sx={{ color: 'rgba(99, 114, 131, 1)' }} />}
                    label={<Typography>Does it expire</Typography>}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <CustomInputLabel htmlFor="roles">Restrict document to Roles</CustomInputLabel>
              <CustomDropdownField
                name="roles"
                control={control}
                placeholder="Select Role"
                options={rolesOptions}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                rules={{ required: 'Role is required' }}
              />
            </Grid>
          </Grid>
        </Box>
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button onClick={() => { reset(); onClose(); }} size="small" variant="outlined">Cancel</Button>
          <Button type="submit" size="small" variant="contained" color="primary">Save</Button>
        </Box>
      </Box>
    </Drawer>
  );
}

export default EditDocumentTypeDialog; 