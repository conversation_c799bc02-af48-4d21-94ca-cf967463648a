import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import AddNoticePeriodDialog from './AddNoticePeriodDialog';
import EditNoticePeriodDialog from './EditNoticePeriodDialog';

function NoticePeriods() {
  const [noticePeriodDetails, setNoticePeriodDetails] = useState([
    {
      id: 1,
      period_name: '30 Days',
      created_by: 'Admin',
      created_on: '2023-01-01',
      modified_by: 'Admin',
      modified_on: '2023-01-01',
    },
    {
      id: 2,
      period_name: '60 Days',
      created_by: 'Admin',
      created_on: '2023-01-05',
      modified_by: 'Admin',
      modified_on: '2023-01-05',
    },
  ]);

  const { control: searchControl, watch: watchSearch } = useForm();
  const searchQuery = watchSearch('search', '');

  const [openAddNoticePeriodDialog, setOpenAddNoticePeriodDialog] = useState(false);
  const [openEditNoticePeriodDialog, setOpenEditNoticePeriodDialog] = useState(false);
  const [selectedNoticePeriod, setSelectedNoticePeriod] = useState(null);

  const handleAddNoticePeriodDialogOpen = () => {
    setOpenAddNoticePeriodDialog(true);
  };

  const handleAddNoticePeriodDialogClose = () => {
    setOpenAddNoticePeriodDialog(false);
  };

  const handleAddNoticePeriodSave = (data) => {
    setNoticePeriodDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'User',
        modified_on: new Date().toISOString().slice(0, 10),
      },
    ]);
    handleAddNoticePeriodDialogClose();
  };

  const handleEditNoticePeriodSave = (updatedPeriod) => {
    setNoticePeriodDetails((prev) => prev.map((period) => (period.id === updatedPeriod.id ? updatedPeriod : period)));
    setOpenEditNoticePeriodDialog(false);
    setSelectedNoticePeriod(null);
  };

  const NoticePeriodActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedNoticePeriod(params.row);
      setOpenEditNoticePeriodDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete notice period:', params.row);
      setNoticePeriodDetails((prev) => prev.filter((period) => period.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const noticePeriodColumns = [
    {
      field: 'period_name',
      headerName: 'APPLICANT NOTICE PERIOD',
      flex: 1,
      minWidth: 200,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'LAST MODIFIED',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <NoticePeriodActionCell params={params} />
    },
  ];

  const filteredNoticePeriods = noticePeriodDetails.filter(period =>
    period.period_name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <MainCard
      title="Notice Periods"
      sx={{ borderRadius: '0%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
    >
      <CustomCardHeader
        secondary={
          <Box display="flex" alignItems="center" gap={1}>
            <Button variant="outlined" size="small">
              Activities
            </Button>
            <Button variant="contained" size="small" color="primary" onClick={handleAddNoticePeriodDialogOpen}>
              + Add
            </Button>
          </Box>
        }
        sx={{ mt: -2 }}
      />
       <Divider sx={{ mb: 1 }} />
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 400, width: '100%' }}>
            <CustomDataGrid
              rows={filteredNoticePeriods}
              columns={noticePeriodColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={filteredNoticePeriods.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddNoticePeriodDialog
        open={openAddNoticePeriodDialog}
        onClose={handleAddNoticePeriodDialogClose}
        onSave={handleAddNoticePeriodSave}
      />
      <EditNoticePeriodDialog
        open={openEditNoticePeriodDialog}
        onClose={() => setOpenEditNoticePeriodDialog(false)}
        onSave={handleEditNoticePeriodSave}
        noticePeriod={selectedNoticePeriod}
      />
    </MainCard>
  );
}

export default NoticePeriods;
