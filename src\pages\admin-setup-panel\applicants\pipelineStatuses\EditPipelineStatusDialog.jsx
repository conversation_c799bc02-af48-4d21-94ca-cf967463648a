import React from 'react';
import { Drawer, Typography, <PERSON>, Divider, IconButton, Button, Grid, Stack, FormControlLabel, Checkbox } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm, Controller } from 'react-hook-form';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';

const statusOptions = [
  { value: 'Interview Scheduled', label: 'Interview Scheduled' },
  { value: 'Shortlisted', label: 'Shortlisted' },
  // Add more statuses as needed
];

function EditPipelineStatusDialog({ open, onClose, onSave, pipelineStatus }) {
  const { control, handleSubmit, reset, setValue } = useForm();
  React.useEffect(() => {
    if (open && pipelineStatus) {
      setValue('status', pipelineStatus.status || '');
      setValue('email_alert_title', pipelineStatus.email_alert_title || '');
      setValue('header', pipelineStatus.header || '');
      setValue('trigger_mail', pipelineStatus.trigger_mail || false);
    }
  }, [open, pipelineStatus, setValue]);
  React.useEffect(() => { if (!open) reset(); }, [open, reset]);
  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box sx={{ width: { xs: '100%', sm: 400 }, height: '100%', display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">Edit Pipeline Status</Typography>
            <IconButton onClick={onClose}><CloseIcon /></IconButton>
          </Box>
        </Box>
        <Divider />
        <Box component="form" onSubmit={handleSubmit((data) => { onSave({ ...pipelineStatus, ...data }); reset(); })} sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="status">Status of Applicant is changed to *</CustomInputLabel>
                <CustomDropdownField
                  name="status"
                  control={control}
                  placeholder="Select Status"
                  options={statusOptions}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  rules={{ required: 'Status is required' }}
                />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="email_alert_title">Email alert title *</CustomInputLabel>
                <CustomNameField
                  name="email_alert_title"
                  control={control}
                  placeholder="Enter Email Alert Title"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  required
                />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="header">Header</CustomInputLabel>
                <CustomNameField
                  name="header"
                  control={control}
                  placeholder="Enter Header"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Controller
                name="trigger_mail"
                control={control}
                defaultValue={false}
                render={({ field }) => (
                  <FormControlLabel
                    control={<Checkbox {...field} checked={field.value || false} />}
                    label="Trigger Mail"
                  />
                )}
              />
            </Grid>
          </Grid>
        </Box>
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button onClick={() => { reset(); onClose(); }} size="small" variant="outlined">Cancel</Button>
          <Button type="submit" size="small" variant="contained" color="primary">Save</Button>
        </Box>
      </Box>
    </Drawer>
  );
}

export default EditPipelineStatusDialog; 