import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, TextField, Menu, MenuItem, Divider } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import AddPipelineStatusDialog from './AddPipelineStatusDialog';
import EditPipelineStatusDialog from './EditPipelineStatusDialog';

function PipelineStatuses() {
  const [pipelineStatuses, setPipelineStatuses] = useState([
    {
      id: 1,
      name: 'Shortlisted',
      created_by: 'Admin',
      modified_by: 'Admin',
      last_modified: '2023-01-01'
    }
  ]);
  const [search, setSearch] = useState('');
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [selectedPipelineStatus, setSelectedPipelineStatus] = useState(null);

  const handleAddDialogOpen = () => setOpenAddDialog(true);
  const handleAddDialogClose = () => setOpenAddDialog(false);
  const handleAddSave = (data) => {
    setPipelineStatuses((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        modified_by: 'User',
        last_modified: new Date().toISOString().slice(0, 10)
      }
    ]);
    handleAddDialogClose();
  };
  const handleEditSave = (updated) => {
    setPipelineStatuses((prev) => prev.map((item) => (item.id === updated.id ? updated : item)));
    setOpenEditDialog(false);
    setSelectedPipelineStatus(null);
  };
  const PipelineStatusActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);
    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };
    const handleClose = () => setAnchorEl(null);
    const handleEdit = () => {
      setSelectedPipelineStatus(params.row);
      setOpenEditDialog(true);
      handleClose();
    };
    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu anchorEl={anchorEl} open={open} onClose={handleClose} anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }} transformOrigin={{ vertical: 'top', horizontal: 'right' }}>
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
        </Menu>
      </>
    );
  };
  const columns = [
    {
      field: 'name',
      headerName: 'NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'last_modified',
      headerName: 'LAST MODIFIED',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <PipelineStatusActionCell params={params} />
    }
  ];
  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });
  const filteredRows = pipelineStatuses.filter((row) => row.name.toLowerCase().includes(search.toLowerCase()));
  return (
 <MainCard
      title="Pipeline Statuses"
      sx={{ borderRadius: '0%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
    >
      <CustomCardHeader
       
        secondary={
          <Box display="flex" alignItems="center" gap={1}>
            <Button variant="outlined" size="small">Activities</Button>
            <Button variant="contained" size="small" color="primary" onClick={handleAddDialogOpen}>
              + Add
            </Button>
          </Box>
        }
        sx={{ mt: -2 }}
      />
     <Divider sx={{ mb: 1 }} />

    
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={filteredRows}
              columns={columns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={filteredRows.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddPipelineStatusDialog
        open={openAddDialog}
        onClose={handleAddDialogClose}
        onSave={handleAddSave}
      />
      <EditPipelineStatusDialog
        open={openEditDialog}
        onClose={() => setOpenEditDialog(false)}
        onSave={handleEditSave}
        pipelineStatus={selectedPipelineStatus}
      />
    </MainCard>
  );
}

export default PipelineStatuses;
