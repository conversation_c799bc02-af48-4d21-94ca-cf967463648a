import React from 'react';
import { Grid, Switch, FormControlLabel, Checkbox, TextField, FormHelperText, Box, Button } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import { useForm, Controller } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomExperience from 'components/custom-components/CustomExperience';

const periodOptions = [
  { value: 'Per day', label: 'Per day' },
  { value: 'Per week', label: 'Per week' },
  { value: 'Per month', label: 'Per month' }
];

function ProfileDownload() {
  const [limitEnabled, setLimitEnabled] = React.useState(true);
  const { control } = useForm();

  return (
    <MainCard
      title="Profile Download Notification"
      secondary={
        <Box display="flex" alignItems="center" gap={1}>
          <Button variant="outlined" size="small">
            Activities
          </Button>
          <Button variant="contained" size="small" color="primary">
            Download History
          </Button>
        </Box>
      }
    >
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12}>
          <Grid container spacing={2} alignItems="center" justifyContent="space-between">
            <Grid item xs={12} sm={6} lg={6} xl={6}>
              <CustomInputLabel sx={{ whiteSpace: 'normal', textOverflow: 'unset', overflow: 'visible' }}>
                Limit Profile Downloads
              </CustomInputLabel>
              <FormHelperText>
                (Restrict users from downloading applicant profiles. Enable to configure download limits and notifications and restrict
                users from downloading once the limit is reached.)
              </FormHelperText>
            </Grid>
            <Grid item xs={12} sm={6} lg={6} xl={6} style={{ textAlign: 'left' }}>
              <FormControlLabel
                control={<Switch checked={limitEnabled} onChange={(e) => setLimitEnabled(e.target.checked)} color="primary" />}
                label=""
              />
            </Grid>
          </Grid>

          {limitEnabled && (
            <>
              <Grid container spacing={2} alignItems="center" sx={{ mt: 2 }}>
                <Grid item xs={12} sm={6} lg={6} xl={6}>
                  <CustomInputLabel>Profile Download Limit</CustomInputLabel>
                  <FormHelperText>Set the maximum number of profiles a user can download in the selected period.</FormHelperText>
                </Grid>
                <Grid item xs={12} sm={3} lg={3} xl={3}>
                  <CustomExperience
                    name="download_limit"
                    control={control}
                    placeholder="Per limit"
                    sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  />
                </Grid>
                <Grid item xs={12} sm={3} lg={3} xl={3}>
                  <CustomDropdownField
                    name="download_period"
                    control={control}
                    placeholder="Per day"
                    options={periodOptions}
                    sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  />
                </Grid>
              </Grid>
              <Grid container spacing={2} alignItems="center" sx={{ mt: 2 }}>
                <Grid item xs={12} sm={6} lg={6} xl={6}>
                  <CustomInputLabel>Which Profile Do You Want to Monitor?</CustomInputLabel>
                  <FormHelperText>Select which types of profile downloads to monitor for this limit.</FormHelperText>
                </Grid>
                <Grid item xs={12} sm={6} lg={6} xl={6}>
                  <FormControlLabel control={<Checkbox />} label="Downloads Of Own Profiles" />
                  <FormControlLabel control={<Checkbox />} label="Download of Profiles Created by Others" />
                </Grid>
              </Grid>
              <Grid container spacing={2} alignItems="center" sx={{ mt: 2 }}>
                <Grid item xs={12} sm={6} lg={6} xl={6}>
                  <CustomInputLabel>Which Role do you Want to Monitor?</CustomInputLabel>
                </Grid>
                <Grid item xs={12} sm={6} lg={6} xl={6}>
                  <CustomDropdownField
                    name="monitor_role"
                    control={control}
                    placeholder="Select"
                    options={[]}
                    sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  />
                </Grid>
              </Grid>
              <Grid container spacing={2} alignItems="center" sx={{ mt: 2 }}>
                <Grid item xs={12} sm={6} lg={6} xl={6}>
                  <CustomInputLabel>Select Users to Notify When Limit is Reached</CustomInputLabel>
                </Grid>
                <Grid item xs={12} sm={6} lg={6} xl={6}>
                  <CustomDropdownField
                    name="notify_users"
                    control={control}
                    placeholder="Select"
                    options={[]}
                    sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  />
                </Grid>
              </Grid>
              <Grid container spacing={2} alignItems="center" sx={{ mt: 2 }}>
                <Grid item xs={12} sm={6} lg={6} xl={6}>
                  <CustomInputLabel>Restrict Users From Downloading Once Limit is Reached</CustomInputLabel>
                  <FormHelperText>
                    If enabled, users will be prevented from downloading more profiles after reaching the limit.
                  </FormHelperText>
                </Grid>
                <Grid item xs={12} sm={6} lg={6} xl={6}>
                  <FormControlLabel control={<Switch color="primary" />} label="" />
                </Grid>
              </Grid>
            </>
          )}
        </Grid>
      </Grid>
    </MainCard>
  );
}

export default ProfileDownload;
