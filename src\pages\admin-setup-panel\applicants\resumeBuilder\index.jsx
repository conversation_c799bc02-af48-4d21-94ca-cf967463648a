import React, { useState } from 'react';
import { <PERSON>po<PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import AddResumeTemplateDialog from './AddResumeTemplateDialog';
import EditResumeTemplateDialog from './EditResumeTemplateDialog';

function ResumeBuilder() {
  const [templates, setTemplates] = useState([
    {
      id: 1,
      template_name: 'Default Template',
      description: 'A basic resume template',
      modified_on: '2024-06-01'
    }
  ]);

  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);

  const handleAddDialogOpen = () => setOpenAddDialog(true);
  const handleAddDialogClose = () => setOpenAddDialog(false);
  const handleAddSave = (data) => {
    setTemplates((prev) => [
      ...prev,
      {
        id: prev.length ? Math.max(...prev.map((t) => t.id)) + 1 : 1,
        ...data,
        modified_on: new Date().toISOString().slice(0, 10)
      }
    ]);
    handleAddDialogClose();
  };

  const handleEditSave = (updated) => {
    setTemplates((prev) =>
      prev.map((t) => (t.id === updated.id ? { ...t, ...updated, modified_on: new Date().toISOString().slice(0, 10) } : t))
    );
    setOpenEditDialog(false);
    setSelectedTemplate(null);
  };

  const TemplateActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);
    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };
    const handleClose = () => setAnchorEl(null);
    const handleEdit = () => {
      setSelectedTemplate(params.row);
      setOpenEditDialog(true);
      handleClose();
    };
    const handleDelete = () => {
      setTemplates((prev) => prev.filter((t) => t.id !== params.row.id));
      handleClose();
    };
    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const columns = [
    {
      field: 'template_name',
      headerName: 'TEMPLATE NAME',
      flex: 1,
      minWidth: 200,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <TemplateActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
<MainCard
      title="Resume Builder"
      sx={{ borderRadius: '0%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
    >
      <CustomCardHeader
       
        secondary={
          <Box display="flex" alignItems="center" gap={1}>
            <Button variant="contained" size="small" color="primary" onClick={handleAddDialogOpen}>
            + Add
          </Button>
          </Box>
        }
        sx={{ mt: -2 }}
      />
     <Divider sx={{ mb: 1 }} />
    
   
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 400, width: '100%' }}>
            <CustomDataGrid
              rows={templates}
              columns={columns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={templates.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddResumeTemplateDialog open={openAddDialog} onClose={handleAddDialogClose} onSave={handleAddSave} />
      <EditResumeTemplateDialog
        open={openEditDialog}
        onClose={() => setOpenEditDialog(false)}
        onSave={handleEditSave}
        template={selectedTemplate}
      />
    </MainCard>
  );
}

export default ResumeBuilder;
