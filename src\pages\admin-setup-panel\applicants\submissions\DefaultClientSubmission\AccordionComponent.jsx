import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  FormControlLabel,
  Checkbox,
  Stack,
  Box,
  Chip,
  RadioGroup,
  Radio
} from '@mui/material';
import CustomAccordion from 'components/custom-components/CustomAccordion';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomN<PERSON>Field from 'components/custom-components/CustomNameField';
import ReactDraft<PERSON>uto from './ReactDraftAuto';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomFormHelperText from 'components/custom-components/CustomFormHelperText';
import { Controller } from 'react-hook-form';

function AccordionComponent({
  control,
  tableOptions,
  columnOptions,
  selectOptions,
  handleAddMergeField,
  addReferences,
  handleAddReferences,
  subjectFields,
  handleDeleteChip,
  descriptionFields,
  select,
  table,
  column
}) {
  return (
    <CustomAccordion
      defaultExpanded
      summary={
        <CustomInputLabel>
          Default Client Submission Format{' '}
          <CustomFormHelperText>
            (When a user clicks the &apos;Submit to Client&apos; option from the submission record, an email with the fields and content
            configured in this template will be sent to Client Contact)
          </CustomFormHelperText>
        </CustomInputLabel>
      }
      accordionSx={{ mb: 2 }}
    >
      <Grid container spacing={2} mt={1}>
        <Grid item xs={12} sm={12}>
          <CustomInputLabel>Format Style</CustomInputLabel>
          <Controller
            name="format_style"
            control={control}
            render={({ field }) => (
              <RadioGroup row {...field}>
                <FormControlLabel value="default" control={<Radio />} label="Default Format" />
                <FormControlLabel value="tabular" control={<Radio />} label="Tabular Format" />
              </RadioGroup>
            )}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <CustomInputLabel>Select Table</CustomInputLabel>
          <CustomDropdownField
            name="table"
            control={control}
            placeholder="Select Table"
            options={tableOptions}
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <CustomInputLabel>Select Column</CustomInputLabel>
          <CustomDropdownField
            name="column"
            control={control}
            placeholder="Select Column"
            options={columnOptions}
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <CustomInputLabel>Select</CustomInputLabel>
          <CustomDropdownField
            name="select"
            control={control}
            placeholder="Select"
            options={selectOptions}
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
          />
        </Grid>
        <Grid item>
          <Button variant="contained" sx={{ mt: 2.3 }} onClick={handleAddMergeField} fullWidth>
            Add
          </Button>
        </Grid>
        <Grid item xs={12}>
          <FormControlLabel
            control={<Checkbox checked={addReferences} onChange={handleAddReferences} />}
            label="Add References (For Applicant Submissions Only)"
          />
        </Grid>
        <Grid item xs={12}>
          <Stack spacing={1}>
            <CustomInputLabel>Email Subject</CustomInputLabel>
            <CustomNameField
              name="subject"
              control={control}
              placeholder="Enter Email Subject"
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
            <Box display="flex" flexWrap="wrap" gap={1}>
              {subjectFields.map((field) => (
                <Chip key={field} label={field} onDelete={() => handleDeleteChip('subject', field)} color="primary" size="small" />
              ))}
            </Box>
          </Stack>
        </Grid>
        <Grid item xs={12}>
          <Stack spacing={1}>
            <CustomInputLabel>Description</CustomInputLabel>
            <ReactDraftAuto
              name="description"
              control={control}
              mergeField={select === 'description' && table && column ? `{${table}: ${column}}` : ''}
            />
            <Box display="flex" flexWrap="wrap" gap={1}>
              {descriptionFields.map((field) => (
                <Chip key={field} label={field} onDelete={() => handleDeleteChip('description', field)} color="primary" size="small" />
              ))}
            </Box>
          </Stack>
        </Grid>
      </Grid>
    </CustomAccordion>
  );
}

export default AccordionComponent;
