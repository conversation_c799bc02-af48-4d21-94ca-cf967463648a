import { Box, Stack, Grid } from '@mui/material';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import { useForm } from 'react-hook-form';
import DefaultClientSubmissionTab from './DefaultClientSubmissionTab';
import { useState } from 'react';
import AccordionComponent from './AccordionComponent';
import AccordionComponentVendor from './AccordionComponentVendor';

function DefaultClientSubmission() {
  // react-hook-form setup
  const { control, setValue, watch } = useForm({
    defaultValues: {
      table: '',
      column: '',
      select: '',
      subject: '',
      description: ''
    }
  });

  // Placeholder state for form fields
  const [addReferences, setAddReferences] = useState(false);
  const [subjectFields, setSubjectFields] = useState([]);
  const [descriptionFields, setDescriptionFields] = useState([]);

  // Handlers
  const handleAddReferences = (event) => setAddReferences(event.target.checked);

  // Dropdown options
  const tableOptions = [
    { value: 'JobPosting', label: 'Job Posting' },
    { value: 'Applicant', label: 'Applicant' }
  ];
  const columnOptions = [
    { value: 'position_title', label: 'Position Title' },
    { value: 'client_jot', label: 'Client Jot' }
  ];
  const selectOptions = [
    { value: 'subject', label: 'Add to Subject' },
    { value: 'description', label: 'Add to Description' }
  ];

  // Watch dropdowns
  const table = watch('table');
  const column = watch('column');
  const select = watch('select');
  const subject = watch('subject');
  const description = watch('description');

  // Add merge field to subject or description and show as chip
  const handleAddMergeField = () => {
    if (!table || !column || !select) return;
    const mergeField = `{${table}: ${column}}`;
    if (select === 'subject') {
      setSubjectFields((prev) => (prev.includes(mergeField) ? prev : [...prev, mergeField]));
      setValue('subject', (subject || '') + mergeField);
    } else if (select === 'description') {
      setDescriptionFields((prev) => (prev.includes(mergeField) ? prev : [...prev, mergeField]));
      setValue('description', (description || '') + mergeField);
    }
  };

  // Remove chip and its value from the input
  const handleDeleteChip = (field, mergeField) => {
    if (field === 'subject') {
      setSubjectFields((prev) => prev.filter((f) => f !== mergeField));
      setValue('subject', (subject || '').replace(mergeField, ''));
    } else if (field === 'description') {
      setDescriptionFields((prev) => prev.filter((f) => f !== mergeField));
      setValue('description', (description || '').replace(mergeField, ''));
    }
  };

  return (
    <Box>
      <AccordionComponent
        control={control}
        tableOptions={tableOptions}
        columnOptions={columnOptions}
        selectOptions={selectOptions}
        handleAddMergeField={handleAddMergeField}
        addReferences={addReferences}
        handleAddReferences={handleAddReferences}
        subjectFields={subjectFields}
        handleDeleteChip={handleDeleteChip}
        descriptionFields={descriptionFields}
        select={select}
        table={table}
        column={column}
      />
      <Grid item xs={12} mt={2} mb={5}>
        <Stack spacing={2}>
          <CustomInputLabel>Field to be show in Excel</CustomInputLabel>
          <DefaultClientSubmissionTab />
        </Stack>
      </Grid>

      <AccordionComponentVendor
        control={control}
        tableOptions={tableOptions}
        columnOptions={columnOptions}
        selectOptions={selectOptions}
        handleAddMergeField={handleAddMergeField}
        addReferences={addReferences}
        handleAddReferences={handleAddReferences}
        subjectFields={subjectFields}
        handleDeleteChip={handleDeleteChip}
        descriptionFields={descriptionFields}
        select={select}
        table={table}
        column={column}
      />
    </Box>
  );
}

export default DefaultClientSubmission;
