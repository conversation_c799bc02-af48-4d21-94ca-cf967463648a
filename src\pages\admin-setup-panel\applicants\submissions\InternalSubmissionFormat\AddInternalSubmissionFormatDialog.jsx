import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Typography,
  IconButton,
  Button,
  Grid,
  Stack,
  FormControlLabel,
  Checkbox,
  Radio,
  RadioGroup,
  FormControl,
  Chip,
  Box,
  Divider
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm, Controller } from 'react-hook-form';
import CustomN<PERSON>Field from 'components/custom-components/CustomNameField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import ReactDraftAuto from './ReactDraftAuto';
import AddInternalSubmissionFormatTab from './AddInternalSubmissionFormatTab';

function AddInternalSubmissionFormatDialog({ open, onClose, onSave }) {
  const { control, handleSubmit, reset, setValue, watch } = useForm({
    defaultValues: {
      template_name: '',
      reply_to: '',
      format_style: 'default',
      table: '',
      column: '',
      select: '',
      email_subject: '',
      description: '',
      add_references: false,
      add_skills: false,
      add_ranking_summary: false,
      include_accept_reject: false,
      enable_template: false
    }
  });

  const [subjectFields, setSubjectFields] = React.useState([]);
  const [descriptionFields, setDescriptionFields] = React.useState([]);

  const tableOptions = [{ value: 'JobSeeker', label: 'Job Seeker' }];

  const columnOptions = [
    { value: 'applicant_id', label: 'Applicant ID' },
    { value: 'first_name', label: 'First Name' },
    { value: 'last_name', label: 'Last Name' },
    { value: 'middle_name', label: 'Middle Name' },
    { value: 'nick_name', label: 'Nick Name' },
    { value: 'consultant_name', label: 'Consultant Name' }
  ];

  const selectOptions = [
    { value: 'subject', label: 'Add to Subject' },
    { value: 'description', label: 'Add to Description' }
  ];

  const table = watch('table');
  const column = watch('column');
  const select = watch('select');
  const emailSubject = watch('email_subject');
  const description = watch('description');

  const handleAddMergeField = () => {
    if (!table || !column || !select) return;
    const mergeField = `{${table}: ${column}}`;
    if (select === 'subject') {
      setSubjectFields((prev) => (prev.includes(mergeField) ? prev : [...prev, mergeField]));
      setValue('email_subject', (emailSubject || '') + mergeField);
    } else if (select === 'description') {
      setDescriptionFields((prev) => (prev.includes(mergeField) ? prev : [...prev, mergeField]));
      setValue('description', (description || '') + mergeField);
    }
  };

  const handleDeleteChip = (field, mergeField) => {
    if (field === 'subject') {
      setSubjectFields((prev) => prev.filter((f) => f !== mergeField));
      setValue('email_subject', (emailSubject || '').replace(mergeField, ''));
    } else if (field === 'description') {
      setDescriptionFields((prev) => prev.filter((f) => f !== mergeField));
      setValue('description', (description || '').replace(mergeField, ''));
    }
  };

  React.useEffect(() => {
    if (!open) {
      reset();
      setSubjectFields([]);
      setDescriptionFields([]);
    }
  }, [open, reset]);

  const handleSave = (data) => {
    onSave(data);
    reset();
    setSubjectFields([]);
    setDescriptionFields([]);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth scroll="paper">
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h5">Add Internal Submission Format</Typography>
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        <Box component="form" onSubmit={handleSubmit(handleSave)} sx={{ mt: 1 }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="template_name">
                  Template Name<span style={{ color: 'red' }}>*</span>
                </CustomInputLabel>
                <CustomNameField
                  name="template_name"
                  control={control}
                  placeholder="Enter Template Name"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  rules={{ required: 'Template Name is required' }}
                />
              </Stack>
            </Grid>

            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="reply_to">Reply To</CustomInputLabel>
                <CustomNameField
                  name="reply_to"
                  control={control}
                  placeholder="Enter Reply To"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="reply_to">Clients</CustomInputLabel>
                <CustomNameField name="clients" control={control} placeholder="Enter" sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} />
              </Stack>
            </Grid>

            <Grid item xs={12}>
              <Grid item xs={12} sm={6}>
                <CustomInputLabel>Format Style</CustomInputLabel>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Controller
                  name="format_style"
                  control={control}
                  render={({ field }) => (
                    <RadioGroup row {...field}>
                      <FormControlLabel value="default" control={<Radio />} label="Default Format" />
                      <FormControlLabel value="tabular" control={<Radio />} label="Tabular Format" />
                    </RadioGroup>
                  )}
                />
              </Grid>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <CustomInputLabel>Select Table</CustomInputLabel>
              <CustomDropdownField
                name="table"
                control={control}
                placeholder="Select Table"
                options={tableOptions}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <CustomInputLabel>Select Column</CustomInputLabel>
              <CustomDropdownField
                name="column"
                control={control}
                placeholder="Select Column"
                options={columnOptions}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <CustomInputLabel>Select</CustomInputLabel>
              <CustomDropdownField
                name="select"
                control={control}
                placeholder="Select"
                options={selectOptions}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={2} sx={{ mt: 2.2 }}>
              <Button variant="contained" onClick={handleAddMergeField}>
                Add
              </Button>
            </Grid>

            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel>Email Subject</CustomInputLabel>
                <CustomNameField
                  name="email_subject"
                  control={control}
                  placeholder="Enter Email Subject"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
                <Box display="flex" flexWrap="wrap" gap={1}>
                  {subjectFields.map((field) => (
                    <Chip key={field} label={field} onDelete={() => handleDeleteChip('subject', field)} color="primary" size="small" />
                  ))}
                </Box>
              </Stack>
            </Grid>

            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel>Description</CustomInputLabel>
                <ReactDraftAuto
                  name="description"
                  control={control}
                  mergeField={select === 'description' && table && column ? `{${table}: ${column}}` : ''}
                />
                <Box display="flex" flexWrap="wrap" gap={1}>
                  {descriptionFields.map((field) => (
                    <Chip key={field} label={field} onDelete={() => handleDeleteChip('description', field)} color="primary" size="small" />
                  ))}
                </Box>
              </Stack>
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Controller
                    name="add_references"
                    control={control}
                    render={({ field: { value, onChange } }) => <Checkbox checked={value} onChange={(e) => onChange(e.target.checked)} />}
                  />
                }
                label="Add References (For Applicant Submissions Only)"
              />
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Controller
                    name="add_skills"
                    control={control}
                    render={({ field: { value, onChange } }) => <Checkbox checked={value} onChange={(e) => onChange(e.target.checked)} />}
                  />
                }
                label="Add Skills (For Applicant Submissions Only)"
              />
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Controller
                    name="add_ranking_summary"
                    control={control}
                    render={({ field: { value, onChange } }) => <Checkbox checked={value} onChange={(e) => onChange(e.target.checked)} />}
                  />
                }
                label="Add candidate ranking summary"
              />
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Controller
                    name="include_accept_reject"
                    control={control}
                    render={({ field: { value, onChange } }) => <Checkbox checked={value} onChange={(e) => onChange(e.target.checked)} />}
                  />
                }
                label="Include Accept and Reject buttons"
              />
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Controller
                    name="enable_template"
                    control={control}
                    render={({ field: { value, onChange } }) => <Checkbox checked={value} onChange={(e) => onChange(e.target.checked)} />}
                  />
                }
                label="Enable This Email Template"
              />
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={2}>
                <CustomInputLabel>Field to be show in Excel</CustomInputLabel>
                <AddInternalSubmissionFormatTab />
              </Stack>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button
          onClick={() => {
            reset();
            setSubjectFields([]);
            setDescriptionFields([]);
            onClose();
          }}
          size="small"
          variant="outlined"
        >
          Cancel
        </Button>
        <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(handleSave)}>
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default AddInternalSubmissionFormatDialog;
