import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem, Divider} from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import AddInternalSubmissionFormatDialog from './AddInternalSubmissionFormatDialog';
import EditInternalSubmissionFormatDialog from './EditInternalSubmissionFormatDialog';
import CopyInternalSubmissionFormatDialog from './CopyInternalSubmissionFormatDialog';

function InternalSubmissionFormat() {
  const [formats, setFormats] = useState([
    {
      id: 1,
      template_name: 'Interview Invite',
      email_subject: 'Interview Scheduled',
      default_template: true
    },
    {
      id: 2,
      template_name: 'Offer Letter',
      email_subject: 'Congratulations!',
      default_template: false
    },
    {
      id: 3,
      template_name: 'Rejection Notice',
      email_subject: 'Application Status Update',
      default_template: false
    },
    {
      id: 4,
      template_name: 'Reference Check',
      email_subject: 'Reference Request for Candidate',
      default_template: false
    },
    {
      id: 5,
      template_name: 'Onboarding Welcome',
      email_subject: 'Welcome to the Company!',
      default_template: false
    },
    {
      id: 6,
      template_name: 'Document Submission',
      email_subject: 'Please Submit Your Documents',
      default_template: false
    },
    {
      id: 7,
      template_name: 'Interview Reminder',
      email_subject: 'Reminder: Upcoming Interview',
      default_template: false
    },
    {
      id: 8,
      template_name: 'Assessment Invitation',
      email_subject: 'Online Assessment Link',
      default_template: false
    }
  ]);
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [openEditDialogs, setOpenEditDialogs] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState(null);
  const [selectedFormats, setSelectedFormats] = useState(null);
  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  const handleAddDialogOpen = () => setOpenAddDialog(true);
  const handleAddDialogClose = () => setOpenAddDialog(false);
  const handleEditDialogClose = () => setOpenEditDialog(false);
  const handleCloses = () => setOpenEditDialogs(false);

  const handleAddSave = (data) => {
    setFormats((prev) => [...prev, { id: prev.length + 1, ...data }]);
    handleAddDialogClose();
  };

  const handleEditSave = (updated) => {
    setFormats((prev) => prev.map((f) => (f.id === updated.id ? updated : f)));
    handleEditDialogClose();
    setSelectedFormat(null);
  };
  const handleCopySave = (updated) => {
    setFormats((prev) => prev.map((f) => (f.id === updated.id ? updated : f)));
    handleCloses();
    setSelectedFormats(null);
  };

  const ActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);
    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };
    const handleClose = () => setAnchorEl(null);
    const handleEdit = () => {
      setSelectedFormat(params.row);
      setOpenEditDialog(true);
      handleClose();
    };
    const handleCopy = () => {
      setSelectedFormats(params.row);
      setOpenEditDialogs(true);
    };
    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleCopy}>Copy</MenuItem>
        </Menu>
      </>
    );
  };

  const columns = [
    {
      field: 'template_name',
      headerName: 'TEMPLATE NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'email_subject',
      headerName: 'EMAIL SUBJECT',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'default_template',
      headerName: 'DEFAULT TEMPLATE',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => <Typography variant="body2">{params.value ? 'Yes' : 'No'}</Typography>
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <ActionCell params={params} />
    }
  ];

  return (
    <>
    <CustomCardHeader
           
            secondary={
              <Box display="flex" alignItems="center" gap={1}>
               <Button variant="outlined" size="small">
              Activities
            </Button>
            <Button variant="contained" size="small" color="primary" onClick={handleAddDialogOpen}>
              + Add
            </Button>
              </Box>
            }
            sx={{ mt: -2 }}
          />
         <Divider sx={{ mb: 1 }} />      
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={formats}
              columns={columns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={formats.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddInternalSubmissionFormatDialog open={openAddDialog} onClose={handleAddDialogClose} onSave={handleAddSave} />
      <EditInternalSubmissionFormatDialog
        open={openEditDialog}
        onClose={handleEditDialogClose}
        onSave={handleEditSave}
        format={selectedFormat}
      />
      <CopyInternalSubmissionFormatDialog open={openEditDialogs} onClose={handleCloses} onSave={handleCopySave} format={selectedFormats} />
    </>
  );
}

export default InternalSubmissionFormat;
