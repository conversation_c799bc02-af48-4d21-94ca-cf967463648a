import React, { useState } from 'react';
import { Box, Typography, Tabs, Tab, Card } from '@mui/material';
import MainCard from 'components/MainCard';
import DefaultClientSubmission from './DefaultClientSubmission';
import SubmissionSummary from './SubmissionSummary';
import InternalSubmissionFormat from './InternalSubmissionFormat';

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div role="tabpanel" hidden={value !== index} id={`simple-tabpanel-${index}`} aria-labelledby={`simple-tab-${index}`} {...other}>
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`
  };
}

function Submissions() {
  const [selectedTab, setSelectedTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  return (
    <MainCard  sx={{
          borderRadius: '0%',
          backgroundColor: 'white',
          '& .MuiInputLabel-root': { fontSize: '0.875rem' }
        }}>
          <Tabs
            Tabs value={selectedTab} onChange={handleTabChange} aria-label="submission-tabs" variant="scrollable" scrollButtons="auto"
            sx={{
              mt:-2,
              ml: -2,
              alignItems: 'flex-start',
              justifyContent: 'flex-start',
              borderBottom: 0.2,
              borderColor: 'divider'
            }}
            orientation="horizontal"
          > 
          <Tab label="Default Client Submission" {...a11yProps(0)} />
          <Tab label="Internal Submission Format" {...a11yProps(1)} />
          <Tab label="Submission Summary" {...a11yProps(1)} />
        </Tabs>
      
      <TabPanel value={selectedTab} index={0}>
        <DefaultClientSubmission />
      </TabPanel>
      <TabPanel value={selectedTab} index={1}>
        <InternalSubmissionFormat />
      </TabPanel>
      <TabPanel value={selectedTab} index={2}>
        <SubmissionSummary />
      </TabPanel>
    </MainCard>
  );
}

export default Submissions;
