import {
  Paper,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Box,
  Typography
} from '@mui/material';
import CustomTablePagination from 'components/custom-components/CustomPaginationItems';
import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';

const ActivityLog = ({ logs = [], page, setPage, rowsPerPage, setRowsPerPage }) => {
  const paginatedLogs = logs.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  return (
    <Box sx={{ mt: 4 }}>
      <Paper elevation={2} sx={{ p: 2 }}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell><strong>Date & Time</strong></TableCell>
              <TableCell><strong>Description</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedLogs.length > 0 ? (
              paginatedLogs.map((log, index) => (
                <TableRow key={index}>
                  <TableCell>{log.timestamp}</TableCell>
                  <TableCell>
                    {log.action === 'updated'
                        ? `Bench Age Configuration changed from '${log.from}' to '${log.to}' by ${log.user}`
                      : `Bench Age Configuration '${log.to}' was ${log.action} by ${log.user}`}
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={2} align="center">
                  No activity logs available.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>

        <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
          <RowsPerPageSelector
            getState={() => ({ pagination: { pageIndex: page, pageSize: rowsPerPage } })}
            setPageSize={(size) => {
              setRowsPerPage(size);
              setPage(0);
            }}
          />
          <CustomTablePagination
            setPageSize={(size) => {
              setRowsPerPage(size);
              setPage(0);
            }}
            setPageIndex={setPage}
            getState={() => ({ pagination: { pageIndex: page, pageSize: rowsPerPage } })}
            getPageCount={() => Math.ceil(logs.length / rowsPerPage)}
          />
        </Box>
      </Paper>
    </Box>
  );
};

export default ActivityLog;
