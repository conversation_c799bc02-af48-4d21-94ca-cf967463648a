import React, { useState } from 'react';
import { Box, Grid, Button, Checkbox, FormControlLabel, Radio, RadioGroup } from '@mui/material';
import MainCard from 'components/MainCard';
import { useForm, Controller, FormProvider } from 'react-hook-form';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import ActivityLog from './activitylog';

const statusOptions = [{ value: 'Active Bench' }, { value: 'Active/On Demand Bench' }, { value: 'Inactive Bench' }];
const userOptions = [{ value: 'Nagaraju' }, { value: '<PERSON><PERSON><PERSON><PERSON>' }, { value: 'Radhika G' }];
const roleOptions = [{ value: 'Hiring Manager' }, { value: 'HR Manager' }, { value: 'Technical Recruiter' }];

const notifyOptions = [
  { label: 'Sales Team Member', value: 'sales_team' },
  { label: 'Account Manager', value: 'account_manager' },
  { label: 'Specific Users', value: 'specific_users' },
  { label: 'Specific Roles', value: 'specific_roles' }
];
const notifyEndOptions = [
  { label: 'Sales Team Member', value: 'sales_team' },
  { label: 'Account Manager', value: 'account_manager' },
  { label: 'Bench Consultant', value: 'bench_consultant' },
  { label: 'Specific Users', value: 'specific_users' },
  { label: 'Specific Roles', value: 'specific_roles' }
];

function BenchAgeConfiguration() {
  const form = useForm({
    defaultValues: {
      default_days: '',
      status: [],
      bench_age_notification: '',
      email_notification_days: '',
      notify: '',
      specific_users: [],
      specific_roles: [],
      notify_end: false,
      notify_end_options: [],
      notify_end_specific_users: [],
      notify_end_specific_roles: []
    }
  });
  const { control, handleSubmit, watch, reset } = form;

  const [showActivities, setShowActivities] = useState(false);
  const [activityLogs, setActivityLogs] = useState([]);
  const [activityPage, setActivityPage] = useState(0);
  const [activityRowsPerPage, setActivityRowsPerPage] = useState(5);

  // Watchers for dynamic UI
  const notify = watch('notify', '');
  const notifyEnd = watch('notify_end', false);
  const notifyEndChecked = watch('notify_end_options', []);

  const onSubmit = (data) => {
    if (!data.default_days) {
      alert('Default Number of Days to Place a Consultant is required.');
      return;
    }
    if (!data.status) {
      alert('Status to be Considered is required.');
      return;
    }
    if (!data.bench_age_notification) {
      alert('Send a Bench Age Notification is required.');
      return;
    }
    if (!data.email_notification_days) {
      alert('Send Email Notifications Every is required.');
      return;
    }
    if (!data.notify) {
      alert('Please select one user/role to notify.');
      return;
    }
    if (data.notify === 'specific_users' && (!data.specific_users || data.specific_users.length === 0)) {
      alert('Please select at least one Specific User.');
      return;
    }
    if (data.notify === 'specific_roles' && (!data.specific_roles || data.specific_roles.length === 0)) {
      alert('Please select at least one Specific Role.');
      return;
    }
    if (notifyEnd) {
      if (notifyEndChecked.length === 0) {
        alert('Please select at least one user/role to notify when Bench age ends.');
        return;
      }
      if (notifyEndChecked.includes('specific_users') && (!data.notify_end_specific_users || data.notify_end_specific_users.length === 0)) {
        alert('Please select at least one Specific User for Bench age end notification.');
        return;
      }
      if (notifyEndChecked.includes('specific_roles') && (!data.notify_end_specific_roles || data.notify_end_specific_roles.length === 0)) {
        alert('Please select at least one Specific Role for Bench age end notification.');
        return;
      }
    }
    const newLog = {
      timestamp: new Date().toLocaleString(),
      action: 'saved',
      to: data.status,
      user: 'Jathin',
      data: { ...data }
    };
    setActivityLogs([newLog, ...activityLogs]);
    alert('Bench Age Configuration saved successfully!');
    reset();
  };

  return (
    <FormProvider {...form}>
      <MainCard
        title="Bench Age Configuration"
        secondary={
          <>
            <Button variant="outlined" size="small" onClick={() => setShowActivities((prev) => !prev)}>
              {showActivities ? 'Back' : 'Activities'}
            </Button>
            <Button sx={{ ml: 2 }} size="small" variant="contained" color="primary" type="submit">
              Save
            </Button>
          </>
        }
        sx={{ borderRadius: 2, backgroundColor: 'white', mt: 0.2 }}
      >
        {showActivities ? (
          <ActivityLog
            logs={activityLogs}
            page={activityPage}
            setPage={setActivityPage}
            rowsPerPage={activityRowsPerPage}
            setRowsPerPage={setActivityRowsPerPage}
          />
        ) : (
          <form onSubmit={handleSubmit(onSubmit)}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={5} xl={5} lg={4}>
                <CustomInputLabel>
                  Default Number of Days to Place a Consultant <span style={{ color: 'red' }}>*</span>
                </CustomInputLabel>
              </Grid>
              <Grid item xs={12} sm={5} xl={4} lg={4}>
                <Box display="flex" alignItems="center">
                  <Controller
                    name="default_days"
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <CustomNameField {...field} placeholder="Days" required sx={{ backgroundColor: 'rgba(248, 249, 250, 1)', mr: 1 }} />
                    )}
                  />
                  <span style={{ marginLeft: 8, color: '#888' }}>Days</span>
                </Box>
              </Grid>

              <Grid item xs={12} sm={5} xl={5} lg={4}>
                <CustomInputLabel>
                  Status to be Considered <span style={{ color: 'red' }}>*</span>
                </CustomInputLabel>
              </Grid>
              <Grid item xs={12} sm={5} xl={3.5} lg={4}>
                <CustomDropdownField
                  name="status"
                  control={control}
                  placeholder="Select"
                  options={statusOptions}
                  multiple
                  rules={{ required: 'Status is required' }}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>

              <Grid item xs={12} sm={5} xl={5} lg={4}>
                <CustomInputLabel>
                  Send a Bench Age Notification <span style={{ color: 'red' }}>*</span>
                </CustomInputLabel>
              </Grid>
              <Grid item xs={12} sm={5} xl={4.5} lg={4}>
                <Box display="flex" alignItems="center">
                  <Controller
                    name="bench_age_notification"
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <CustomNameField
                        {...field}
                        placeholder="Days Before it Ends"
                        required
                        sx={{ backgroundColor: 'rgba(248, 249, 250, 1)', mr: 1 }}
                      />
                    )}
                  />
                  <span style={{ marginLeft: 8, color: '#888' }}>Days Before it Ends</span>
                </Box>
              </Grid>

              <Grid item xs={12} sm={5} xl={5} lg={4}>
                <CustomInputLabel>
                  Send Email Notifications Every <span style={{ color: 'red' }}>*</span>
                </CustomInputLabel>
              </Grid>
              <Grid item xs={12} sm={5} xl={4} lg={4}>
                <Box display="flex" alignItems="center">
                  <Controller
                    name="email_notification_days"
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <CustomNameField {...field} placeholder="Days" required sx={{ backgroundColor: 'rgba(248, 249, 250, 1)', mr: 1 }} />
                    )}
                  />
                  <span style={{ marginLeft: 8, color: '#888' }}>Days</span>
                </Box>
              </Grid>

              <Grid item xs={12} sm={5} xl={5} lg={4}>
                <CustomInputLabel>Notify these users when Bench age is due to end</CustomInputLabel>
              </Grid>
              <Grid item xs={12} sm={5} xl={4} lg={4}>
                <Box display="flex" flexWrap="wrap" gap={2}>
                  {notifyOptions.map((opt) => (
                    <FormControlLabel
                      key={opt.value}
                      control={
                        <Controller
                          name="notify"
                          control={control}
                          render={({ field }) => (
                            <Checkbox
                              checked={Array.isArray(field.value) ? field.value.includes(opt.value) : field.value === opt.value}
                              onChange={(e) => {
                                if (Array.isArray(field.value)) {
                                  const checked = e.target.checked;
                                  const newValue = checked
                                    ? [...field.value, opt.value]
                                    : field.value.filter((v) => v !== opt.value);
                                  field.onChange(newValue);
                                } else {
                                  field.onChange(e.target.checked ? [opt.value] : []);
                                }
                              }}
                            />
                          )}
                        />
                      }
                      label={opt.label}
                    />
                  ))}
                </Box>

                {/* Show dropdowns for specific users/roles if checked */}
                {Array.isArray(form.getValues('notify')) && form.getValues('notify').includes('specific_users') && (
                  <Box mt={2}>
                    <CustomInputLabel>
                      Specific Users <span style={{ color: 'red' }}>*</span>
                    </CustomInputLabel>
                    <CustomDropdownField
                      name="specific_users"
                      control={control}
                      placeholder="Select"
                      options={userOptions}
                      multiple
                      sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                    />
                  </Box>
                )}
                {Array.isArray(form.getValues('notify')) && form.getValues('notify').includes('specific_roles') && (
                  <Box mt={2}>
                    <CustomInputLabel>
                      Specific Roles <span style={{ color: 'red' }}>*</span>
                    </CustomInputLabel>
                    <CustomDropdownField
                      name="specific_roles"
                      control={control}
                      placeholder="Select"
                      options={roleOptions}
                      multiple
                      sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                    />
                  </Box>
                )}
              </Grid>

              <Grid item xs={12} sm={5} xl={5} lg={4}>
                <CustomInputLabel>Notify when Bench age ends</CustomInputLabel>
              </Grid>
              <Grid item xs={12} sm={5} xl={4} lg={4}>
                <Controller
                  name="notify_end"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={<Checkbox checked={field.value} onChange={(e) => field.onChange(e.target.checked)} />}
                      label="Notify the below-selected users when the Bench age ends"
                    />
                  )}
                />
              </Grid>

              {notifyEnd && (
                <>
                  <Grid item xs={12} sm={5} xl={5} lg={4}>
                    <CustomInputLabel>Select users to notify</CustomInputLabel>
                  </Grid>
                  <Grid item xs={12} sm={5} xl={4} lg={4}>
                    <Box display="flex" flexWrap="wrap" gap={2}>
                      {notifyEndOptions.map((opt) => (
                        <FormControlLabel
                          key={opt.value}
                          control={
                            <Controller
                              name="notify_end_options"
                              control={control}
                              render={({ field }) => (
                                <Checkbox
                                  checked={field.value.includes(opt.value)}
                                  onChange={(e) => {
                                    const checked = e.target.checked;
                                    const newValue = checked ? [...field.value, opt.value] : field.value.filter((v) => v !== opt.value);
                                    field.onChange(newValue);
                                  }}
                                />
                              )}
                            />
                          }
                          label={opt.label}
                        />
                      ))}
                    </Box>

                    {notifyEndChecked.includes('specific_users') && (
                      <Box mt={2}>
                        <CustomInputLabel>
                          Specific Users <span style={{ color: 'red' }}>*</span>
                        </CustomInputLabel>
                        <CustomDropdownField
                          name="notify_end_specific_users"
                          control={control}
                          placeholder="Select"
                          options={userOptions}
                          multiple
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)', width: '300px' }}
                        />
                      </Box>
                    )}

                    {notifyEndChecked.includes('specific_roles') && (
                      <Box mt={2}>
                        <CustomInputLabel>
                          Specific Roles <span style={{ color: 'red' }}>*</span>
                        </CustomInputLabel>
                        <CustomDropdownField
                          name="notify_end_specific_roles"
                          control={control}
                          placeholder="Select"
                          options={roleOptions}
                          multiple
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)', width: '300px' }}
                        />
                      </Box>
                    )}
                  </Grid>
                </>
              )}
            </Grid>
          </form>
        )}
      </MainCard>
    </FormProvider>
  );
}

export default BenchAgeConfiguration;
