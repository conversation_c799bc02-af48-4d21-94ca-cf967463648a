import {
  Paper, Table, TableHead, TableBody, TableRow, TableCell, Box
} from '@mui/material';
import { useState } from 'react';
import CustomTablePagination from 'components/custom-components/CustomPaginationItems';
import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';

const ActivityLog = ({ logs }) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const paginatedLogs = logs.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  return (
    <Paper sx={{ mt: 2, p: 2 }}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell sx={{ fontWeight: 'bold' }}>Date & Time</TableCell>
            <TableCell sx={{ fontWeight: 'bold' }}>Description</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {paginatedLogs.map((log, index) => (
            <TableRow key={index}>
              <TableCell>{log.timestamp}</TableCell>
              <TableCell>
                {log.action === 'updated'
                  ? `Availability changed from '${log.from}' to '${log.name}' by ${log.user}`
                  : `Availability '${log.name}' was ${log.action} by ${log.user}`}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
        <RowsPerPageSelector
          getState={() => ({ pagination: { pageIndex: page, pageSize: rowsPerPage } })}
          setPageSize={(size) => {
            setRowsPerPage(size);
            setPage(0);
          }}
        />
        <CustomTablePagination
          setPageSize={(size) => {
            setRowsPerPage(size);
            setPage(0);
          }}
          setPageIndex={setPage}
          getState={() => ({ pagination: { pageIndex: page, pageSize: rowsPerPage } })}
          getPageCount={() => Math.ceil(logs.length / rowsPerPage)}
        />
      </Box>
    </Paper>
  );
};

export default ActivityLog;
