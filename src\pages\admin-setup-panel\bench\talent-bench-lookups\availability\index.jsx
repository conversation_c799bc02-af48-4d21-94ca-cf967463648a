import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddAvailabilityDialog from './add-availability';
import EditAvailabilityDialog from './edit-availability';
import ActivityLog from './activitylog';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';

function Availability() {
  const [availabilityDetails, setAvailabilityDetails] = useState([
    {
      id: 1,
      availability_name: '2 Weeks',
      created_by: 'Prudhvi <PERSON>',
      created_on: '2023-01-01',
      modified_by: '<PERSON><PERSON><PERSON><PERSON>',
      modified_on: '2023-01-01',
      status: 'Active'
    },  
    {
      id: 2,
      availability_name: '1 Week',
      created_by: '<PERSON><PERSON><PERSON><PERSON>',
      created_on: '2023-01-05',
      modified_by: '<PERSON><PERSON><PERSON><PERSON>',
      modified_on: '2023-01-05',
      status: 'Active'
    },
    {
      id: 3,
      availability_name: 'Immediately',
      created_by: 'Prudhvi Kanumuri',
      created_on: '2023-01-05',
      modified_by: 'Prudhvi Kanumuri',
      modified_on: '2023-01-05',
      status: 'Active'
    }
  ]);

  const { control } = useForm();

  const [openAddAvailabilityDialog, setOpenAddAvailabilityDialog] = useState(false);
  const [openEditAvailabilityDialog, setOpenEditAvailabilityDialog] = useState(false);
  const [selectedAvailability, setSelectedAvailability] = useState(null);
  const [logs, setLogs] = useState([]);
  const [showActivities, setShowActivities] = useState(false);

  const handleAddAvailabilityDialogOpen = () => {
    setOpenAddAvailabilityDialog(true);
  };

  const handleAddAvailabilityDialogClose = () => {
    setOpenAddAvailabilityDialog(false);
  };

  const handleAddAvailabilitySave = (data) => {
    const time = new Date().toLocaleString('en-GB');
    setAvailabilityDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'Prudhvi Kanumuri',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'Prudhvi Kanumuri',
        modified_on: new Date().toISOString().slice(0, 10),
        status: 'Active'
      }
    ]);
    setLogs((prev) => [
      ...prev,
      { action: 'created', name: data.availability_name, user: 'Prudhvi Kanumuri', timestamp: time }
    ]);
    handleAddAvailabilityDialogClose();
  };

  const handleEditAvailabilitySave = (updatedMode) => {
    const time = new Date().toLocaleString('en-GB');
    setAvailabilityDetails((prev) => prev.map((availability) => (availability.id === updatedMode.id ? updatedMode : availability)));
    setLogs((prev) => [
      ...prev,
      { action: 'updated', name: updatedMode.availability_name, from: selectedAvailability?.availability_name, user: 'Prudhvi Kanumuri', timestamp: time }
    ]);
    setOpenEditAvailabilityDialog(false);
    setSelectedAvailability(null);
  };

  const AvailabilityActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedAvailability(params.row);
      setOpenEditAvailabilityDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      const time = new Date().toLocaleString('en-GB');
      setLogs((prev) => [
        ...prev,
        { action: 'deleted', name: params.row.availability_name, user: 'Prudhvi Kanumuri', timestamp: time }
      ]);
      setAvailabilityDetails((prev) => prev.filter((availability) => availability.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const availabilityColumns = [
    {
      field: 'availability_name',
      headerName: 'AVAILABILITY NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_on',
      headerName: 'CREATED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <AvailabilityActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    // <MainCard
    //   title="Availability"
    //   sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
    // >
    <>
      {showActivities ? (
        <>
          <Box display="flex" justifyContent="flex-end">
            <Button onClick={() => setShowActivities(false)} variant="outlined" size="small">Back</Button>
          </Box>
          <ActivityLog logs={logs} />
        </>
      ) : (
        <>
          <CustomCardHeader
            secondary={
              <Box display="flex" alignItems="center" gap={1}>
                <Button variant="outlined" size="small" onClick={() => setShowActivities(true)}>
                  Activities
                </Button>
                <Button variant="contained" size="small" color="primary" onClick={handleAddAvailabilityDialogOpen}>
                  + Add
                </Button>
              </Box>
            }
            // sx={{ mb: 2 }}
          />
          <Divider sx={{ mb: 1 }} />
        
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Box sx={{ height: 300, width: '100%' }}>
                <CustomDataGrid
                  rows={availabilityDetails}
                  columns={availabilityColumns}
                  paginationModel={paginationModel}
                  onPaginationModelChange={setPaginationModel}
                  rowCount={availabilityDetails.length}
                />
              </Box>
            </Grid>
          </Grid>
          <AddAvailabilityDialog
            open={openAddAvailabilityDialog}
            onClose={handleAddAvailabilityDialogClose}
            onSave={handleAddAvailabilitySave}
          />
          <EditAvailabilityDialog
            open={openEditAvailabilityDialog}
            onClose={() => setOpenEditAvailabilityDialog(false)}
            onSave={handleEditAvailabilitySave}
            availability={selectedAvailability}
          />
        </>
      )}
    </>
  );
}

export default Availability; 