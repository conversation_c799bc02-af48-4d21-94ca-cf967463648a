import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddConsultantTypesDialog from './add-consultant-types';
import EditConsultantTypesDialog from './edit-consultant-types';
import ActivityLog from './activitylog';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';

function ConsultantTypes() {
  const [consultantTypesDetails, setConsultantTypesDetails] = useState([
    {
      id: 1,
      consultant_types_name: 'H1 Transfer',
      created_by: '<PERSON><PERSON><PERSON><PERSON>',
      created_on: '2023-01-01',
      modified_by: '<PERSON><PERSON><PERSON><PERSON>',
      modified_on: '2023-01-01',
      status: 'Active'
    },
    {
      id: 2,
      consultant_types_name: 'W2',
      created_by: '<PERSON><PERSON><PERSON><PERSON>',
      created_on: '2023-01-05',
      modified_by: 'Prudhvi Kanumuri',
      modified_on: '2023-01-05',
      status: 'Active'
    }
  ]);

  const { control } = useForm();

  const [openAddConsultantTypesDialog, setOpenAddConsultantTypesDialog] = useState(false);
  const [openEditConsultantTypesDialog, setOpenEditConsultantTypesDialog] = useState(false);
  const [selectedConsultantTypes, setSelectedConsultantTypes] = useState(null);
  const [logs, setLogs] = useState([]);
  const [showActivities, setShowActivities] = useState(false);

  const handleAddConsultantTypesDialogOpen = () => {
    setOpenAddConsultantTypesDialog(true);
  };

  const handleAddConsultantTypesDialogClose = () => {
    setOpenAddConsultantTypesDialog(false);
  };

  const handleAddConsultantTypesSave = (data) => {
    const time = new Date().toLocaleString('en-GB');
    setConsultantTypesDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'Prudhvi Kanumuri',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'Prudhvi Kanumuri',
        modified_on: new Date().toISOString().slice(0, 10),
        status: 'Active'
      }
    ]);
    setLogs((prev) => [
      ...prev,
      { action: 'created', name: data.consultant_types_name, user: 'Prudhvi Kanumuri', timestamp: time }
    ]);
    handleAddConsultantTypesDialogClose();
  };

  const handleEditConsultantTypesSave = (updatedMode) => {
    const time = new Date().toLocaleString('en-GB');
    setConsultantTypesDetails((prev) => prev.map((consultantType) => (consultantType.id === updatedMode.id ? updatedMode : consultantType)));
    setLogs((prev) => [
      ...prev,
      { action: 'updated', name: updatedMode.consultant_types_name, from: selectedConsultantTypes?.consultant_types_name, user: 'Prudhvi Kanumuri', timestamp: time }
    ]);
    setOpenEditConsultantTypesDialog(false);
    setSelectedConsultantTypes(null);
  };

  const ConsultantTypesActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedConsultantTypes(params.row);
      setOpenEditConsultantTypesDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      const time = new Date().toLocaleString('en-GB');
      setLogs((prev) => [
        ...prev,
        { action: 'deleted', name: params.row.consultant_types_name, user: 'Prudhvi Kanumuri', timestamp: time }
      ]);
      setConsultantTypesDetails((prev) => prev.filter((consultantType) => consultantType.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const consultantTypesColumns = [
    {
      field: 'consultant_types_name',
      headerName: 'CONSULTANT TYPES NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_on',
      headerName: 'CREATED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <ConsultantTypesActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <>
      {showActivities ? (
        <>
          <Box display="flex" justifyContent="flex-end">
            <Button onClick={() => setShowActivities(false)} variant="outlined" size="small">Back</Button>
          </Box>
          <ActivityLog logs={logs} />
        </>
      ) : (
        <>
          <CustomCardHeader
            secondary={
              <Box display="flex" alignItems="center" gap={1}>
                <Button variant="outlined" size="small" onClick={() => setShowActivities(true)}>
                  Activities
                </Button>
                <Button variant="contained" size="small" color="primary" onClick={handleAddConsultantTypesDialogOpen}>
                  + Add
                </Button>
              </Box>
            }
            // sx={{ mb: 2 }}
          />
          <Divider sx={{ mb: 1 }} />
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Box sx={{ height: 300, width: '100%' }}>
                <CustomDataGrid
                  rows={consultantTypesDetails}
                  columns={consultantTypesColumns}
                  paginationModel={paginationModel}
                  onPaginationModelChange={setPaginationModel}
                  rowCount={consultantTypesDetails.length}
                />
              </Box>
            </Grid>
          </Grid>
          <AddConsultantTypesDialog
            open={openAddConsultantTypesDialog}
            onClose={handleAddConsultantTypesDialogClose}
            onSave={handleAddConsultantTypesSave}
          />
          <EditConsultantTypesDialog
            open={openEditConsultantTypesDialog}
            onClose={() => setOpenEditConsultantTypesDialog(false)}
            onSave={handleEditConsultantTypesSave}
            consultantTypes={selectedConsultantTypes}
          />
        </>
      )}
    </>
  );
}

export default ConsultantTypes; 