import React, { useState } from 'react';
import { Box, Tabs, Tab } from '@mui/material';
import MainCard from 'components/MainCard';
import Availability from './availability';
import ConsultantTypes from './consultant-types';
import NonIntegratedJobBoards from './non-integrated-job-boards';
import RequiredDocuments from './required-documents';

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`
  };
}

function TalentBenchLookups() {
  const [selectedTab, setSelectedTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  return (
    <MainCard>
      <Tabs
        value={selectedTab}
        onChange={handleTabChange}
        aria-label="talent bench lookups tabs"
        sx={{
          mt: -2,
          ml: -2,
          alignItems: 'flex-start',
          justifyContent: 'flex-start',
          borderBottom: 0.2,
          borderColor: 'divider'
        }}
      >
        <Tab label="Availability" {...a11yProps(0)} />
        <Tab label="Consultant Types" {...a11yProps(1)} />
        <Tab label="Non Integrated Job Boards" {...a11yProps(2)} />
        <Tab label="Required Documents" {...a11yProps(3)} />
      </Tabs>

      <Box sx={{ p: 0 }}>
        {selectedTab === 0 && <Availability />}
        {selectedTab === 1 && <ConsultantTypes />}
        {selectedTab === 2 && <NonIntegratedJobBoards />}
        {selectedTab === 3 && <RequiredDocuments />}
      </Box>
    </MainCard>
  );
}

export default TalentBenchLookups;
