import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddNonIntegratedJobBoards from './add-non-integrated-job-boards';
import EditNonIntegratedJobBoards from './edit-non-integrated-job-boards';
import ActivityLog from './activitylog';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';

function NonIntegratedJobBoards() {
  const [nonIntegratedJobBoardsDetails, setNonIntegratedJobBoardsDetails] = useState([
    {
      id: 1,
      non_integrated_job_boards_name: 'CareerBuilder',
      created_by: '<PERSON><PERSON><PERSON><PERSON>',
      created_on: '2023-01-01',
      modified_by: '<PERSON><PERSON>h<PERSON>',
      modified_on: '2023-01-01',
      status: 'Active'
    },
    {
      id: 2,
      non_integrated_job_boards_name: 'Zip Recruiter',
      created_by: 'Prudhvi Kanumuri',
      created_on: '2023-01-05',
      modified_by: 'Prudhvi Kanumuri',
      modified_on: '2023-01-05',
      status: 'Active'
    },{
      id: 3,
      non_integrated_job_boards_name: 'Indeed',
      created_by: 'Prudhvi Kanumuri',
      created_on: '2023-01-01',
      modified_by: 'Prudhvi Kanumuri',
      modified_on: '2023-01-01',
      status: 'Active'
    },{
      id: 4,
      non_integrated_job_boards_name: 'Glassdoor',
      created_by: 'Prudhvi Kanumuri',
      created_on: '2023-01-01',
      modified_by: 'Prudhvi Kanumuri',
      modified_on: '2023-01-01',
      status: 'Active'
    },{
      id: 5,
      non_integrated_job_boards_name: 'Tech Fetch',
      created_by: 'Prudhvi Kanumuri',
      created_on: '2023-01-01',
      modified_by: 'Prudhvi Kanumuri',
      modified_on: '2023-01-01',
      status: 'Active'
    },{
      id: 6,
      non_integrated_job_boards_name: 'Monster',
      created_by: 'Prudhvi Kanumuri',
      created_on: '2023-01-01',
      modified_by: 'Prudhvi Kanumuri',
      modified_on: '2023-01-01',
      status: 'Active'
    },{
      id: 7,
      non_integrated_job_boards_name: 'Dice',
      created_by: 'Prudhvi Kanumuri',
      created_on: '2023-01-01',
      modified_by: 'Prudhvi Kanumuri',
      modified_on: '2023-01-01',
      status: 'Active'
    }
  ]);

  const { control } = useForm();

  const [openAddNonIntegratedJobBoardsDialog, setOpenAddNonIntegratedJobBoardsDialog] = useState(false);
  const [openEditNonIntegratedJobBoardsDialog, setOpenEditNonIntegratedJobBoardsDialog] = useState(false);
  const [selectedNonIntegratedJobBoards, setSelectedNonIntegratedJobBoards] = useState(null);
  const [logs, setLogs] = useState([]);
  const [showActivities, setShowActivities] = useState(false);

  const handleAddNonIntegratedJobBoardsDialogOpen = () => {
    setOpenAddNonIntegratedJobBoardsDialog(true);
  };

  const handleAddNonIntegratedJobBoardsDialogClose = () => {
    setOpenAddNonIntegratedJobBoardsDialog(false);
  };

  const handleAddNonIntegratedJobBoardsSave = (data) => {
    const time = new Date().toLocaleString('en-GB');
    setNonIntegratedJobBoardsDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'Prudhvi Kanumuri',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'Prudhvi Kanumuri',
        modified_on: new Date().toISOString().slice(0, 10),
        status: 'Active'
      }
    ]);
    setLogs((prev) => [
      ...prev,
      { action: 'created', name: data.non_integrated_job_boards_name, user: 'Prudhvi Kanumuri', timestamp: time }
    ]);
    handleAddNonIntegratedJobBoardsDialogClose();
  };

  const handleEditNonIntegratedJobBoardsSave = (updatedMode) => {
    const time = new Date().toLocaleString('en-GB');
    setNonIntegratedJobBoardsDetails((prev) => prev.map((jobType) => (jobType.id === updatedMode.id ? updatedMode : jobType)));
    setLogs((prev) => [
      ...prev,
      { action: 'updated', name: updatedMode.non_integrated_job_boards_name, from: selectedNonIntegratedJobBoards?.non_integrated_job_boards_name, user: 'Prudhvi Kanumuri', timestamp: time }
    ]);
    setOpenEditNonIntegratedJobBoardsDialog(false);
    setSelectedNonIntegratedJobBoards(null);
  };

  const NonIntegratedJobBoardsActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedNonIntegratedJobBoards(params.row);
      setOpenEditNonIntegratedJobBoardsDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      const time = new Date().toLocaleString('en-GB');
      setLogs((prev) => [
        ...prev,
        { action: 'deleted', name: params.row.non_integrated_job_boards_name, user: 'Prudhvi Kanumuri', timestamp: time }
      ]);
      setNonIntegratedJobBoardsDetails((prev) => prev.filter((jobType) => jobType.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const nonIntegratedJobBoardsColumns = [
    {
      field: 'non_integrated_job_boards_name',
      headerName: 'NON INTEGRATED JOB BOARDS NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_on',
      headerName: 'CREATED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <NonIntegratedJobBoardsActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <>
      {showActivities ? (
        <>
          <Box display="flex" justifyContent="flex-end">
            <Button onClick={() => setShowActivities(false)} variant="outlined" size="small">Back</Button>
          </Box>
          <ActivityLog logs={logs} />
        </>
      ) : (
        <>
          <CustomCardHeader
            secondary={
              <Box display="flex" alignItems="center" gap={1}>
                <Button variant="outlined" size="small" onClick={() => setShowActivities(true)}>
                  Activities
                </Button>
                <Button variant="contained" size="small" color="primary" onClick={handleAddNonIntegratedJobBoardsDialogOpen}>
                  + Add
                </Button>
              </Box>
            }
            // sx={{ mb: 2 }}
          />
          <Divider sx={{ mb: 1 }} />
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Box sx={{ height: 300, width: '100%' }}>
                <CustomDataGrid
                  rows={nonIntegratedJobBoardsDetails}
                  columns={nonIntegratedJobBoardsColumns}
                  paginationModel={paginationModel}
                  onPaginationModelChange={setPaginationModel}
                  rowCount={nonIntegratedJobBoardsDetails.length}
                />
              </Box>
            </Grid>
          </Grid>
          <AddNonIntegratedJobBoards
            open={openAddNonIntegratedJobBoardsDialog}
            onClose={handleAddNonIntegratedJobBoardsDialogClose}
            onSave={handleAddNonIntegratedJobBoardsSave}
          />
          <EditNonIntegratedJobBoards
            open={openEditNonIntegratedJobBoardsDialog}
            onClose={() => setOpenEditNonIntegratedJobBoardsDialog(false)}
            onSave={handleEditNonIntegratedJobBoardsSave}
            nonIntegratedJobBoards={selectedNonIntegratedJobBoards}
          />
        </>
      )}
    </>
  );
}

export default NonIntegratedJobBoards; 