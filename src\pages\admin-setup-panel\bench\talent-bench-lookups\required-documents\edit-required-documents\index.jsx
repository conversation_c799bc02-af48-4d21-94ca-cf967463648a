import React, { useEffect } from 'react';
import { <PERSON>er, Typo<PERSON>, Box, Divider, IconButton, Button, Grid, Stack, InputLabel } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm } from 'react-hook-form';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';

function EditRequiredDocumentsDialog({ open, onClose, onSave, requiredDocuments }) {
  const { control, handleSubmit, reset, setValue } = useForm();

  useEffect(() => {
    if (open && requiredDocuments) {
      setValue('required_documents_name', requiredDocuments.required_documents_name,
        setValue('consultant_types_name', requiredDocuments.consultant_types_name)
      );
    }
  }, [open,requiredDocuments, setValue],
    );

  const handleSave = (data) => {
    onSave({ ...requiredDocuments, ...data });
    reset();
  };

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box sx={{ width: { xs: '100%', sm: 400 }, height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Sticky Header */}
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">Edit Required Documents</Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <Divider />

         {/* Form */}
         <Box component="form" onSubmit={handleSubmit(handleSave)} sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <Stack spacing={1}>
                        <InputLabel htmlFor="consultant_types_name">Consultant Types Name</InputLabel>
                        <CustomDropdownField
                          name="consultant_types_name"
                          control={control}
                          options={[
                            { value: 'W2', label: 'W2' },
                            { value: 'H1 Transfer', label: 'H1 Transfer' }
                          ]}
                          placeholder="Select Consultant Type"
                          required
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)'}}
                        />
                      </Stack>
                          <Stack spacing={1}>
                        <InputLabel htmlFor="required_documents_name">Required Documents Name</InputLabel>
                        <CustomDropdownField
                          name="required_documents_name"
                          control={control}
                          options={[
                            { value: 'Resume', label: 'Resume' },
                            { value: 'Transcripts', label: 'Transcripts' },
                            { value: 'Driving License', label: 'Driving License' },
                            { value: 'Passport', label: 'Passport' },
                            { value: 'SSN', label: 'SSN' },
                            { value: 'EML File', label: 'EML File' }
                          ]}
                          placeholder="Select Required Document"
                          required
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)'}}
                        />
                      </Stack>
                    </Grid>
                  </Grid>
                </Box>

        {/* Footer */}
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            onClick={() => {
              reset();
              onClose();
            }}
            size="small"
            variant="outlined"
          >
            Cancel
          </Button>
          <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(handleSave)}>
            Save
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
}

export default EditRequiredDocumentsDialog;   