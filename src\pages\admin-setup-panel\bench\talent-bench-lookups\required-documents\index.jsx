import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddRequiredDocumentsDialog from './add-required-documents';
import EditRequiredDocumentsDialog from './edit-required-documents';
import ActivityLog from './activitylog';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';

function RequiredDocuments() {
  const [requiredDocumentsDetails, setRequiredDocumentsDetails] = useState([
    {
      id: 1,
      consultantType: 'H1 Transfer',
      required_documents_name: 'Resume',
      created_by: '<PERSON><PERSON><PERSON><PERSON>',
      created_on: '2023-01-01',
      modified_by: '<PERSON><PERSON><PERSON><PERSON>',
      modified_on: '2023-01-01',
      status: 'Active'
    },
    {
      id: 2,
      consultantType: 'W2',
      required_documents_name: 'Transcripts',
      created_by: 'Prudhvi Kanumuri',
      created_on: '2023-01-05',
      modified_by: 'Prudhvi Kanumuri',
      modified_on: '2023-01-05',
      status: 'Active'
    }
  ]);

  const { control } = useForm();

  const [openAddRequiredDocumentsDialog, setOpenAddRequiredDocumentsDialog] = useState(false);
  const [openEditRequiredDocumentsDialog, setOpenEditRequiredDocumentsDialog] = useState(false);
  const [selectedRequiredDocuments, setSelectedRequiredDocuments] = useState(null);
  const [logs, setLogs] = useState([]);
  const [showActivities, setShowActivities] = useState(false);

  const handleAddRequiredDocumentsDialogOpen = () => {
    setOpenAddRequiredDocumentsDialog(true);
  };

  const handleAddRequiredDocumentsDialogClose = () => {
    setOpenAddRequiredDocumentsDialog(false);
  };

  const handleAddRequiredDocumentsSave = (data) => {
    const time = new Date().toLocaleString('en-GB');
    setRequiredDocumentsDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        consultantType: data.consultant_types_name, 
        required_documents_name: data.required_documents_name,
        created_by: 'Prudhvi Kanumuri',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'Prudhvi Kanumuri',
        modified_on: new Date().toISOString().slice(0, 10),
        status: 'Active'
      }
    ]);
    setLogs((prev) => [
      ...prev,
      { action: 'created', name: data.required_documents_name, user: 'Prudhvi Kanumuri', timestamp: time }
    ]);
    handleAddRequiredDocumentsDialogClose();
  };

  const handleEditRequiredDocumentsSave = (updatedMode) => {
    const time = new Date().toLocaleString('en-GB');
    setRequiredDocumentsDetails((prev) => prev.map((requiredDocument) => (requiredDocument.id === updatedMode.id ? updatedMode : requiredDocument)));
    setLogs((prev) => [
      ...prev,
      { action: 'updated', name: updatedMode.required_documents_name, from: selectedRequiredDocuments?.required_documents_name, user: 'Prudhvi Kanumuri', timestamp: time }
    ]);
    setOpenEditRequiredDocumentsDialog(false);
    setSelectedRequiredDocuments(null);
  };

  const RequiredDocumentsActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedRequiredDocuments(params.row);
      setOpenEditRequiredDocumentsDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      const time = new Date().toLocaleString('en-GB');
      setLogs((prev) => [
        ...prev,
        { action: 'deleted', name: params.row.required_documents_name, user: 'Prudhvi Kanumuri', timestamp: time }
      ]);
      setRequiredDocumentsDetails((prev) => prev.filter((requiredDocument) => requiredDocument.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const requiredDocumentsColumns = [
    {
      field: 'consultantType',
      headerName: 'CONSULTANT TYPES NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'required_documents_name',
      headerName: 'REQUIRED DOCUMENTS NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_on',
      headerName: 'CREATED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <RequiredDocumentsActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <>
      {showActivities ? (
        <>
          <Box display="flex" justifyContent="flex-end">
            <Button onClick={() => setShowActivities(false)} variant="outlined" size="small">Back</Button>
          </Box>
          <ActivityLog logs={logs} />
        </>
      ) : (
        <>
          <CustomCardHeader
            secondary={
              <Box display="flex" alignItems="center" gap={1}>
                <Button variant="outlined" size="small" onClick={() => setShowActivities(true)}>
                  Activities
                </Button>
                <Button variant="contained" size="small" color="primary" onClick={handleAddRequiredDocumentsDialogOpen}>
                  + Add
                </Button>
              </Box>
            }
            // sx={{ mb: 2 }}
          />
          <Divider sx={{ mb: 1 }} />
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Box sx={{ height: 300, width: '100%' }}>
                <CustomDataGrid
                  rows={requiredDocumentsDetails}
                  columns={requiredDocumentsColumns}
                  paginationModel={paginationModel}
                  onPaginationModelChange={setPaginationModel}
                  rowCount={requiredDocumentsDetails.length}
                />
              </Box>
            </Grid>
          </Grid>
          <AddRequiredDocumentsDialog
            open={openAddRequiredDocumentsDialog}
            onClose={handleAddRequiredDocumentsDialogClose}
            onSave={handleAddRequiredDocumentsSave}
          />
          <EditRequiredDocumentsDialog
            open={openEditRequiredDocumentsDialog}
            onClose={() => setOpenEditRequiredDocumentsDialog(false)}
            onSave={handleEditRequiredDocumentsSave}
            requiredDocuments={selectedRequiredDocuments}
          />
        </>
      )}
    </>
  );
}

export default RequiredDocuments; 