import React, { useState } from 'react';
import {
  Box,
  Grid,
  Button,
  Radio,
  RadioGroup,
  FormControlLabel,
  Switch
} from '@mui/material';
import MainCard from 'components/MainCard';
import { useForm, Controller, FormProvider } from 'react-hook-form';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomFormHelperText from 'components/custom-components/CustomFormHelperText';
import ActivityLog from './activitylog';

const roleOptions = [
  { value: 'HR Manager (Role)' },
  { value: 'Hiring Manager (Role)' },
  { value: 'Technical Recruiter (Role)' },
  { value: 'Administrator (Role)' }
];
const userOptions = [
  { value: '<PERSON><PERSON>hvi Kanmuri (User)' },
  { value: 'Radhi<PERSON> G (User)' },
  { value: '<PERSON><PERSON><PERSON> (User)' }
];
const groupOptions = [
  { value: 'Nagaraju (Group)' },
  { value: '<PERSON>dhi<PERSON> G (Group)' },
  { value: '<PERSON><PERSON><PERSON><PERSON> (Group)' }
];
const additionalNotifiersOptions = [
  ...userOptions,
  ...groupOptions,
  ...roleOptions
];

function TalentBenchSettings() {
  const form = useForm({
    defaultValues: {
      starting_number: '',
      vendor_name_display: 'blur',
      vendor_roles: [],
      notify_applicant: false,
      notify_additional: [],
      notify_sales: false,
      additional_notifiers: []
    }
  });
  const { control, handleSubmit, watch, reset } = form;

  const vendorNameDisplay = watch('vendor_name_display', 'blur');

  // Activities state
  const [showActivities, setShowActivities] = useState(false);
  const [activityLogs, setActivityLogs] = useState([]);
  const [activityPage, setActivityPage] = useState(0);
  const [activityRowsPerPage, setActivityRowsPerPage] = useState(5);

  const onSubmit = (data) => {
    if (!data.starting_number) {
      alert('Starting Number is required.');
      return;
    }
    if (data.vendor_name_display === 'roles' && (!data.vendor_roles || data.vendor_roles.length === 0)) {
      alert('Please select at least one role for vendor name display.');
      return;
    }
    // Add activity log
    const newLog = {
      timestamp: new Date().toLocaleString(),
      action: 'saved',
      to: data.starting_number,
      user: 'Jathin', // Replace with actual user if available
      data: { ...data }
    };
    setActivityLogs([newLog, ...activityLogs]);
    alert('Talent Bench Settings saved successfully!');
    reset();
  };

  return (
    <FormProvider {...form}>
      <MainCard
        title="Talent Bench Settings"
        secondary={
          <>
            <Button variant="outlined" size="small" onClick={() => setShowActivities((prev) => !prev)}>
              {showActivities ? 'Back' : 'Activities'}
            </Button>
            <Button sx={{ ml: 2 }} size="small" variant="contained" color="primary" type="submit">
              Save
            </Button>
          </>
        }
        sx={{ borderRadius: 2, backgroundColor: 'white', mt: 0.2 }}
      >
        {showActivities ? (
          <ActivityLog
            logs={activityLogs}
            page={activityPage}
            setPage={setActivityPage}
            rowsPerPage={activityRowsPerPage}
            setRowsPerPage={setActivityRowsPerPage}
          />
        ) : (
          <form onSubmit={handleSubmit(onSubmit)}>
            <Grid container spacing={3}>
              {/* Starting Number */}
              <Grid item xs={12} sm={6}>
                <Box>
                  <CustomInputLabel>Starting Number <span style={{ color: 'red' }}>*</span></CustomInputLabel>
                  <CustomFormHelperText>
                    (Bench ID will be auto-incremented from this number when a bench profile is added to the system)
                  </CustomFormHelperText>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Controller
                  name="starting_number"
                  control={control}
                  rules={{ required: true }}
                  render={({ field }) => (
                    <CustomNameField {...field} placeholder="" required sx={{ backgroundColor: 'rgba(248, 249, 250, 1)', minWidth: 200 }} />
                  )}
                />
              </Grid>

              {/* Vendor name display */}
              <Grid item xs={12} sm={6}>
                <Box>
                  <CustomInputLabel>Vendor name display when a user doesn&apos;t have Vendor access</CustomInputLabel>
                  <CustomFormHelperText>
                    (Choose whether to show the vendor name or completely restrict access to that vendor in the Submission list)
                  </CustomFormHelperText>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Controller
                  name="vendor_name_display"
                  control={control}
                  render={({ field }) => (
                    <RadioGroup row {...field}>
                      <FormControlLabel value="blur" control={<Radio />} label="Blur the Vendor Name" />
                      <FormControlLabel value="roles" control={<Radio />} label="Display the Vendor Name for the Selected Roles" />
                    </RadioGroup>
                  )}
                />
                {vendorNameDisplay === 'roles' && (
                  <Box mt={2}>
                    <CustomDropdownField
                      name="vendor_roles"
                      control={control}
                      placeholder="Select Roles"
                      options={roleOptions}
                      multiple
                      sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                    />
                  </Box>
                )}
              </Grid>

              {/* Submission Notification to Applicant */}
              <Grid item xs={12} sm={6}>
                <Box>
                  <CustomInputLabel>Submission Notification to Applicant</CustomInputLabel>
                  <CustomFormHelperText>
                    (A pop-up appears after each submission asking whether to email the applicant)
                  </CustomFormHelperText>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Controller
                  name="notify_applicant"
                  control={control}
                  render={({ field }) => (
                    <Switch
                      checked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    />
                  )}
                />
              </Grid>

              {/* Submission Notification to Additional Notifiers */}
              <Grid item xs={12} sm={6}>
                <Box>
                  <CustomInputLabel>Submission Notification to Additional Notifiers</CustomInputLabel>
                  <CustomFormHelperText>
                    (These users are notified about submissions via email)
                  </CustomFormHelperText>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <CustomDropdownField
                  name="notify_additional"
                  control={control}
                  placeholder="Select Users/Roles/Groups"
                  options={[...roleOptions, ...userOptions, ...groupOptions]}
                  multiple
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>

              {/* Submission Notification to Sales Team */}
              <Grid item xs={12} sm={6}>
                <Box>
                  <CustomInputLabel>Submission Notification to Sales Team Members & Account Managers</CustomInputLabel>
                  <CustomFormHelperText>
                    (The Sales Team Members and Account Managers are notified about submissions via email)
                  </CustomFormHelperText>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Controller
                  name="notify_sales"
                  control={control}
                  render={({ field }) => (
                    <Switch
                      checked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    />
                  )}
                />
              </Grid>

              {/* Additional Notifiers */}
              <Grid item xs={12} sm={6}>
                <Box>
                  <CustomInputLabel>Additional Notifiers</CustomInputLabel>
                  <CustomFormHelperText>
                    (These additional users are notified when a Talent Bench profile is assigned via email)
                  </CustomFormHelperText>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <CustomDropdownField
                  name="additional_notifiers"
                  control={control}
                  placeholder="Select Additional Notifiers"
                  options={additionalNotifiersOptions}
                  multiple
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>
            </Grid>
          </form>
        )}
      </MainCard>
    </FormProvider>
  );
}

export default TalentBenchSettings;
