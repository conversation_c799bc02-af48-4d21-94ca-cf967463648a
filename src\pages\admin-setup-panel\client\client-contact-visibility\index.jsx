import React, { useState } from 'react';
import { Box, Switch, Checkbox, FormControlLabel, Button, FormControl, Grid, Divider } from '@mui/material';
import MainCard from 'components/MainCard';
import ActivityLog from './activitylog';
import CustomAllCharactersField from 'components/custom-components/CustomAllCharactersField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import { useForm } from 'react-hook-form';

const users = ['Jathin', 'Nagaraju', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON> G'];

const ClientContactVisibility = () => {
  const { control, handleSubmit, reset } = useForm({
    defaultValues: {
      requirementCount: '',
      submissions: '',
      interviews: '',
      closures: '',
      deadline: '',
      duration: ''
    }
  });

  const [assignTarget, setAssignTarget] = useState(false);
  const [sendNotification, setSendNotification] = useState([]);
  const [positionsCountEnabled, setPositionsCountEnabled] = useState(false);
  const [allowAccess, setAllowAccess] = useState(false);
  const [activityLogs, setActivityLogs] = useState([
    {
      timestamp: '2025-06-09 14:30',
      action: 'created',
      visibility: 'New',
      user: 'Jathin'
    }
  ]);
  const [activityPage, setActivityPage] = useState(0);
  const [activityRowsPerPage, setActivityRowsPerPage] = useState(5);
  const [showActivities, setShowActivities] = useState(false);

  const onSubmit = (data) => {
    if (!assignTarget) {
      alert('Please enable the target assignment switch before saving.');
      return;
    }

    if (!data.deadline) {
      alert('Please enter a deadline.');
      return;
    }

    if (!allowAccess && sendNotification.length === 0) {
      alert('Please select at least one protocol.');
      return;
    }

    const newLog = {
      timestamp: new Date().toISOString().slice(0, 16).replace('T', ' '),
      action: 'created',
      visibility: 'New',
      user: 'Jathin'
    };

    setActivityLogs([newLog, ...activityLogs]);
    setAssignTarget(false);
    reset();
    setAllowAccess(false);
    setSendNotification([]);
  };

  return (
    <MainCard
      title="Client Contact Visibility"
      secondary={
        <>
          <Button variant="outlined" size="small" onClick={() => setShowActivities(!showActivities)}>
            {showActivities ? 'Back' : 'Activities'}
          </Button>

          <Button sx={{ ml: 2 }} variant="contained" size="small" color="primary" type="submit">
            Save
          </Button>
        </>
      }
      sx={{
        borderRadius: '0%',
        backgroundColor: 'white',
        '& .MuiInputLabel-root': { fontSize: '0.875rem' },
        mt: 0.2
      }}
    >
      {showActivities ? (
        <ActivityLog
          logs={activityLogs}
          page={activityPage}
          setPage={setActivityPage}
          rowsPerPage={activityRowsPerPage}
          setRowsPerPage={setActivityRowsPerPage}
        />
      ) : (
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} sm={6} lg={6} xl={5}>
                  <CustomInputLabel>Assign Target to Sales Manager/User When Client Contacts are Created</CustomInputLabel>
                </Grid>
                <Grid item xs={12} sm={6} lg={6} xl={4}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={assignTarget}
                        onChange={(e) => setAssignTarget(e.target.checked)}
                      />
                    }
                    label=""
                    sx={{ ml: '-2px' }}
                  />
                </Grid>
              </Grid>
            </Grid>

            {assignTarget && (
              <>
                <Grid item xs={12}>
                  <Grid container alignItems="center" spacing={2}>
                    <Grid item xs={12} sm={6} lg={6} xl={5}>
                      <CustomInputLabel >Total Requirements Needed From Each Client Contact</CustomInputLabel>
                    </Grid>
                    <Grid item xs={12} sm={6} lg={6} xl={4}>
                      <CustomAllCharactersField
                        name="requirementCount"
                        control={control}
                        placeholder="Enter total requirements"
                        required
                        sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                      />
                    </Grid>
                  </Grid>
                </Grid>

                <Grid item xs={12}>
                  <Grid container alignItems="center" spacing={2}>
                    <Grid item xs={12} sm={6} lg={6} xl={5}>
                      <CustomInputLabel>Check this box if the system needs to consider positions in the count:</CustomInputLabel>
                    </Grid>
                    <Grid item xs={12} sm={6} lg={6} xl={4}>
                      <FormControlLabel
                        control={<Checkbox checked={positionsCountEnabled} onChange={(e) => setPositionsCountEnabled(e.target.checked)} />}
                        label=""
                      />
                    </Grid>
                  </Grid>
                </Grid>

                <Grid item xs={12}>
                  <Grid container alignItems="center" spacing={2}>
                    <Grid item xs={12} sm={6} lg={6} xl={5}>
                      <CustomInputLabel>Total Submissions:</CustomInputLabel>
                    </Grid>
                    <Grid item xs={12} sm={6} lg={6} xl={4}>
                      <CustomAllCharactersField
                        name="submissions"
                        control={control}
                        placeholder="Enter total submissions"
                        sx={{ width: '100%', backgroundColor: 'rgba(248, 249, 250, 1)' }}
                      />
                    </Grid>
                  </Grid>
                </Grid>

                <Grid item xs={12}>
                  <Grid container alignItems="center" spacing={2}>
                    <Grid item xs={12} sm={6} lg={6} xl={5}>
                      <CustomInputLabel>Total Interviews:</CustomInputLabel>
                    </Grid>
                    <Grid item xs={12} sm={6} lg={6} xl={4}>
                      <CustomAllCharactersField
                        name="interviews"
                        control={control}
                        placeholder="Enter total interviews"
                        sx={{ width: '100%', backgroundColor: 'rgba(248, 249, 250, 1)' }}
                      />
                    </Grid>
                  </Grid>
                </Grid>

                <Grid item xs={12}>
                  <Grid container alignItems="center" spacing={2}>
                    <Grid item xs={12} sm={6} lg={6} xl={5}>
                      <CustomInputLabel>Total Closures:</CustomInputLabel>
                    </Grid>
                    <Grid item xs={12} sm={6} lg={6} xl={4}>
                      <CustomAllCharactersField
                        name="closures"
                        control={control}
                        placeholder="Enter total closures"
                        sx={{ width: '100%', backgroundColor: 'rgba(248, 249, 250, 1)' }}
                      />
                    </Grid>
                  </Grid>
                </Grid>

                <Grid item xs={12}>
                  <Grid container alignItems="center" spacing={2}>
                    <Grid item xs={12} sm={6} lg={6} xl={5}>
                      <CustomInputLabel>Deadline to Meet Target:</CustomInputLabel>
                    </Grid>
                    <Grid item xs={12} sm={6} lg={6} xl={4}>
                      <Grid container spacing={1} sx={{ mt: 0.5 }}>
                        <Grid item xs={12} xl={8} lg={8} sm={8}>
                          <CustomAllCharactersField
                            name="deadline"
                            control={control}
                            placeholder="Enter deadline"
                            required
                            fullWidth
                            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                          />
                        </Grid>
                        <Grid item xs={12}xl={4} lg={4} sm={4}>
                          <CustomDropdownField
                            name="duration"
                            control={control}
                            placeholder="Select duration"
                            options={[{ value: 'Days' }, { value: 'Weeks' }, { value: 'Months' }]}
                            required
                            fullWidth
                            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>

                {/* System Protocol Section */}
                <Grid item xs={12}>
                  <Grid container alignItems="center" spacing={2}>
                    <Grid item xs={12} sm={6} lg={6} xl={5}>
                      <CustomInputLabel required>System Protocol if Target Isn't Reached</CustomInputLabel>
                    </Grid>
                    <Grid item xs={12} sm={6} lg={6} xl={7}>
                      <Grid container spacing={1} alignItems="center">
                        <Grid item xs={6} sm={6} md={4} lg={3} xl={3}>
                          <FormControlLabel
                            control={
                              <Checkbox checked={allowAccess} onChange={(e) => setAllowAccess(e.target.checked)} />
                            }
                            label="Allow Client Access to All Users"
                          />
                        </Grid>
                        <Grid item xs={6} sm={6} md={4} lg={3} xl={3}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={sendNotification.length > 0}
                                onChange={(e) => setSendNotification(e.target.checked ? [users[0]] : [])}
                              />
                            }
                            label="Send Notification"
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                  {/* Notification dropdown (remains below, only shows if checked) */}
                  {sendNotification.length > 0 && (
                    <Grid container alignItems="center" spacing={2} sx={{ mt: 1 }}>
                      <Grid item xs={12} sm={6} lg={6} xl={5} />
                      <Grid item xs={12} sm={6} lg={6} xl={4}>
                        <FormControl sx={{ width: '100%' }}>
                          <CustomDropdownField
                            name="notifyUsers"
                            control={control}
                            placeholder="Select users to notify"
                            options={users.map((name) => ({ value: name }))}
                            multiple
                            value={sendNotification}
                            onChange={(e) => setSendNotification(e.target.value)}
                            renderValue={(selected) => selected.join(', ')}
                            label=""
                            sx={{ width: '100%', backgroundColor: 'rgba(248, 249, 250, 1)' }}
                          />
                        </FormControl>
                      </Grid>
                    </Grid>
                  )}
                </Grid>
              </>
            )}
          </Grid>

          {/* <Box
            sx={{
              position: 'fixed',
              bottom: 0,
              left: 362,
              right: 40,
              bgcolor: 'white',
              boxShadow: '0 -2px 10px rgba(0, 0, 0, 0.1)',
              p: 2,
              display: 'flex',
              justifyContent: 'center',
              zIndex: 10
            }}
          >
          </Box> */}
        </form>
      )}
    </MainCard>
  );
};

export default ClientContactVisibility;
