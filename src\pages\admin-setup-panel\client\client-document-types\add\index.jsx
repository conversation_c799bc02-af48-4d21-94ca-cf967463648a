import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  Divider,
  IconButton,
  Button,
  Grid,
  <PERSON>ack,
  FormControlLabel,
  Radio,
  RadioGroup,
  Switch
} from '@mui/material';
import { Close } from '@mui/icons-material';
import CustomAllCharactersField from 'components/custom-components/CustomAllCharactersField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import { useForm, Controller } from 'react-hook-form';

const roles = ['Nagaraj<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Radhi<PERSON> G'];
const businessUnit = ['Administrator', 'Hiring Manager', 'HR Manager', 'Technical Recruiter'];

const AddDocumentType = ({ open, onClose, onSave, isEdit = false, initialData = {} }) => {
  const { control, handleSubmit, setValue, watch, reset } = useForm({
    defaultValues: {
      documentType: initialData.documentType || '',
      restrictToRoles: initialData.businessUnit || [],
      isMandatory: initialData.isMandatory || 'no',
      sendExpiryNotification: initialData.sendExpiryNotification || false,
      notificationFrequency: initialData.notificationFrequency || '',
      notificationStopCondition: initialData.notificationStopCondition || '',
      notificationStopValue: initialData.notificationStopValue || '',
      notificationRecipients: initialData.notificationRecipients || [],
      expiryStart: initialData.expiryStart || '',
      expiryFrequency: initialData.expiryFrequency || '',
      expiryRecipients: initialData.expiryRecipients || []
    }
  });

  const formValues = watch();
  const showNotificationConfig = formValues.isMandatory === 'yesButLater';

  const handleSave = (data) => {
    onSave(data);
    reset();
  };

  React.useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box sx={{ width: { xs: '100%', sm: 400 }, height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Sticky Header */}
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">{isEdit ? 'Edit Document Type' : 'Add Document Type'}</Typography>
            <IconButton onClick={onClose}>
              <Close />
            </IconButton>
          </Box>
        </Box>

        <Divider />

        {/* Form */}
        <Box component="form" onSubmit={handleSubmit(handleSave)} sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
          <Grid container spacing={3}>
            {/* Document Type */}
            <Grid item xs={12}>
              <Stack spacing={1}>
                <Typography>Document Type *</Typography>
                <CustomAllCharactersField
                  name="documentType"
                  control={control}
                  required
                  placeholder="Enter document type"
                  sx={{ width: '100%', backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            {/* Restrict document to Roles */}
            <Grid item xs={12}>
              <Stack spacing={1}>
                <Typography>Restrict document to Roles</Typography>
                <CustomDropdownField
                  name="restrictToRoles"
                  control={control}
                  options={businessUnit.map((r) => ({ value: r, label: r }))}
                  multiple
                  placeholder="Select roles"
                  renderValue={(selected) => selected.join(', ')}
                  sx={{ width: '100%', backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            {/* Is Mandatory */}
            <Grid item xs={12}>
              <Stack spacing={1}>
                <Typography>Is Mandatory</Typography>
                <RadioGroup row name="isMandatory" value={formValues.isMandatory} onChange={(e) => setValue('isMandatory', e.target.value)}>
                  <FormControlLabel value="no" control={<Radio />} label="No" />
                  <FormControlLabel value="yes" control={<Radio />} label="Yes" />
                  <FormControlLabel value="yesButLater" control={<Radio />} label="Yes, But Can Be Added Later" />
                </RadioGroup>
              </Stack>
            </Grid>

            {/* Configure notification for missing documents */}
            {showNotificationConfig && (
              <>
                <Grid item xs={12}>
                  <Divider />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle1">Configure notification for missing documents</Typography>
                </Grid>

                {/* Notification Frequency */}
                <Grid item xs={12}>
                  <Stack spacing={1}>
                    <Typography>Notification Frequency</Typography>
                    <Box display="flex" alignItems="center" gap={2}>
                      <CustomAllCharactersField
                        name="notificationFrequency"
                        control={control}
                        type="number"
                        placeholder="Enter frequency"
                        sx={{ width: '200px', backgroundColor: 'rgba(248, 249, 250, 1)' }}
                      />
                      <Typography>Days</Typography>
                    </Box>
                  </Stack>
                </Grid>

                {/* When do you want to stop notifications? */}
                <Grid item xs={12}>
                  <Stack spacing={1}>
                    <Typography>When do you want to stop notifications?</Typography>
                    <Box display="flex" alignItems="center" gap={2}>
                      <CustomDropdownField
                        name="notificationStopCondition"
                        control={control}
                        placeholder="Select condition"
                        options={[
                          { value: 'after', label: 'After Specific no.of reminders' },
                          { value: 'uploaded', label: 'After uploading the required documents' }
                        ]}
                        sx={{ width: '300px', backgroundColor: 'rgba(248, 249, 250, 1)' }}
                      />
                      <CustomAllCharactersField
                        name="notificationStopValue"
                        control={control}
                        type="number"
                        placeholder="Enter value"
                        sx={{ width: '100px', backgroundColor: 'rgba(248, 249, 250, 1)' }}
                      />
                      <Typography>Reminders</Typography>
                    </Box>
                  </Stack>
                </Grid>

                {/* Notifications to */}
                <Grid item xs={12}>
                  <Stack spacing={1}>
                    <Typography>Notifications to (Sends notification to upload the document)</Typography>
                    <CustomDropdownField
                      name="notificationRecipients"
                      control={control}
                      placeholder="Select recipients"
                      options={roles.map((r) => ({ value: r, label: r }))}
                      multiple
                      renderValue={(selected) => selected.join(', ')}
                      sx={{ width: '100%', backgroundColor: 'rgba(248, 249, 250, 1)' }}
                    />
                  </Stack>
                </Grid>
              </>
            )}

            {/* Send Expiry Notification */}
            <Grid item xs={12}>
              <Stack spacing={1}>
                <Typography>Send Expiry Notification</Typography>
                <FormControlLabel
                  control={
                    <Controller
                      name="sendExpiryNotification"
                      control={control}
                      render={({ field }) => <Switch checked={field.value} onChange={(e) => field.onChange(e.target.checked)} />}
                    />
                  }
                  label=""
                />
              </Stack>
            </Grid>

            {/* Expiry notification details */}
            {formValues.sendExpiryNotification && (
              <>
                {/* Start Notifications */}
                <Grid item xs={12}>
                  <Stack spacing={1}>
                    <Typography>Start Notifications</Typography>
                    <Box display="flex" alignItems="center" gap={2}>
                      <CustomAllCharactersField
                        name="expiryStart"
                        control={control}
                        type="number"
                        placeholder="Enter days"
                        sx={{ width: '200px', backgroundColor: 'rgba(248, 249, 250, 1)' }}
                      />
                      <Typography>Days Before Expiry Date</Typography>
                    </Box>
                  </Stack>
                </Grid>

                {/* Notification Frequency */}
                <Grid item xs={12}>
                  <Stack spacing={1}>
                    <Typography>Notification Frequency</Typography>
                    <Box display="flex" alignItems="center" gap={2}>
                      <CustomAllCharactersField
                        name="expiryFrequency"
                        control={control}
                        type="number"
                        placeholder="Enter frequency"
                        sx={{ width: '200px', backgroundColor: 'rgba(248, 249, 250, 1)' }}
                      />
                      <Typography>Days</Typography>
                    </Box>
                  </Stack>
                </Grid>

                {/* Notify User(s) */}
                <Grid item xs={12}>
                  <Stack spacing={1}>
                    <Typography>Notify User(s)</Typography>
                    <CustomDropdownField
                      name="expiryRecipients"
                      control={control}
                      placeholder="Select recipients"
                      options={roles.map((role) => ({ value: role, label: role }))}
                      multiple
                      renderValue={(selected) => selected.join(', ')}
                      sx={{ width: '100%', backgroundColor: 'rgba(248, 249, 250, 1)' }}
                    />
                  </Stack>
                </Grid>
              </>
            )}
          </Grid>
        </Box>

        {/* Footer */}
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            onClick={() => {
              reset();
              onClose();
            }}
            size="small"
            variant="outlined"
          >
            Cancel
          </Button>
          <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(handleSave)}>
            {isEdit ? 'Update' : 'Save'}
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
};

export default AddDocumentType;
