import { I<PERSON><PERSON><PERSON><PERSON>, Typo<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>u, <PERSON>u<PERSON><PERSON>, But<PERSON>, Divider } from '@mui/material';
import { MoreVert } from '@mui/icons-material';
import { useState } from 'react';
import AddDocumentType from './add';
import EditDocumentType from './edit';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';

const ClientDocumentTypes = () => {
  const [documentTypes, setDocumentTypes] = useState([
    {
      id: 1,
      documentType: 'PAN Card',
      businessUnit: 'HR',
      restrictToRoles: ['Employee'],
      isMandatory: 'yes',
      sendExpiryNotification: true,
      notificationFrequency: 7,
      notificationStopCondition: 'after',
      notificationStopValue: 3,
      notificationRecipients: ['Employee', 'HR'],
      createdBy: 'Admin User',
      modifiedBy: 'Admin User',
      lastModified: '2025-06-10T10:30:00Z'
    },
    {
      id: 2,
      documentType: 'Passport',
      businessUnit: 'HR',
      restrictToRoles: ['Employee', 'Client'],
      isMandatory: 'yesButLater',
      sendExpiryNotification: false,
      notificationFrequency: 14,
      notificationStopCondition: 'after',
      notificationStopValue: 5,
      notificationRecipients: ['Employee', 'Manager', 'HR'],
      createdBy: 'Admin User',
      modifiedBy: 'Admin User',
      lastModified: '2025-06-09T15:45:00Z'
    }
  ]);

  const [logs, setLogs] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [editingDoc, setEditingDoc] = useState(null);
  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });
  const [showActivities, setShowActivities] = useState(false);

  const filteredDocumentTypes = documentTypes.filter((doc) => doc.documentType.toLowerCase().includes(searchTerm.toLowerCase()));

  const handleSave = (formData) => {
    const time = new Date().toISOString();
    const currentUser = 'Current User';

    if (editingDoc) {
      setDocumentTypes(
        documentTypes.map((doc) =>
          doc.id === editingDoc.id
            ? {
                ...formData,
                id: editingDoc.id,
                lastModified: time,
                createdBy: doc.createdBy,
                modifiedBy: currentUser
              }
            : doc
        )
      );
    } else {
      const newDoc = {
        ...formData,
        id: Math.max(0, ...documentTypes.map((doc) => doc.id)) + 1,
        createdBy: currentUser,
        modifiedBy: currentUser,
        lastModified: time
      };
      setDocumentTypes([newDoc, ...documentTypes]);
    }
    setLogs([
      ...logs,
      {
        action: editingDoc ? 'updated' : 'created',
        name: formData.documentType,
        user: currentUser,
        timestamp: time
      }
    ]);
    setEditingDoc(null);
    setDrawerOpen(false);
  };

  const handleDelete = (id) => {
    const time = new Date().toISOString();
    setLogs([
      ...logs,
      {
        action: 'deleted',
        name: documentTypes.find((doc) => doc.id === id).documentType,
        user: 'Current User',
        timestamp: time
      }
    ]);
    setDocumentTypes(documentTypes.filter((doc) => doc.id !== id));
  };

  const DocumentActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setEditingDoc(params.row);
      setDrawerOpen(true);
      handleClose();
    };

    const handleDeleteAction = () => {
      handleDelete(params.row.id);
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVert />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDeleteAction}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const columns = [
    {
      field: 'documentType',
      headerName: 'DOCUMENT TYPE',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value || ''}>
          <Typography variant="body2">{params.value || '-'}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'restrictToRoles',
      headerName: 'BUSINESS UNITS',
      flex: 1,
      minWidth: 150,
      valueGetter: (params) => params.row?.restrictToRoles?.join(', ') || '-',
      renderCell: (params) => (
        <Tooltip title={params.value || ''}>
          <Typography variant="body2">{params.value || '-'}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'isMandatory',
      headerName: 'IS MANDATORY',
      flex: 1,
      minWidth: 150,
      valueGetter: (params) => {
        const value = params.row?.isMandatory;
        if (value === 'yes') return 'Yes';
        if (value === 'no') return 'No';
        return 'Yes, But Can Be Added Later';
      },
      renderCell: (params) => (
        <Tooltip title={params.value || ''}>
          <Typography variant="body2">{params.value || '-'}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'sendExpiryNotification',
      headerName: 'EXPIRY NOTIFICATION',
      flex: 1,
      minWidth: 150,
      valueGetter: (params) => (params.row?.sendExpiryNotification ? 'Yes' : 'No'),
      renderCell: (params) => (
        <Tooltip title={params.value || ''}>
          <Typography variant="body2">{params.value || '-'}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'createdBy',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value || ''}>
          <Typography variant="body2">{params.value || '-'}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modifiedBy',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value || ''}>
          <Typography variant="body2">{params.value || '-'}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'lastModified',
      headerName: 'LAST MODIFIED',
      flex: 1,
      minWidth: 150,
      valueGetter: (params) => new Date(params.row?.lastModified).toLocaleString(),
      renderCell: (params) => (
        <Tooltip title={params.value || ''}>
          <Typography variant="body2">{params.value || '-'}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 1,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <DocumentActionCell params={params} />
    }
  ];

  return (
    <MainCard title="Document Types">
      <CustomCardHeader
        secondary={
          <>
            <Button variant="outlined" size="small" onClick={() => setShowActivities(true)} sx={{ mr: 2 }}>
              Activities
            </Button>
            <Button
              variant="contained"
              size="small"
              onClick={() => {
                setEditingDoc(null);
                setDrawerOpen(true);
              }}
            >
              + Add
            </Button>
          </>
        }
        searchTerm={searchTerm}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
      />
       <Divider sx={{ mb: 1 }} />
      <CustomDataGrid
        rows={filteredDocumentTypes}
        columns={columns}
        paginationModel={paginationModel}
        onPaginationModelChange={setPaginationModel}
        pageSizeOptions={[10, 25, 50]}
        autoHeight
        disableColumnMenu
        hideFooterSelectedRowCount
      />
      <AddDocumentType open={drawerOpen && !editingDoc} onClose={() => setDrawerOpen(false)} onSave={handleSave} />
      <EditDocumentType open={drawerOpen && editingDoc} onClose={() => setDrawerOpen(false)} onSave={handleSave} initialData={editingDoc} />
    </MainCard>
  );
};

export default ClientDocumentTypes;
