import React, { useState } from 'react';
import { Box, Button, Grid, <PERSON><PERSON><PERSON>, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddClientCategory from './add';
import EditClientCategory from './edit';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';

const ClientCategories = () => {
  const [categories, setCategories] = useState([
    {
      id: 1,
      name: 'Tier 1',
      createdBy: '<PERSON><PERSON>h<PERSON>',
      modifiedBy: '<PERSON><PERSON>h<PERSON>n<PERSON>',
      lastModified: '2025-06-06 18:00:00'
    },
    {
      id: 2,
      name: 'Direct',
      createdBy: '<PERSON><PERSON><PERSON><PERSON>',
      modifiedBy: '<PERSON><PERSON><PERSON><PERSON>',
      lastModified: '2025-06-06 19:00:00'
    }
  ]);

  const { control } = useForm();

  const [openAddClientCategoryDialog, setOpenAddClientCategoryDialog] = useState(false);
  const [openEditClientCategoryDialog, setOpenEditClientCategoryDialog] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [showActivities, setShowActivities] = useState(false);

  const handleAddClientCategoryDialogOpen = () => {
    setOpenAddClientCategoryDialog(true);
  };

  const handleAddClientCategoryDialogClose = () => {
    setOpenAddClientCategoryDialog(false);
  };

  const handleAddClientCategorySave = (data) => {
    setCategories((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        createdBy: 'User',
        modifiedBy: 'User',
        lastModified: new Date().toISOString().slice(0, 10)
      }
    ]);
    handleAddClientCategoryDialogClose();
  };

  const handleEditClientCategorySave = (updatedCategory) => {
    setCategories((prev) => prev.map((category) => (category.id === updatedCategory.id ? updatedCategory : category)));
    setOpenEditClientCategoryDialog(false);
    setSelectedCategory(null);
  };

  const ClientCategoryActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedCategory(params.row);
      setOpenEditClientCategoryDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete client category:', params.row);
      setCategories((prev) => prev.filter((category) => category.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const clientCategoryColumns = [
    {
      field: 'name',
      headerName: 'CLIENT CATEGORY',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <p>{params.value}</p>
        </Tooltip>
      )
    },
    {
      field: 'createdBy',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <p>{params.value}</p>
        </Tooltip>
      )
    },
    {
      field: 'modifiedBy',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <p>{params.value}</p>
        </Tooltip>
      )
    },
    {
      field: 'lastModified',
      headerName: 'LAST MODIFIED',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <p>{params.value}</p>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <ClientCategoryActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  const filteredCategories = categories.filter((cat) =>
    cat.name.toLowerCase().includes(control._formValues.search?.toLowerCase() || '')
  );

  return (
    // <MainCard
    //   // title="Client Categories"
    //   sx={{
    //     borderRadius: '1%',
    //     backgroundColor: 'white',
    //     '& .MuiInputLabel-root': { fontSize: '0.875rem' }
    //   }}
    // >
    <>
      <CustomCardHeader
        control={control}
        name="search"
        placeholder="Search Client Category"
        secondary={
          <Box display="flex" alignItems="center" gap={1}>
            <Button variant="outlined" size="small" onClick={() => setShowActivities(!showActivities)}>
              {showActivities ? 'Back' : 'Activities'}
            </Button>
            <Button variant="contained" size="small" color="primary" onClick={handleAddClientCategoryDialogOpen}>
              + Add
            </Button>
          </Box>
        }
        // sx={{ mb: 2 }}
      />
       <Divider sx={{ mb: 1 }} />
      {showActivities ? (
        null
      ) : (
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box sx={{ height: 300, width: '100%' }}>
              <CustomDataGrid
                rows={filteredCategories}
                columns={clientCategoryColumns}
                paginationModel={paginationModel}
                onPaginationModelChange={setPaginationModel}
                rowCount={filteredCategories.length}
              />
            </Box>
          </Grid>
        </Grid>
      )}

      <AddClientCategory
        open={openAddClientCategoryDialog}
        onClose={handleAddClientCategoryDialogClose}
        onSave={handleAddClientCategorySave}
      />
      <EditClientCategory
        open={openEditClientCategoryDialog}
        onClose={() => setOpenEditClientCategoryDialog(false)}
        onSave={handleEditClientCategorySave}
        category={selectedCategory}
      />
      </>
    // </MainCard>
  );
};

export default ClientCategories;
