import React, { useState } from 'react';
import { <PERSON>, Button, Grid, <PERSON><PERSON><PERSON>, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddClientContactRejectionReason from './add';
import EditClientContactRejectionReason from './edit';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';

const ClientContactRejectionReasons = () => {
  const [reasons, setReasons] = useState([
    {
      id: 1,
      name: 'Duplicate Client',
      createdBy: 'Prudhvi Kan<PERSON>ri',
      modifiedBy: 'Prudhvi Kanmuri',
      lastModified: '2025-06-06 18:00:00'
    },
    {
      id: 2,
      name: 'Not Interested',
      createdBy: '<PERSON><PERSON><PERSON><PERSON>',
      modifiedBy: '<PERSON><PERSON><PERSON><PERSON>',
      lastModified: '2025-06-06 19:00:00'
    }
  ]);

  const { control } = useForm();

  const [openAddReasonDialog, setOpenAddReasonDialog] = useState(false);
  const [openEditReasonDialog, setOpenEditReasonDialog] = useState(false);
  const [selectedReason, setSelectedReason] = useState(null);
  const [showActivities, setShowActivities] = useState(false);

  const handleAddReasonDialogOpen = () => {
    setOpenAddReasonDialog(true);
  };

  const handleAddReasonDialogClose = () => {
    setOpenAddReasonDialog(false);
  };

  const handleAddReasonSave = (data) => {
    setReasons((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        createdBy: 'User',
        modifiedBy: 'User',
        lastModified: new Date().toISOString().slice(0, 10)
      }
    ]);
    handleAddReasonDialogClose();
  };

  const handleEditReasonSave = (updatedReason) => {
    setReasons((prev) => prev.map((reason) => (reason.id === updatedReason.id ? updatedReason : reason)));
    setOpenEditReasonDialog(false);
    setSelectedReason(null);
  };

  const ClientContactRejectionReasonActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedReason(params.row);
      setOpenEditReasonDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete client contact rejection reason:', params.row);
      setReasons((prev) => prev.filter((reason) => reason.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const clientContactRejectionReasonColumns = [
    {
      field: 'name',
      headerName: 'REASON NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <p>{params.value}</p>
        </Tooltip>
      )
    },
    {
      field: 'createdBy',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <p>{params.value}</p>
        </Tooltip>
      )
    },
    {
      field: 'modifiedBy',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <p>{params.value}</p>
        </Tooltip>
      )
    },
    {
      field: 'lastModified',
      headerName: 'LAST MODIFIED',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <p>{params.value}</p>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <ClientContactRejectionReasonActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  const filteredReasons = reasons.filter((reason) =>
    reason.name.toLowerCase().includes(control._formValues.search?.toLowerCase() || '')
  );

  return (
    <>
      <CustomCardHeader
        control={control}
        name="search"
        placeholder="Search Rejection Reason"
        secondary={
          <Box display="flex" alignItems="center" gap={1}>
            <Button variant="outlined" size="small" onClick={() => setShowActivities(!showActivities)}>
              {showActivities ? 'Back' : 'Activities'}
            </Button>
            <Button variant="contained" size="small" color="primary" onClick={handleAddReasonDialogOpen}>
              + Add
            </Button>
          </Box>
        }
        // sx={{ mb: 2 }}
      />
       <Divider sx={{ mb: 1 }} />

      {showActivities ? (
        null
      ) : (
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box sx={{ height: 300, width: '100%' }}>
              <CustomDataGrid
                rows={filteredReasons}
                columns={clientContactRejectionReasonColumns}
                paginationModel={paginationModel}
                onPaginationModelChange={setPaginationModel}
                rowCount={filteredReasons.length}
              />
            </Box>
          </Grid>
        </Grid>
      )}

      <AddClientContactRejectionReason
        open={openAddReasonDialog}
        onClose={handleAddReasonDialogClose}
        onSave={handleAddReasonSave}
      />
      <EditClientContactRejectionReason
        open={openEditReasonDialog}
        onClose={() => setOpenEditReasonDialog(false)}
        onSave={handleEditReasonSave}
        reason={selectedReason}
      />
    </>
  );
};

export default ClientContactRejectionReasons;
