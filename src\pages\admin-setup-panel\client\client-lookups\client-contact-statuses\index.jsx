import React, { useState } from 'react';
import { Box, Button, Grid, <PERSON>lt<PERSON>, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddClientContactStatus from './add';
import EditClientContactStatus from './edit';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';

const ClientContactStatuses = () => {
  const [statuses, setStatuses] = useState([
    {
      id: 1,
      name: 'Active',
      createdBy: 'P<PERSON>h<PERSON>',
      modifiedBy: 'P<PERSON>h<PERSON>n<PERSON>ri',
      lastModified: '2025-06-06 18:00:00'
    },
    {
      id: 2,
      name: 'Inactive',
      createdBy: '<PERSON><PERSON><PERSON><PERSON>',
      modifiedBy: '<PERSON><PERSON><PERSON><PERSON>',
      lastModified: '2025-06-06 19:00:00'
    }
  ]);

  const { control } = useForm();

  const [openAddStatusDialog, setOpenAddStatusDialog] = useState(false);
  const [openEditStatusDialog, setOpenEditStatusDialog] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [showActivities, setShowActivities] = useState(false);

  const handleAddStatusDialogOpen = () => {
    setOpenAddStatusDialog(true);
  };

  const handleAddStatusDialogClose = () => {
    setOpenAddStatusDialog(false);
  };

  const handleAddStatusSave = (data) => {
    setStatuses((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        createdBy: 'User',
        modifiedBy: 'User',
        lastModified: new Date().toISOString().slice(0, 10)
      }
    ]);
    handleAddStatusDialogClose();
  };

  const handleEditStatusSave = (updatedStatus) => {
    setStatuses((prev) => prev.map((status) => (status.id === updatedStatus.id ? updatedStatus : status)));
    setOpenEditStatusDialog(false);
    setSelectedStatus(null);
  };

  const ClientContactStatusActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedStatus(params.row);
      setOpenEditStatusDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete client contact status:', params.row);
      setStatuses((prev) => prev.filter((status) => status.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const clientContactStatusColumns = [
    {
      field: 'name',
      headerName: 'STATUS NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <p>{params.value}</p>
        </Tooltip>
      )
    },
    {
      field: 'createdBy',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <p>{params.value}</p>
        </Tooltip>
      )
    },
    {
      field: 'modifiedBy',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <p>{params.value}</p>
        </Tooltip>
      )
    },
    {
      field: 'lastModified',
      headerName: 'LAST MODIFIED',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <p>{params.value}</p>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <ClientContactStatusActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  const filteredStatuses = statuses.filter((status) =>
    status.name.toLowerCase().includes(control._formValues.search?.toLowerCase() || '')
  );

  return (
    <>
      <CustomCardHeader
        control={control}
        name="search"
        placeholder="Search Contact Status"
        secondary={
          <Box display="flex" alignItems="center" gap={1}>
            <Button variant="outlined" size="small" onClick={() => setShowActivities(!showActivities)}>
              {showActivities ? 'Back' : 'Activities'}
            </Button>
            <Button variant="contained" size="small" color="primary" onClick={handleAddStatusDialogOpen}>
              + Add
            </Button>
          </Box>
        }
      />
      <Divider sx={{ mb: 1 }} />
      {showActivities ? (
        null
      ) : (
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box sx={{ height: 300, width: '100%' }}>
              <CustomDataGrid
                rows={filteredStatuses}
                columns={clientContactStatusColumns}
                paginationModel={paginationModel}
                onPaginationModelChange={setPaginationModel}
                rowCount={filteredStatuses.length}
              />
            </Box>
          </Grid>
        </Grid>
      )}
      <AddClientContactStatus
        open={openAddStatusDialog}
        onClose={handleAddStatusDialogClose}
        onSave={handleAddStatusSave}
      />
      <EditClientContactStatus
        open={openEditStatusDialog}
        onClose={() => setOpenEditStatusDialog(false)}
        onSave={handleEditStatusSave}
        status={selectedStatus}
      />
    </>
  );
};

export default ClientContactStatuses;