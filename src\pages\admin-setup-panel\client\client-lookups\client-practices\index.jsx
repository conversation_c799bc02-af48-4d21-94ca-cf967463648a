import React, { useState } from 'react';
import { Box, Button, Grid, <PERSON>ltip, IconButton, Menu, MenuItem, Divider } from '@mui/material';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddClientPractice from './add';
import EditClientPractice from './edit';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';

const ClientPractices = () => {
  const [practices, setPractices] = useState([
    {
      id: 1,
      name: 'Enterprise',
      createdBy: '<PERSON><PERSON>h<PERSON><PERSON>',
      modifiedBy: '<PERSON><PERSON>h<PERSON>',
      lastModified: '2025-06-06 18:00:00'
    },
    {
      id: 2,
      name: 'SMB',
      createdBy: '<PERSON><PERSON><PERSON><PERSON>',
      modifiedBy: '<PERSON><PERSON>h<PERSON>',
      lastModified: '2025-06-06 19:00:00'
    }
  ]);

  const { control } = useForm();

  const [openAddPracticeDialog, setOpenAddPracticeDialog] = useState(false);
  const [openEditPracticeDialog, setOpenEditPracticeDialog] = useState(false);
  const [selectedPractice, setSelectedPractice] = useState(null);
  const [showActivities, setShowActivities] = useState(false);

  const handleAddPracticeDialogOpen = () => {
    setOpenAddPracticeDialog(true);
  };

  const handleAddPracticeDialogClose = () => {
    setOpenAddPracticeDialog(false);
  };

  const handleAddPracticeSave = (data) => {
    setPractices((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        createdBy: 'User',
        modifiedBy: 'User',
        lastModified: new Date().toISOString().slice(0, 10)
      }
    ]);
    handleAddPracticeDialogClose();
  };

  const handleEditPracticeSave = (updatedPractice) => {
    setPractices((prev) => prev.map((practice) => (practice.id === updatedPractice.id ? updatedPractice : practice)));
    setOpenEditPracticeDialog(false);
    setSelectedPractice(null);
  };

  const ClientPracticeActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedPractice(params.row);
      setOpenEditPracticeDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete client practice:', params.row);
      setPractices((prev) => prev.filter((practice) => practice.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const clientPracticeColumns = [
    {
      field: 'name',
      headerName: 'PRACTICE NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <p>{params.value}</p>
        </Tooltip>
      )
    },
    {
      field: 'createdBy',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <p>{params.value}</p>
        </Tooltip>
      )
    },
    {
      field: 'modifiedBy',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <p>{params.value}</p>
        </Tooltip>
      )
    },
    {
      field: 'lastModified',
      headerName: 'LAST MODIFIED',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <p>{params.value}</p>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <ClientPracticeActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  const filteredPractices = practices.filter((practice) =>
    practice.name.toLowerCase().includes(control._formValues.search?.toLowerCase() || '')
  );

  return (
    <>
      <CustomCardHeader
        control={control}
        name="search"
        placeholder="Search Client Practice"
        secondary={
          <Box display="flex" alignItems="center" gap={1}>
            <Button variant="outlined" size="small" onClick={() => setShowActivities(!showActivities)}>
              {showActivities ? 'Back' : 'Activities'}
            </Button>
            <Button variant="contained" size="small" color="primary" onClick={handleAddPracticeDialogOpen}>
              + Add
            </Button>
          </Box>
        }
        // sx={{ mb: 2 }}
      />
       <Divider sx={{ mb: 1 }} />
      {showActivities ? (
        null
      ) : (
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box sx={{ height: 300, width: '100%' }}>
              <CustomDataGrid
                rows={filteredPractices}
                columns={clientPracticeColumns}
                paginationModel={paginationModel}
                onPaginationModelChange={setPaginationModel}
                rowCount={filteredPractices.length}
              />
            </Box>
          </Grid>
        </Grid>
      )}

      <AddClientPractice
        open={openAddPracticeDialog}
        onClose={handleAddPracticeDialogClose}
        onSave={handleAddPracticeSave}
      />
      <EditClientPractice
        open={openEditPracticeDialog}
        onClose={() => setOpenEditPracticeDialog(false)}
        onSave={handleEditPracticeSave}
        practice={selectedPractice}
      />
    </>
  );
};

export default ClientPractices; 