import React, { useState } from 'react';
import { Box, Tabs, Tab, CardActionArea, Card, Divider } from '@mui/material';
import MainCard from 'components/MainCard';
import ClientCategories from './client-categories';
import ClientContactRejectionReasons from './client-contact-rejection-reasons';
import ClientContactStatuses from './client-contact-statuses';
import ClientPractices from './client-practices';
import ClientStatuses from './client-statuses';


const ClientLookups = () => {
  const [selectedTab, setSelectedTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const renderTabContent = () => {
    switch (selectedTab) {
      case 0:
        return <ClientCategories />;
      case 1:
        return <ClientContactRejectionReasons />;
      case 2:
        return <ClientContactStatuses />;
      case 3:
        return <ClientPractices />;
      case 4:
        return <ClientStatuses />;
      default:
        return null;
    }
  };

  return (
    <MainCard  sx={{
      borderRadius: '0%',
      backgroundColor: 'white',
      '& .MuiInputLabel-root': { fontSize: '0.875rem' }
    }}>
      <Tabs
        value={selectedTab}
        onChange={handleTabChange}
        aria-label="client lookups tabs"
        variant="scrollable"
        scrollButtons="auto"
        sx={{
          mt:-2,
          ml: -2,
          alignItems: 'flex-start',
          justifyContent: 'flex-start',
          borderBottom: 0.2,
          borderColor: 'divider'
        }}
        orientation="horizontal"
      >
        <Tab label="Client Categories" />
        <Tab label="Client Contact Rejection Reasons" />
        <Tab label="Client Contact Statuses" />
        <Tab label="Client Practices" />
        <Tab label="Client Statuses" />
      </Tabs>
      {/* <Divider/> */}
      <Box sx={{ ml: 0 }}>
        {renderTabContent()}
      </Box>
    </MainCard>
  );
};

export default ClientLookups;
