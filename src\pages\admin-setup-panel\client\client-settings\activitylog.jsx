import {
  Paper, Table, TableHead, TableBody, TableRow, TableCell, Box, Typography, TableContainer
} from '@mui/material';
import { useState } from 'react';
import CustomTablePagination from 'components/custom-components/CustomPaginationItems';
import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';

const cellSx = {
  maxWidth: 180,
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap',
};

const ActivityLog = ({ logs }) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const paginatedLogs = logs.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  return (
    <Paper sx={{ mt: 2, p: { xs: 1, sm: 2 } }}>
      <TableContainer sx={{ maxHeight: 420, overflowX: 'auto' }}>
        <Table stickyHeader sx={{ minWidth: 700 }}>
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold' }}>Date & Time</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>User</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Action</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Description</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedLogs.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} align="center">
                  <Typography variant="body2" color="text.secondary">No activity logs found.</Typography>
                </TableCell>
              </TableRow>
            ) : (
              paginatedLogs.map((log, index) => (
                <TableRow key={index}>
                  <TableCell sx={cellSx} title={log.timestamp}>{log.timestamp || '-'}</TableCell>
                  <TableCell sx={cellSx} title={log.user}>{log.user || '-'}</TableCell>
                  <TableCell sx={cellSx} title={log.action}>{log.action || '-'}</TableCell>
                  <TableCell sx={cellSx} title={log.description}>{log.description || '-'}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
      <Box sx={{ display: 'flex', gap: 2, mt: 2, flexDirection: { xs: 'column', sm: 'row' } }}>
        <RowsPerPageSelector
          getState={() => ({ pagination: { pageIndex: page, pageSize: rowsPerPage } })}
          setPageSize={(size) => {
            setRowsPerPage(size);
            setPage(0);
          }}
        />
        <CustomTablePagination
          setPageSize={(size) => {
            setRowsPerPage(size);
            setPage(0);
          }}
          setPageIndex={setPage}
          getState={() => ({ pagination: { pageIndex: page, pageSize: rowsPerPage } })}
          getPageCount={() => Math.ceil(logs.length / rowsPerPage)}
        />
      </Box>
    </Paper>
  );
};

export default ActivityLog;
