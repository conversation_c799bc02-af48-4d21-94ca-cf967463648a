import React, { useState } from 'react';
import {
  Switch,
  Radio,
  RadioGroup,
  FormControlLabel,
  Checkbox,
  Button,
  Box,
  Grid
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import MainCard from 'components/MainCard';
import { useForm, Controller } from 'react-hook-form';
import ActivityLog from './activitylog';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomFormHelperText from 'components/custom-components/CustomFormHelperText';
import CustomAccordion from 'components/custom-components/CustomAccordion';

const teamOptions = [{ value: 'Team A' }, { value: 'Team B' }, { value: 'Team C' }];

const notifyOptions = [
  { value: 'Primary Owner', label: 'Primary Owner' },
  { value: 'Administrator', label: 'Administrator' }
];

const ClientSettings = () => {
  const [showActivities, setShowActivities] = useState(false);
  const [activityLogs, setActivityLogs] = useState([]); // Dummy fallback
  const [activityPage, setActivityPage] = useState(0);
  const [activityRowsPerPage, setActivityRowsPerPage] = useState(10);

  const { control, handleSubmit, watch } = useForm({
    defaultValues: {
      displayClientNames: true,
      defaultSubmissionFormat: 'clients',
      sendRequirement: true,
      sendHotlist: true,
      displayParentClient: false,
      companyAccess: 'accessRequired',
      companyNotify: ['Primary Owner'],
      companyContactAccess: 'accessRequired',
      companyContactNotify: ['Primary Owner'],
      teamLevel: false,
      team: '',
      teamAccess: 'accessRequired',
      teamNotify: ['Primary Owner'],
      teamContactAccess: 'accessRequired',
      teamContactNotify: ['Primary Owner']
    }
  });

  const teamLevel = watch('teamLevel');

  const onSubmit = (data) => {
    alert('Settings saved!');
    console.log('Submitted:', data);

    setActivityLogs((prevLogs) => [
      {
        timestamp: new Date().toLocaleString(),
        user: 'Current User', // Replace with actual user if available
        action: 'Saved Settings',
        description: 'Client settings were updated.'
      },
      ...prevLogs
    ]);
  };

  if (showActivities) {
    return (
      <MainCard sx={{ borderRadius: 2, p: 3, minHeight: '85vh' }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <CustomInputLabel sx={{ fontSize: '1.5rem', fontWeight: 500 }}>Client Settings</CustomInputLabel>
          <Button variant="outlined" size="small" onClick={() => setShowActivities(false)}>
            Back
          </Button>
        </Box>
        <ActivityLog
          logs={activityLogs}
          page={activityPage}
          setPage={setActivityPage}
          rowsPerPage={activityRowsPerPage}
          setRowsPerPage={setActivityRowsPerPage}
        />
      </MainCard>
    );
  }

  return (
    <MainCard
      title="Client Settings"
      secondary={
        <>
          <Button variant="outlined" size="small" onClick={() => setShowActivities(true)}>
            Activities
          </Button>
          <Button sx={{ ml: 2 }} variant="contained" color="primary" type="submit" size="small">
            Save
          </Button>
        </>
      }
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        {/* Main Settings */}
        <Grid container spacing={3} mb={3}>
          <Grid item xs={12} sm={6} lg={6} xl={6}>
            <CustomInputLabel>
              Display Client Names on Job Posting/Placements Irrespective of Visibility in the Clients Module
            </CustomInputLabel>
          </Grid>
          <Grid item xs={12} sm={6} lg={6} xl={6}>
            <Controller name="displayClientNames" control={control} render={({ field }) => <Switch {...field} checked={field.value} />} />
          </Grid>

          <Grid item xs={12} sm={6} lg={6} xl={6}>
            <Box>
              <CustomInputLabel>Default Submission Format</CustomInputLabel>
              <CustomFormHelperText>
                (Select where to configure the default submission format for clients, at the client or client contact level.)
              </CustomFormHelperText>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} lg={6} xl={6}>
            <Controller
              name="defaultSubmissionFormat"
              control={control}
              render={({ field }) => (
                <RadioGroup row {...field} value={field.value}>
                  <FormControlLabel value="clients" control={<Radio />} label="Clients" />
                  <FormControlLabel value="clientsContact" control={<Radio />} label="Clients Contact" />
                </RadioGroup>
              )}
            />
          </Grid>

          <Grid item xs={12} sm={6} lg={6} xl={6}>
            <Box>
              <CustomInputLabel>Default Send Requirement, Send Hotlist</CustomInputLabel>
              <CustomFormHelperText>If checked, the options will be enabled by default while creating a new client</CustomFormHelperText>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} lg={6} xl={6}>
            <Controller
              name="sendRequirement"
              control={control}
              render={({ field }) => <FormControlLabel control={<Checkbox {...field} checked={field.value} />} label="Send Requirement" />}
            />
            <Controller
              name="sendHotlist"
              control={control}
              render={({ field }) => <FormControlLabel control={<Checkbox {...field} checked={field.value} />} label="Send Hotlist" />}
            />
          </Grid>

          <Grid item xs={12} sm={6} lg={6} xl={6}>
            <Box>
              <CustomInputLabel>Do you want to display the parent client instead of child client</CustomInputLabel>
              <CustomFormHelperText>If enabled, Parent clients will be displayed while doing the confirmation</CustomFormHelperText>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} lg={6} xl={6}>
            <Controller name="displayParentClient" control={control} render={({ field }) => <Switch {...field} checked={field.value} />} />
          </Grid>
        </Grid>
        {/* Company Level Settings */}
        <CustomAccordion summary={<CustomInputLabel sx={{ fontWeight: 600 }}>Company Level Settings</CustomInputLabel>}>
          {/* <Box sx={{ pl: 0 }}> */}
            <Grid container spacing={3}>
              {/* Action if the Client Exists in the Database */}
              <Grid item xs={12} sm={6} lg={6} xl={6}>
                <Box>
                  <CustomInputLabel>Action if the Client Exists in the Database</CustomInputLabel>
                  <CustomFormHelperText>
                    • If Access Required is selected, the system sends an email requesting access to the client record from the primary
                    client owner and/or the administrator (based on the notification configuration)
                  </CustomFormHelperText>
                  <CustomFormHelperText>
                    • If Access Not Required is selected, the system automatically gives access to the client record by adding the user as
                    an owner.
                  </CustomFormHelperText>
                  <CustomFormHelperText>
                    • If Allow Duplicate is selected, the user will be allowed to create a duplicate client record
                  </CustomFormHelperText>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} lg={6} xl={6}>
                <Controller
                  name="companyAccess"
                  control={control}
                  render={({ field }) => (
                    <RadioGroup row {...field} value={field.value}>
                      <FormControlLabel value="accessRequired" control={<Radio />} label="Access Required" />
                      <FormControlLabel value="accessNotRequired" control={<Radio />} label="Access Not Required" />
                      <FormControlLabel value="allowDuplicates" control={<Radio />} label="Allow Duplicates" />
                    </RadioGroup>
                  )}
                />
              </Grid>
              {/* Notify This User About Client Record Access */}
              <Grid item xs={12} sm={6} lg={6} xl={6}>
                <CustomInputLabel>Notify This User About Client Record Access</CustomInputLabel>
              </Grid>
              <Grid item xs={12} sm={6} lg={6} xl={6}>
                <Grid container spacing={2}>
                  {notifyOptions.map((opt) => (
                    <Grid item key={opt.value}>
                      <Controller
                        name="companyNotify"
                        control={control}
                        render={({ field }) => (
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={field.value.includes(opt.value)}
                                onChange={(e) => {
                                  const checked = e.target.checked;
                                  const newValue = checked ? [...field.value, opt.value] : field.value.filter((v) => v !== opt.value);
                                  field.onChange(newValue);
                                }}
                                sx={{
                                  '&.Mui-checked': {
                                    color: 'primary.main'
                                  }
                                }}
                              />
                            }
                            label={opt.label}
                          />
                        )}
                      />
                    </Grid>
                  ))}
                </Grid>
              </Grid>
              {/* Action if the Client Contact Exists in the Database */}
              <Grid item xs={12} sm={6} lg={6} xl={6}>
                <Box>
                  <CustomInputLabel>Action if the Client Contact Exists in the Database</CustomInputLabel>
                  <CustomFormHelperText>
                    • If Access Required is selected, the system sends an email requesting access to the client contact record
                  </CustomFormHelperText>
                  <CustomFormHelperText>
                    from the primary client owner and/or the administrator (based on the notification configuration)
                  </CustomFormHelperText>
                  <CustomFormHelperText>
                    • If Allow Duplicates is selected, the user will be allowed to create a duplicate client contact record
                  </CustomFormHelperText>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} lg={6} xl={6}>
                <Controller
                  name="companyContactAccess"
                  control={control}
                  render={({ field }) => (
                    <RadioGroup row {...field} value={field.value}>
                      <FormControlLabel value="accessRequired" control={<Radio />} label="Access Required" />
                      <FormControlLabel value="allowDuplicates" control={<Radio />} label="Allow Duplicates" />
                    </RadioGroup>
                  )}
                />
              </Grid>
              {/* Notify these people to get the access to the client contact record */}
              <Grid item xs={12} sm={6} lg={6} xl={6}>
                <CustomInputLabel>Notify these people to get the access to the client contact record</CustomInputLabel>
              </Grid>
              <Grid item xs={12} sm={6} lg={6} xl={6}>
                <Grid container spacing={2}>
                  {notifyOptions.map((opt) => (
                    <Grid item key={opt.value}>
                      <Controller
                        name="companyContactNotify"
                        control={control}
                        render={({ field }) => (
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={field.value.includes(opt.value)}
                                onChange={(e) => {
                                  const checked = e.target.checked;
                                  const newValue = checked ? [...field.value, opt.value] : field.value.filter((v) => v !== opt.value);
                                  field.onChange(newValue);
                                }}
                                sx={{
                                  '&.Mui-checked': {
                                    color: 'primary.main'
                                  }
                                }}
                              />
                            }
                            label={opt.label}
                          />
                        )}
                      />
                    </Grid>
                  ))}
                </Grid>
              </Grid>
            </Grid>
          {/* </Box> */}
        </CustomAccordion>
        {/* Team Level Settings */}
        <CustomAccordion summary={<CustomInputLabel sx={{ fontWeight: 600 }}>Team Level Settings</CustomInputLabel>} >
          {/* <Box> */}
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} sm={6} lg={6} xl={6}>
                <Controller name="teamLevel" control={control} render={({ field }) => <Switch {...field} checked={field.value} />} />
              </Grid>
            </Grid>
            {teamLevel && (
              <Grid container spacing={3} mt={1}>
                {/* Applicable to Teams */}
                <Grid item xs={12} sm={6} lg={6} xl={6}>
                  <Box>
                    <CustomInputLabel>Applicable to Teams</CustomInputLabel>
                    <CustomFormHelperText>
                      The below configuration will only apply to the selected team; all other teams will follow the Company Level Settings
                      defined above.
                    </CustomFormHelperText>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} lg={6} xl={6}>
                  <Box>
                    {control && (
                      <Controller
                        name="team"
                        control={control}
                        render={({ field }) => (
                          <CustomDropdownField
                            {...field}
                            control={control}
                            options={teamOptions}
                            placeholder="Select Team"
                            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                          />
                        )}
                      />
                    )}
                  </Box>
                </Grid>
                {/* Action if the Client Exists in the Database (Team) */}
                <Grid item xs={12} sm={6} lg={6} xl={6}>
                  <Box>
                    <CustomInputLabel>Action if the Client Exists in the Database</CustomInputLabel>
                    <CustomFormHelperText>
                      • If Access Required is selected, the system sends an email requesting access to the client record from the primary
                      client owner and/or the administrator (based on the notification configuration)
                    </CustomFormHelperText>
                    <CustomFormHelperText>
                      • If Access Not Required is selected, the system automatically gives access to the client record by adding the user as
                      an owner.
                    </CustomFormHelperText>
                    <CustomFormHelperText>
                      • If Allow Duplicate is selected, the user will be allowed to create a duplicate client record
                    </CustomFormHelperText>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} lg={6} xl={6}>
                  <Controller
                    name="teamAccess"
                    control={control}
                    render={({ field }) => (
                      <RadioGroup row {...field} value={field.value}>
                        <FormControlLabel value="accessRequired" control={<Radio />} label="Access Required" />
                        <FormControlLabel value="accessNotRequired" control={<Radio />} label="Access Not Required" />
                        <FormControlLabel value="allowDuplicates" control={<Radio />} label="Allow Duplicates" />
                      </RadioGroup>
                    )}
                  />
                </Grid>
                {/* Notify This User About Client Record Access (Team) */}
                <Grid item xs={12} sm={6} lg={6} xl={6}>
                  <CustomInputLabel>Notify This User About Client Record Access</CustomInputLabel>
                </Grid>
                <Grid item xs={12} sm={6} lg={6} xl={6}>
                  <Grid container spacing={2}>
                    {notifyOptions.map((opt) => (
                      <Grid item key={opt.value}>
                        <Controller
                          name="teamNotify"
                          control={control}
                          render={({ field }) => (
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={field.value.includes(opt.value)}
                                  onChange={(e) => {
                                    const checked = e.target.checked;
                                    const newValue = checked ? [...field.value, opt.value] : field.value.filter((v) => v !== opt.value);
                                    field.onChange(newValue);
                                  }}
                                  sx={{
                                    '&.Mui-checked': {
                                      color: 'primary.main'
                                    }
                                  }}
                                />
                              }
                              label={opt.label}
                            />
                          )}
                        />
                      </Grid>
                    ))}
                  </Grid>
                </Grid>
                {/* Action if the Client Contact Exists in the Database (Team) */}
                <Grid item xs={12} sm={6} lg={6} xl={6}>
                  <Box>
                    <CustomInputLabel>Action if the Client Contact Exists in the Database</CustomInputLabel>
                    <CustomFormHelperText>
                      • If Access Required is selected, the system sends an email requesting access to the client contact record
                    </CustomFormHelperText>
                    <CustomFormHelperText>
                      from the primary client owner and/or the administrator (based on the notification configuration)
                    </CustomFormHelperText>
                    <CustomFormHelperText>
                      • If Allow Duplicates is selected, the user will be allowed to create a duplicate client contact record
                    </CustomFormHelperText>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} lg={6} xl={6}>
                  <Controller
                    name="teamContactAccess"
                    control={control}
                    render={({ field }) => (
                      <RadioGroup row {...field} value={field.value}>
                        <FormControlLabel value="accessRequired" control={<Radio />} label="Access Required" />
                        <FormControlLabel value="allowDuplicates" control={<Radio />} label="Allow Duplicates" />
                      </RadioGroup>
                    )}
                  />
                </Grid>
                {/* Notify This User About Client Contact Record Access (Team) */}
                <Grid item xs={12} sm={6} lg={6} xl={6}>
                  <CustomInputLabel>Notify This User About Client Record Access</CustomInputLabel>
                </Grid>
                <Grid item xs={12} sm={6} lg={6} xl={6}>
                  <Grid container spacing={2}>
                    {notifyOptions.map((opt) => (
                      <Grid item key={opt.value}>
                        <Controller
                          name="teamContactNotify"
                          control={control}
                          render={({ field }) => (
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={field.value.includes(opt.value)}
                                  onChange={(e) => {
                                    const checked = e.target.checked;
                                    const newValue = checked ? [...field.value, opt.value] : field.value.filter((v) => v !== opt.value);
                                    field.onChange(newValue);
                                  }}
                                  sx={{
                                    '&.Mui-checked': {
                                      color: 'primary.main'
                                    }
                                  }}
                                />
                              }
                              label={opt.label}
                            />
                          )}
                        />
                      </Grid>
                    ))}
                  </Grid>
                </Grid>
              </Grid>
            )}
          {/* </Box> */}
        </CustomAccordion>
      </form>
    </MainCard>
  );
};

export default ClientSettings;
