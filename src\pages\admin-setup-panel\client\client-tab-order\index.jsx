import React, { useState } from 'react';
import {
  Typography,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Checkbox,
  Button,
  Paper,
  Box,
  Stack,
  IconButton
} from '@mui/material';
import MainCard from 'components/MainCard';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import DoubleArrowIcon from '@mui/icons-material/KeyboardDoubleArrowRight';
import DoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';
import { Controller } from 'react-hook-form';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
const initialSource = [
  'Assignments',
  'Task Manager',
  'Guidelines',
  'Markup Calculations',
  'Blacklisted Applicants',
  'Email Activity',
  'Notes'
];
const initialTarget = [
  'SnapShot',
  'VMS Integrations',
  'Requirements',
  'Client Submission Format',
  'Eforms',
  'Hires',
  'Checklists',
  'Activities'
];

const ClientTabOrder = () => {
  const [checked, setChecked] = useState([]);
  const [source, setSource] = useState(initialSource);
  const [target, setTarget] = useState(initialTarget);
  const [selectedTargetIdx, setSelectedTargetIdx] = useState(null);
  const [teamLevel, setTeamLevel] = useState(false);
  const [control, setControl] = useState(null);
  const [teamOptions, setTeamOptions] = useState([]);

  const leftChecked = checked.filter((item) => source.includes(item));
  const rightChecked = checked.filter((item) => target.includes(item));

  const handleToggle = (value) => () => {
    const currentIndex = checked.indexOf(value);
    const newChecked = [...checked];
    if (currentIndex === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(currentIndex, 1);
    }
    setChecked(newChecked);
  };

  // Move selected from source to target (no duplicates)
  const handleCheckedRight = () => {
    const newItems = leftChecked.filter((item) => !target.includes(item));
    setTarget([...target, ...newItems]);
    setSource(source.filter((item) => !leftChecked.includes(item)));
    setChecked(checked.filter((item) => !leftChecked.includes(item)));
  };

  // Move all from source to target (no duplicates)
  const handleAllRight = () => {
    const newItems = source.filter((item) => !target.includes(item));
    setTarget([...target, ...newItems]);
    setSource([]);
    setChecked(checked.filter((item) => !source.includes(item)));
  };

  // Move selected from target to source (no duplicates)
  const handleCheckedLeft = () => {
    const newItems = rightChecked.filter((item) => !source.includes(item));
    setSource([...source, ...newItems]);
    setTarget(target.filter((item) => !rightChecked.includes(item)));
    setChecked(checked.filter((item) => !rightChecked.includes(item)));
  };

  // Move all from target to source (no duplicates)
  const handleAllLeft = () => {
    const newItems = target.filter((item) => !source.includes(item));
    setSource([...source, ...newItems]);
    setTarget([]);
    setChecked(checked.filter((item) => !target.includes(item)));
  };

  // Move selected item in target up or down
  const moveTargetItem = (direction) => {
    if (selectedTargetIdx === null || selectedTargetIdx < 0 || selectedTargetIdx >= target.length) return;
    const newTarget = [...target];
    if (direction === 'up' && selectedTargetIdx > 0) {
      [newTarget[selectedTargetIdx - 1], newTarget[selectedTargetIdx]] = [newTarget[selectedTargetIdx], newTarget[selectedTargetIdx - 1]];
      setTarget(newTarget);
      setSelectedTargetIdx(selectedTargetIdx - 1);
    } else if (direction === 'down' && selectedTargetIdx < newTarget.length - 1) {
      [newTarget[selectedTargetIdx + 1], newTarget[selectedTargetIdx]] = [newTarget[selectedTargetIdx], newTarget[selectedTargetIdx + 1]];
      setTarget(newTarget);
      setSelectedTargetIdx(selectedTargetIdx + 1);
    }
  };

  // Custom list rendering
  const customList = (title, items, isTarget = false) => (
    <Paper
      sx={{ width: { xs: '100%', sm: 260 }, minHeight: 320, maxHeight: 400, overflow: 'auto', p: 1, borderRadius: 2, mb: { xs: 2, sm: 0 } }}
    >
      <Typography variant="subtitle1" align="center" sx={{ fontWeight: 600, mb: 1 }}>
        {title}
      </Typography>
      <List dense component="div" role="list">
        {items.map((value, idx) => {
          const labelId = `transfer-list-item-${value}-label`;
          const isSelected = checked.indexOf(value) !== -1;
          const isTargetSelected = isTarget && selectedTargetIdx === idx;
          return (
            <ListItem
              key={value}
              role="listitem"
              button
              onClick={isTarget ? () => setSelectedTargetIdx(idx) : handleToggle(value)}
              onDoubleClick={isTarget ? handleToggle(value) : undefined}
              selected={isTarget ? isTargetSelected : isSelected}
              sx={{ borderRadius: 1, mb: 0.5, bgcolor: isTargetSelected ? 'primary.lighter' : undefined }}
            >
              <ListItemIcon>
                <Checkbox
                  checked={isSelected}
                  tabIndex={-1}
                  disableRipple
                  color="primary"
                  inputProps={{ 'aria-labelledby': labelId }}
                  onClick={handleToggle(value)}
                />
              </ListItemIcon>
              <ListItemText id={labelId} primary={value} sx={{ wordBreak: 'break-word' }} />
            </ListItem>
          );
        })}
      </List>
    </Paper>
  );

  return (
    <MainCard 
      title="Client Default Tab Order"
      secondary={
        <Button variant="contained" color="primary" type="submit" size="small">
          Save
        </Button>
      }
      sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
    >
      <Box sx={{ mt: 2 }}>
        <Grid container spacing={2} justifyContent="center" alignItems="flex-start" direction={{ xs: 'column', sm: 'row' }}>
          <Grid item xs={12} sm="auto" sx={{ width: { xs: '100%', sm: 'auto' } }}>
            {customList('Source List', source)}
          </Grid>
          <Grid
            item
            xs={12}
            sm="auto"
            sx={{
              display: 'flex',
              flexDirection: { xs: 'row', sm: 'column' },
              justifyContent: 'center',
              alignItems: 'center',
              gap: 1,
              mb: { xs: 2, sm: 0 }
            }}
          >
            <IconButton
              color="primary"
              onClick={handleCheckedRight}
              disabled={leftChecked.length === 0}
              sx={{ border: '1px solid #1976d2', bgcolor: 'white', '&:hover': { bgcolor: 'primary.lighter' }, m: 0.5 }}
            >
              <ArrowForwardIcon />
            </IconButton>
            <IconButton
              color="primary"
              onClick={handleAllRight}
              disabled={source.length === 0}
              sx={{ border: '1px solid #1976d2', bgcolor: 'white', '&:hover': { bgcolor: 'primary.lighter' }, m: 0.5 }}
            >
              <DoubleArrowIcon />
            </IconButton>
            <IconButton
              color="primary"
              onClick={handleCheckedLeft}
              disabled={rightChecked.length === 0}
              sx={{ border: '1px solid #1976d2', bgcolor: 'white', '&:hover': { bgcolor: 'primary.lighter' }, m: 0.5 }}
            >
              <ArrowBackIcon />
            </IconButton>
            <IconButton
              color="primary"
              onClick={handleAllLeft}
              disabled={target.length === 0}
              sx={{ border: '1px solid #1976d2', bgcolor: 'white', '&:hover': { bgcolor: 'primary.lighter' }, m: 0.5 }}
            >
              <DoubleArrowLeftIcon />
            </IconButton>
          </Grid>
          <Grid item xs={12} sm="auto" sx={{ width: { xs: '100%', sm: 'auto' }, position: 'relative' }}>
            {customList('Target List', target, true)}
            <Stack
              spacing={2}
              alignItems="center"
              direction={{ xs: 'row', sm: 'column' }}
              sx={{ position: { sm: 'absolute' }, top: { sm: 60 }, right: { sm: -60 }, left: { xs: 0, sm: 'auto' }, mt: { xs: 1, sm: 0 } }}
            >
              <IconButton
                color="primary"
                onClick={() => moveTargetItem('up')}
                disabled={selectedTargetIdx === null || selectedTargetIdx === 0}
                sx={{ border: '1px solid #1976d2', bgcolor: 'white', '&:hover': { bgcolor: 'primary.lighter' }, m: 0.5 }}
              >
                <ArrowUpwardIcon />
              </IconButton>
              <IconButton
                color="primary"
                onClick={() => moveTargetItem('down')}
                disabled={selectedTargetIdx === null || selectedTargetIdx === target.length - 1}
                sx={{ border: '1px solid #1976d2', bgcolor: 'white', '&:hover': { bgcolor: 'primary.lighter' }, m: 0.5 }}
              >
                <ArrowDownwardIcon />
              </IconButton>
            </Stack>
          </Grid>
          {teamLevel && control && (
            <Grid item xs={12} sm={4} sx={{ width: { xs: '100%', sm: 250 } }}>
              <Box sx={{ width: '100%' }}>
                <Controller
                  name="team"
                  control={control}
                  render={({ field }) => <CustomDropdownField {...field} control={control} options={teamOptions} placeholder="Select" />}
                />
              </Box>
            </Grid>
          )}
        </Grid>
      </Box>
    </MainCard>
  );
};

export default ClientTabOrder;
