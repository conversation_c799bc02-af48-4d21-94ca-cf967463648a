import React from 'react';
import PropTypes from 'prop-types';
import { Box, Paper, Typography, Chip, Stack } from '@mui/material';

export default function AdminCard({ title, description, icon: IconComponent, iconSx, isNew }) {
  return (
    <Paper 
      elevation={1} 
      sx={{
        p: 2, 
        display: 'flex', 
        flexDirection: 'row',
        alignItems: 'flex-start', 
        cursor: 'pointer', 
        '&:hover': { backgroundColor: 'grey.100' }, 
        height: '100%',
      }}
    >
      {IconComponent && <Box sx={{ mr: 1.5, display: 'flex', alignItems: 'center' }}><IconComponent sx={iconSx} /></Box>}
      
      <Stack direction="column" alignItems="flex-start">
        <Typography variant="h6">{title}{isNew && <Chip label="New" size="small" color="primary" sx={{ ml: 1 }} />}</Typography>
        <Typography variant="body2" color="text.secondary">
          {description}
        </Typography>
      </Stack>
    </Paper>
  );
}

AdminCard.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  icon: PropTypes.elementType,
  iconSx: PropTypes.object,
  isNew: PropTypes.bool,
}; 