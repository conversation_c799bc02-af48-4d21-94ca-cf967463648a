import React from 'react';
import PropTypes from 'prop-types';
import { Box, Typography, Stack } from '@mui/material';

export default function AdminNavigation({ selectedCategory, handleCategoryClick, categories }) {
  return (
    <Box sx={{ p: 2, borderRight: 1, borderColor: 'divider' }}>

      <Stack spacing={1}>
        {categories.map((category) => (
          <Stack 
            key={category.name}
            direction="row" 
            alignItems="flex-start" 
            spacing={1}
            sx={{
              cursor: 'pointer',
              p: 1, // Add padding to make the clickable area larger
              borderRadius: 1, // Add some border radius
              ...(selectedCategory === category.name && { 
                color: 'primary.main', 
                backgroundColor: 'action.hover' // Highlight background for selected item
              }),
              '&:hover': {
                backgroundColor: 'action.hover',
              },
              mt: category.name !== 'Freddy' ? 1 : 0, // Adjusted mt for spacing
              mb: 1 // Add bottom margin for spacing between category blocks
            }}
            onClick={() => handleCategoryClick(category.name)}
          >
            {category.icon}
            <Stack direction="column" spacing={0.5}> {/* Stack for vertical alignment of title and description */}
              <Typography variant="body1" sx={{ fontWeight: 600 }}>
                {category.name}
              </Typography>
              {/* Description based on category type or always display */}
              {category.description && (
                <Typography variant="body2" color="text.secondary">
                  {category.description}
                </Typography>
              )}
            </Stack>
          </Stack>
        ))}
      </Stack>
    </Box>
  );
}

AdminNavigation.propTypes = {
  selectedCategory: PropTypes.string.isRequired,
  handleCategoryClick: PropTypes.func.isRequired,
  categories: PropTypes.arrayOf(PropTypes.shape({
    name: PropTypes.string.isRequired,
    ref: PropTypes.object.isRequired,
    icon: PropTypes.element.isRequired,
    description: PropTypes.string, // Added description to propTypes
  })).isRequired,
}; 