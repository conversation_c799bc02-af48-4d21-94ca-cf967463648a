import React, { useState, useEffect } from 'react';
import { Grid, Switch, FormControlLabel, FormHelperText, RadioGroup, Radio, Box, Button } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import { useForm } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomNameField from 'components/custom-components/CustomNameField';
import axios from 'axios';

function ConcentSettings() {
  const API_URL = import.meta.env.VITE_APP_API_URL;
  const { control, watch, setValue } = useForm({
    defaultValues: {
      consent_applicable_to: 'based_on_country'
    }
  });
  const [countries, setCountries] = useState([]);

  const consentApplicableTo = watch('consent_applicable_to');

  const handleApplicableToChange = (event) => {
    setValue('consent_applicable_to', event.target.value);
  };

  // Fetch countries from API
  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const response = await axios.get(`${API_URL}/api/countries`);
        console.log('API Response:', response.data); // Debugging log ✅

        // Ensure response is an array before mapping
        setCountries(response.data);
      } catch (error) {
        console.error('Error fetching countries:', error);
      }
    };

    fetchCountries();
  }, [API_URL]);

  // Convert countries to dropdown options format
  const countriesOptions = countries.map((country) => ({
    value: country.name
    // label: country.name
  }));

  const userOptions = [
    { label: 'Nagaraju', value: 'Nagaraju' },
    { label: 'Prudhvi Kanmuri', value: 'Prudhvi Kanmuri' },
    { label: 'Radhika G', value: 'Radhika G' }
  ];
  const roleUserOptions = [
    { value: 'Hiring Manager', label: 'Hiring Manager' },
    { value: 'HR Manager', label: 'HR Manager' },
    { value: 'Lead Recruiter', label: 'Lead Recruiter' },
    { value: 'Technical Recruiter', label: 'Technical Recruiter' },
    { value: 'Nagaraju', label: 'Nagaraju' },
    { value: 'Prudhvi Kanmuri', label: 'Prudhvi Kanmuri' },
    { value: 'Radhika G', label: 'Radhika G' }
  ];

  const numberOptions = Array.from({ length: 10 }, (_, i) => ({ label: String(i + 1), value: String(i + 1) }));

  const consentPeriodOptions = [
    { value: '3 Months', label: '3m' },
    { value: '6 Months', label: '6m' },
    { value: '1 Year', label: '1y' },
    { value: '2 Years', label: '2y' },
    { value: '3 Years', label: '3y' },
    { value: '4 Years', label: '4y' },
    { value: '5 Years', label: '5y' },
    { value: '10 Years', label: '10y' }
  ];

  return (
    <MainCard
      title="Consent Settings"
      secondary={
        <Button variant="contained" color="primary" size="small">
          Save
        </Button>
      }
    >
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Grid container alignItems="center" spacing={2}>
            <Grid item xs={12} sm={6} lg={6}>
              <Box>
                <CustomInputLabel required>Consent Type</CustomInputLabel>
                <FormHelperText sx={{ whiteSpace: 'normal', textOverflow: 'unset', overflow: 'visible' }}>
                  (The consent name provided here will be reflected for all the consent communications sent to the candidates. If the
                  consent type is null, by default GDPR is reflected for all consent communications)
                </FormHelperText>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} lg={6}>
              <CustomNameField
                name="consent_type"
                control={control}
                placeholder="Enter Consent Type"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Grid container alignItems="center" spacing={2}>
            <Grid item xs={12} sm={6} lg={6}>
              <Box>
                <CustomInputLabel>Consent Applicable to</CustomInputLabel>
                <FormHelperText>(Consent request is sent to the applicants based on selection here.)</FormHelperText>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} lg={6}>
              <RadioGroup row name="consent_applicable_to" value={consentApplicableTo} onChange={handleApplicableToChange}>
                <FormControlLabel value="all_applicants" control={<Radio />} label="All Applicants" />
                <FormControlLabel value="based_on_country" control={<Radio />} label="Based on Applicant Country" />
              </RadioGroup>
            </Grid>
          </Grid>
        </Grid>

        {consentApplicableTo === 'based_on_country' && (
          <Grid item xs={12}>
            <Grid container alignItems="center" spacing={2}>
              <Grid item xs={12} sm={6} lg={6}>
                <CustomInputLabel>Countries</CustomInputLabel>
              </Grid>
              <Grid item xs={12} sm={6} lg={6}>
                <CustomDropdownField
                  name="countries"
                  control={control}
                  placeholder="India"
                  options={countriesOptions}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>
            </Grid>
          </Grid>
        )}

        <Grid item xs={12}>
          <Grid container alignItems="center" spacing={2} justifyContent="space-between">
            <Grid item xs={12} sm={6} lg={6}>
              <Box>
                <CustomInputLabel>Bypass for all Applicants</CustomInputLabel>
                <FormHelperText sx={{ whiteSpace: 'normal', textOverflow: 'unset', overflow: 'visible' }}>
                  (If enabled, users can perform recruitment activities on applicant record without any consent approval from the
                  applicant.)
                </FormHelperText>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} lg={6} container justifyContent="flex-start">
              <FormControlLabel control={<Switch />} label="" sx={{ mr: 'auto' }} />
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Grid container alignItems="center" spacing={2}>
            <Grid item xs={12} sm={6} lg={6}>
              <Box>
                <CustomInputLabel>Choose Roles or Users who can bypass consent</CustomInputLabel>
                <FormHelperText sx={{ whiteSpace: 'normal', textOverflow: 'unset', overflow: 'visible' }}>
                  (Users selected here can bypass the consent compliance from the applicant detail page to perform activities without
                  consent approval.)
                </FormHelperText>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} lg={6}>
              <CustomDropdownField
                name="bypass_users"
                control={control}
                placeholder="Select a Notifier"
                options={roleUserOptions}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Grid container alignItems="center" spacing={2}>
            <Grid item xs={12} sm={6} lg={6}>
              <Box>
                <CustomInputLabel>Set the default user to notify for consent bypass</CustomInputLabel>
                <FormHelperText sx={{ whiteSpace: 'normal', textOverflow: 'unset', overflow: 'visible' }}>
                  (Choose the default user to be notified when the user bypasses the consent on any applicant record.)
                </FormHelperText>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} lg={6}>
              <CustomDropdownField
                name="default_bypass_user"
                control={control}
                placeholder="Select a Notifier"
                options={userOptions}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Grid container alignItems="center" spacing={2}>
            <Grid item xs={12} sm={6} lg={6}>
              <Box>
                <CustomInputLabel>Set Default Consent Period</CustomInputLabel>
                <FormHelperText>(The applicant&apos;s data is maintained in the database as per the consent period given.)</FormHelperText>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} lg={6}>
              <CustomDropdownField
                name="consent_period"
                control={control}
                placeholder="1 Year"
                options={consentPeriodOptions}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Grid container alignItems="center" spacing={2} justifyContent="space-between">
            <Grid item xs={12} sm={6} lg={6}>
              <Box>
                <CustomInputLabel>Send consent for all Jobs</CustomInputLabel>
                <FormHelperText sx={{ whiteSpace: 'normal', textOverflow: 'unset', overflow: 'visible' }}>
                  (If enabled, one consent request is initiated to the applicants for all job submissions. Once approved, the applicant
                  profile can be submitted to any job.)
                </FormHelperText>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} lg={6} container justifyContent="flex-start">
              <FormControlLabel control={<Switch defaultChecked />} label="" sx={{ mr: 'auto' }} />
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Grid container alignItems="center" spacing={2} justifyContent="space-between">
            <Grid item xs={12} sm={6} lg={6}>
              <CustomInputLabel>
                Do you want to send a Consent Request form to the applicant while creating/migrating an applicant?
              </CustomInputLabel>
            </Grid>
            <Grid item xs={12} sm={6} lg={6} container justifyContent="flex-start">
              <FormControlLabel control={<Switch defaultChecked />} label="" sx={{ mr: 'auto' }} />
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Grid container alignItems="center" spacing={2} justifyContent="space-between">
            <Grid item xs={12} sm={6} lg={6}>
              <Box>
                <CustomInputLabel>Send Consent based on the Job</CustomInputLabel>
                <FormHelperText sx={{ whiteSpace: 'normal', textOverflow: 'unset', overflow: 'visible' }}>
                  (If enabled, users must initiate consent for each job manually on the applicant record. Users cannot submit applicants for
                  jobs until they have approved consent.)
                </FormHelperText>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} lg={6} container justifyContent="flex-start">
              <FormControlLabel control={<Switch defaultChecked />} label="" sx={{ mr: 'auto' }} />
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Grid container alignItems="center" spacing={2}>
            <Grid item xs={12} sm={6} lg={6}>
              <Box>
                <CustomInputLabel>Set the default user for the Consent email notification</CustomInputLabel>
                <FormHelperText>(Choose the default user to be notified when the Consent email is sent to an Applicant.)</FormHelperText>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} lg={6}>
              <CustomDropdownField
                name="default_email_user"
                control={control}
                placeholder="Select a Notifier"
                options={userOptions}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Grid container alignItems="center" spacing={2}>
            <Grid item xs={12} sm={6} lg={6}>
              <CustomInputLabel>Number of times to send Consent Notifications</CustomInputLabel>
            </Grid>
            <Grid item xs={12} sm={6} lg={6}>
              <CustomDropdownField
                name="notification_count"
                control={control}
                placeholder="1"
                options={numberOptions}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Grid container alignItems="center" spacing={2}>
            <Grid item xs={12} sm={6} lg={6}>
              <CustomInputLabel>Frequency (in days)</CustomInputLabel>
            </Grid>
            <Grid item xs={12} sm={6} lg={6}>
              <CustomDropdownField
                name="frequency"
                control={control}
                placeholder="1"
                options={numberOptions}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Grid container alignItems="center" spacing={2} justifyContent="space-between">
            <Grid item xs={12} sm={6} lg={6}>
              <CustomInputLabel>Display the Send Consent option in the Rejected Consent listing tab.</CustomInputLabel>
            </Grid>
            <Grid item xs={12} sm={6} lg={6} container justifyContent="flex-start">
              <FormControlLabel control={<Switch />} label="" sx={{ mr: 'auto' }} />
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Grid container alignItems="center" spacing={2} justifyContent="space-between">
            <Grid item xs={12} sm={6} lg={6}>
              <Box>
                <CustomInputLabel>Display Consent Terms & Conditions to Applicant</CustomInputLabel>
                <FormHelperText>
                  (Enable this option to display Terms and Conditions to Applicants before they approve consent.)
                </FormHelperText>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} lg={6} container justifyContent="flex-start">
              <FormControlLabel control={<Switch />} label="" sx={{ mr: 'auto' }} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </MainCard>
  );
}

export default ConcentSettings;
