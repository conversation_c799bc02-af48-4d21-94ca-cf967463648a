import React, { useState } from 'react';
import {
  Box,
  Typography,
  Switch,
  Checkbox,
  FormControlLabel,
  Button,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  OutlinedInput,
  ListItemText,
  Grid,
  Divider
} from '@mui/material';
import MainCard from 'components/MainCard';
import ActivityLog from './activitylog';
import CustomAllCharactersField from 'components/custom-components/CustomAllCharactersField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import { useForm } from 'react-hook-form';

const users = ['Jathin', 'Nagaraju', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON> G'];

const ClientContactVisibility = () => {
  const { control, handleSubmit, reset } = useForm({
    defaultValues: {
      requirementCount: '',
      submissions: '',
      interviews: '',
      closures: '',
      deadline: '',
      duration: ''
    }
  });

  const [assignTarget, setAssignTarget] = useState(false);
  const [sendNotification, setSendNotification] = useState([]);
  const [positionsCountEnabled, setPositionsCountEnabled] = useState(false);
  const [allowAccess, setAllowAccess] = useState(false);
  const [activityLogs, setActivityLogs] = useState([
    {
      timestamp: '2025-06-09 14:30',
      action: 'created',
      visibility: 'New',
      user: 'Jathin'
    }
  ]);
  const [activityPage, setActivityPage] = useState(0);
  const [activityRowsPerPage, setActivityRowsPerPage] = useState(5);
  const [showActivities, setShowActivities] = useState(false);

  const onSubmit = (data) => {
    if (!assignTarget) {
      alert('Please enable the target assignment switch before saving.');
      return;
    }

    if (!data.deadline) {
      alert('Please enter a deadline.');
      return;
    }

    if (!allowAccess && sendNotification.length === 0) {
      alert('Please select at least one protocol.');
      return;
    }

    const newLog = {
      timestamp: new Date().toISOString().slice(0, 16).replace('T', ' '),
      action: 'created',
      visibility: 'New',
      user: 'Jathin'
    };

    setActivityLogs([newLog, ...activityLogs]);
    setAssignTarget(false);
    reset();
    setAllowAccess(false);
    setSendNotification([]);
  };

  return (
    <MainCard sx={{ borderRadius: 2, p: 3, minHeight: '85vh' }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5">Client Contact Visibility</Typography>
        <Button variant="outlined" size="small" onClick={() => setShowActivities(!showActivities)}>
          {showActivities ? 'Back' : 'Activities'}
        </Button>
      </Box>

      {showActivities ? (
        <ActivityLog
          logs={activityLogs}
          page={activityPage}
          setPage={setActivityPage}
          rowsPerPage={activityRowsPerPage}
          setRowsPerPage={setActivityRowsPerPage}
        />
      ) : (
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControlLabel
                label="Assign Target to Sales Manager/User When Client Contacts are Created"
                labelPlacement="start"
                control={
                  <Switch
                    checked={assignTarget}
                    onChange={(e) => setAssignTarget(e.target.checked)}
                  />
                }
                sx={{ ml: '-2px' }}
              />
            </Grid>

            {assignTarget && (
              <>
                <Grid item xs={12} container alignItems="center" spacing={2}>
                  <Grid item sx={{ minWidth: 230 }}>
                    <Typography variant="body1">Total Requirements Needed From Each Client Contact:</Typography>
                  </Grid>
                  <Grid item xs>
                    <CustomAllCharactersField
                      name="requirementCount"
                      control={control}
                      placeholder=""
                      required
                      sx={{ width: '30%' }}
                    />
                  </Grid>
                </Grid>

                <Grid item xs={12} container alignItems="center" spacing={2}>
                  <Grid item sx={{ minWidth: 230 }}>
                    <Typography variant="body1">Check this box if the system needs to consider positions in the count:</Typography>
                  </Grid>
                  <Grid item xs>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={positionsCountEnabled}
                          onChange={(e) => setPositionsCountEnabled(e.target.checked)}
                        />
                      }
                      label=""
                    />
                  </Grid>
                </Grid>

                <Grid item xs={12} container alignItems="center" spacing={2}>
                  <Grid item sx={{ minWidth: 230 }}>
                    <Typography variant="body1">Total Submissions:</Typography>
                  </Grid>
                  <Grid item xs>
                    <CustomAllCharactersField
                      name="submissions"
                      control={control}
                      placeholder=""
                      sx={{ width: '30%' }}
                    />
                  </Grid>
                </Grid>

                <Grid item xs={12} container alignItems="center" spacing={2}>
                  <Grid item sx={{ minWidth: 230 }}>
                    <Typography variant="body1">Total Interviews:</Typography>
                  </Grid>
                  <Grid item xs>
                    <CustomAllCharactersField
                      name="interviews"
                      control={control}
                      placeholder=""
                      sx={{ width: '30%' }}
                    />
                  </Grid>
                </Grid>

                <Grid item xs={12} container alignItems="center" spacing={2}>
                  <Grid item sx={{ minWidth: 230 }}>
                    <Typography variant="body1">Total Closures:</Typography>
                  </Grid>
                  <Grid item xs>
                    <CustomAllCharactersField
                      name="closures"
                      control={control}
                      placeholder=""
                      sx={{ width: '30%' }}
                    />
                  </Grid>
                </Grid>

                <Grid item xs={12} container alignItems="center" spacing={2}>
                  <Grid item sx={{ minWidth: 230 }}>
                    <Typography variant="body1">Deadline to Meet Target:</Typography>
                  </Grid>
                  <Grid item xs>
                    <CustomAllCharactersField
                      name="deadline"
                      control={control}
                      placeholder=""
                      required
                      sx={{ width: '30%' }}
                    />
                  </Grid>
                </Grid>

                <Grid item xs={12} container alignItems="center" spacing={2}>
                  <Grid item sx={{ minWidth: 230 }}>
                    <Typography variant="body1">Duration:</Typography>
                  </Grid>
                  <Grid item xs>
                    <CustomDropdownField
                      name="duration"
                      control={control}
                      options={[
                        { value: 'Days' },
                        { value: 'Weeks' },
                        { value: 'Months' }
                      ]}
                      required
                      sx={{ width: '30%' }}
                    />
                  </Grid>
                </Grid>

                {/* System Protocol Section */}
                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom>
                    System Protocol if Target Isn't Reached
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <Box display="flex" flexDirection="column" gap={1}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={allowAccess}
                          onChange={(e) => setAllowAccess(e.target.checked)}
                        />
                      }
                      label="Allow Client Access to All Users"
                    />

                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={sendNotification.length > 0}
                          onChange={(e) =>
                            setSendNotification(e.target.checked ? [users[0]] : [])
                          }
                        />
                      }
                      label="Send Notification"
                    />

                    {sendNotification.length > 0 && (
                      <FormControl sx={{ width: '30%' }}>
                        <CustomDropdownField
                          name="notifyUsers"
                          control={control}
                          options={users.map((name) => ({ value: name }))}
                          multiple
                          value={sendNotification}
                          onChange={(e) => setSendNotification(e.target.value)}
                          renderValue={(selected) => selected.join(', ')}
                          label=""
                        />
                      </FormControl>
                    )}
                  </Box>
                </Grid>
              </>
            )}
          </Grid>

          <Box
            sx={{
              position: 'fixed',
              bottom: 0,
              left: 362,
              right: 40,
              bgcolor: 'white',
              boxShadow: '0 -2px 10px rgba(0, 0, 0, 0.1)',
              p: 2,
              display: 'flex',
              justifyContent: 'center',
              zIndex: 10
            }}
          >
            <Button variant="contained" color="primary" type="submit">
              Save
            </Button>
          </Box>
        </form>
      )}
    </MainCard>
  );
};

export default ClientContactVisibility;
