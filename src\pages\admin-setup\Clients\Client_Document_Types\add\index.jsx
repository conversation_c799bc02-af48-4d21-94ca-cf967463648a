import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Divider,
  Grid,
  FormControlLabel,
  Radio,
  RadioGroup,
  IconButton,
  Checkbox,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  OutlinedInput,
  ListItemText,
  Switch
} from '@mui/material';
import { Close } from '@mui/icons-material';
import MainCard from 'components/MainCard';
import CustomAllCharactersField from 'components/custom-components/CustomAllCharactersField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import { useForm, Controller } from 'react-hook-form';

const roles = [
  'Admin',
  'Manager',
  'HR',
  'Employee',
  'Client'
];

const AddDocumentType = ({ onClose, onSave, isEdit = false, initialData = {} }) => {
  const { control, handleSubmit, setValue, watch } = useForm({
    defaultValues: {
      documentType: initialData.documentType || '',
      restrictToRoles: initialData.restrictToRoles || [],
      isMandatory: initialData.isMandatory || 'no',
      sendExpiryNotification: initialData.sendExpiryNotification || false,
      notificationFrequency: initialData.notificationFrequency || 7,
      notificationStopCondition: initialData.notificationStopCondition || 'after',
      notificationStopValue: initialData.notificationStopValue || 3,
      notificationRecipients: initialData.notificationRecipients || []
    }
  });

  const formValues = watch();
  const showNotificationConfig = formValues.isMandatory === 'yesButLater';

  const onSubmit = (data) => {
    onSave(data);
    onClose();
  };

  return (
    <MainCard
      title={isEdit ? 'Edit Document Type' : 'Add Document Type'}
      secondary={
        <IconButton onClick={onClose} size="large">
          <Close />
        </IconButton>
      }
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={3} sx={{ p: 2 }}>
          {/* Document Type */}
          <Grid item xs={12}>
            <CustomAllCharactersField
              name="documentType"
              control={control}
              label="Document Type"
              required
              fullWidth
            />
          </Grid>

          {/* Restrict document to Roles */}
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Restrict document to Roles</InputLabel>
              <CustomDropdownField
                name="restrictToRoles"
                control={control}
                options={roles.map(role => ({ value: role, label: role }))}
                multiple
                renderValue={(selected) => selected.join(', ')}
                fullWidth
              />
            </FormControl>
          </Grid>

          {/* Is Mandatory */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>Is Mandatory</Typography>
            <FormControl component="fieldset">
              <RadioGroup
                row
                name="isMandatory"
                value={formValues.isMandatory}
                onChange={(e) => setValue('isMandatory', e.target.value)}
              >
                <FormControlLabel value="no" control={<Radio />} label="No" />
                <FormControlLabel value="yes" control={<Radio />} label="Yes" />
                <FormControlLabel 
                  value="yesButLater" 
                  control={<Radio />} 
                  label="Yes, But Can Be Added Later" 
                />
              </RadioGroup>
            </FormControl>
          </Grid>

          {/* Send Expiry Notification */}
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Controller
                  name="sendExpiryNotification"
                  control={control}
                  render={({ field }) => (
                    <Switch
                      checked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    />
                  )}
                />
              }
              label="Send Expiry Notification"
            />
          </Grid>

          {/* Configure notification for missing documents - Only shown when 'Yes, But Can Be Added Later' is selected */}
          {showNotificationConfig && (
            <>
              <Grid item xs={12}>
                <Divider />
              </Grid>

              <Grid item xs={12}>
                <Typography variant="h6">Configure notification for missing documents</Typography>
              </Grid>

              {/* Notification Frequency */}
              <Grid item xs={12} container alignItems="center" spacing={2}>
                <Grid item>
                  <Typography>Notification Frequency</Typography>
                </Grid>
                <Grid item xs={3}>
                  <CustomAllCharactersField
                    name="notificationFrequency"
                    control={control}
                    type="number"
                    inputProps={{ min: 1 }}
                    fullWidth
                  />
                </Grid>
                <Grid item>
                  <Typography>Days</Typography>
                </Grid>
              </Grid>

              {/* When do you want to stop notifications? */}
              <Grid item xs={12} container alignItems="center" spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography>When do you want to stop notifications?</Typography>
                </Grid>
                <Grid item xs={12} sm={8} container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={4}>
                    <CustomDropdownField
                      name="notificationStopCondition"
                      control={control}
                      options={[
                        { value: 'after', label: 'After' },
                        { value: 'before', label: 'Before' }
                      ]}
                      fullWidth
                    />
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <CustomAllCharactersField
                      name="notificationStopValue"
                      control={control}
                      type="number"
                      inputProps={{ min: 1 }}
                      fullWidth
                    />
                  </Grid>
                  <Grid item xs>
                    <Typography>Reminders</Typography>
                  </Grid>
                </Grid>
              </Grid>

              {/* Notifications to */}
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  Notifications to (Sends notification to upload the document)
                </Typography>
                <FormControl fullWidth>
                  <CustomDropdownField
                    name="notificationRecipients"
                    control={control}
                    options={roles.map(role => ({ value: role, label: role }))}
                    multiple
                    renderValue={(selected) => selected.join(', ')}
                    fullWidth
                    label="Select Recipients"
                  />
                </FormControl>
              </Grid>
            </>
          )}

          {/* Action Buttons */}
          <Grid item xs={12} container justifyContent="flex-end" spacing={2} sx={{ mt: 2 }}>
            <Grid item>
              <Button variant="outlined" onClick={onClose}>
                Cancel
              </Button>
            </Grid>
            <Grid item>
              <Button 
                variant="contained" 
                color="primary" 
                type="submit"
                disabled={!formValues.documentType}
              >
                {isEdit ? 'Update' : 'Save'}
              </Button>
            </Grid>
          </Grid>
        </Grid>
      </form>
    </MainCard>
  );
};

export default AddDocumentType;