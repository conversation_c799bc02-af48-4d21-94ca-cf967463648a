import {
  <PERSON>, <PERSON><PERSON>, <PERSON>er, IconButton, InputAdornment, Paper, Table, TableBody,
  TableCell, TableContainer, TableHead, TableRow, TextField, Typography, Checkbox
} from '@mui/material';
import { Add, Edit, Delete, Search } from '@mui/icons-material';
import { useState } from 'react';
import ActivityLog from './activitylog';
import CustomTablePagination from 'components/custom-components/CustomPaginationItems';
import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';
import AddDocumentType from './add';
import EditDocumentType from './edit';
import MainCard from 'components/MainCard'; // Assuming you're using a MainCard wrapper

const ClientDocumentTypes = () => {
  const [documentTypes, setDocumentTypes] = useState([
    {
      id: 1,
      documentType: 'Aadhar Card',
      restrictToRoles: ['Employee'],
      isMandatory: 'yes',
      notificationFrequency: 7,
      notificationStopCondition: 'after',
      notificationStopValue: 3,
      notificationRecipients: ['Employee', 'HR'],
      createdBy: 'Admin User',
      lastModified: '2025-06-10T10:30:00Z'
    },
    {
      id: 2,
      documentType: 'Passport',
      restrictToRoles: ['Employee', 'Client'],
      isMandatory: 'yesButLater',
      notificationFrequency: 14,
      notificationStopCondition: 'after',
      notificationStopValue: 5,
      notificationRecipients: ['Employee', 'Manager', 'HR'],
      createdBy: 'Admin User',
      lastModified: '2025-06-09T15:45:00Z'
    }
  ]);

  const [logs, setLogs] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState({ field: 'documentType', direction: 'asc' });
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [editingDoc, setEditingDoc] = useState(null);
  const [inputValue, setInputValue] = useState({ documentType: '', restrictToRoles: [], isMandatory: '', notificationFrequency: 0, notificationStopCondition: '', notificationStopValue: 0, notificationRecipients: [] });
  const [showActivities, setShowActivities] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const filteredDocumentTypes = documentTypes
    .filter((doc) => doc.documentType.toLowerCase().includes(searchTerm.toLowerCase()))
    .sort((a, b) => {
      const aValue = a[sortConfig.field];
      const bValue = b[sortConfig.field];
      if (sortConfig.direction === 'asc') return aValue.localeCompare(bValue);
      return bValue.localeCompare(aValue);
    });

  const handleSort = (field) => {
    setSortConfig((prev) => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const renderSortSymbol = (field) => {
    if (sortConfig.field !== field) return '↑↓';
    return sortConfig.direction === 'asc' ? '↑' : '↓';
  };

  const handleSave = (formData) => {
    const time = new Date().toISOString();
    
    if (editingDoc) {
      // Update existing document
      setDocumentTypes(documentTypes.map(doc => 
        doc.id === editingDoc.id 
          ? { 
              ...formData, 
              id: editingDoc.id, 
              lastModified: time,
              createdBy: doc.createdBy
            } 
          : doc
      ));
    } else {
      // Add new document
      const newDoc = {
        ...formData,
        id: Math.max(0, ...documentTypes.map(doc => doc.id)) + 1,
        createdBy: 'Current User', // Replace with actual user
        lastModified: time
      };
      setDocumentTypes([newDoc, ...documentTypes]);
    }
    
    setLogs([...logs, {
      action: editingDoc ? 'updated' : 'created',
      name: formData.documentType,
      user: 'Current User',
      timestamp: time
    }]);
    
    setInputValue({ documentType: '', restrictToRoles: [], isMandatory: '', notificationFrequency: 0, notificationStopCondition: '', notificationStopValue: 0, notificationRecipients: [] });
    setEditingDoc(null);
    setDrawerOpen(false);
  };

  const handleDelete = (id) => {
    const time = new Date().toISOString();
    setLogs([...logs, {
      action: 'deleted',
      name: documentTypes.find(doc => doc.id === id).documentType,
      user: 'Current User',
      timestamp: time
    }]);
    setDocumentTypes(documentTypes.filter(doc => doc.id !== id));
  };

  const CustomDataGrid = ({ data }) => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell onClick={() => handleSort('documentType')} style={{ cursor: 'pointer' }}>
              Document Type {renderSortSymbol('documentType')}
            </TableCell>
            <TableCell>Restricted To Roles</TableCell>
            <TableCell>Is Mandatory</TableCell>
            <TableCell>Notification Frequency</TableCell>
            <TableCell>Notification Recipients</TableCell>
            <TableCell onClick={() => handleSort('lastModified')} style={{ cursor: 'pointer' }}>
              Last Modified {renderSortSymbol('lastModified')}
            </TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((doc, index) => (
            <TableRow key={index}>
              <TableCell>{doc.documentType}</TableCell>
              <TableCell>{doc.restrictToRoles.join(', ')}</TableCell>
              <TableCell>{doc.isMandatory}</TableCell>
              <TableCell>
                {doc.notificationFrequency} days
                {doc.notificationStopValue > 0 && 
                  `, Stop ${doc.notificationStopCondition} ${doc.notificationStopValue} reminders`}
                
              </TableCell>
              <TableCell>{doc.notificationRecipients.join(', ')}</TableCell>
              <TableCell>{new Date(doc.lastModified).toLocaleString()}</TableCell>
              <TableCell>
                <IconButton onClick={() => {
                  setEditingDoc(doc);
                  setInputValue(doc);
                  setDrawerOpen(true);
                }} size="small">
                  <Edit />
                </IconButton>
                <IconButton 
                  onClick={() => handleDelete(doc.id)} 
                  size="small"
                  color="error"
                >
                  <Delete />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <MainCard sx={{ borderRadius: 2, p: 3, minHeight: '85vh' }}>
      {showActivities ? (
        <>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h5">Client Document Types</Typography>
            <Button onClick={() => setShowActivities(false)} variant="outlined" size="small">Back</Button>
          </Box>
          <ActivityLog
            logs={logs}
            page={page}
            setPage={setPage}
            rowsPerPage={rowsPerPage}
            setRowsPerPage={setRowsPerPage}
          />
        </>
      ) : (
        <>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h5">Client Document Types</Typography>
          </Box>

          <Box display="flex" justifyContent="space-between" mb={2}>
            <TextField
              size="small"
              placeholder="Search"
              InputProps={{
                startAdornment: <InputAdornment position="start"><Search /></InputAdornment>
              }}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Box>
              <Button
                onClick={() => setShowActivities(true)}
                variant="outlined"
                sx={{ mr: 1 }}
                size="small"
              >
                Activities
              </Button>
              <Button
                variant="contained"
                size="small"
                onClick={() => {
                  setEditingDoc(null);
                  setInputValue({ documentType: '', restrictToRoles: [], isMandatory: '', notificationFrequency: 0, notificationStopCondition: '', notificationStopValue: 0, notificationRecipients: [] });
                  setDrawerOpen(true);
                }}
              >
                Add
              </Button>
            </Box>
          </Box>

          <CustomDataGrid
            data={filteredDocumentTypes.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)}
          />

          <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
            <RowsPerPageSelector
              getState={() => ({ pagination: { pageIndex: page, pageSize: rowsPerPage } })}
              setPageSize={(size) => { setRowsPerPage(size); setPage(0); }}
            />
            <CustomTablePagination
              setPageSize={(size) => { setRowsPerPage(size); setPage(0); }}
              setPageIndex={setPage}
              getState={() => ({ pagination: { pageIndex: page, pageSize: rowsPerPage } })}
              getPageCount={() => Math.ceil(filteredDocumentTypes.length / rowsPerPage)}
            />
          </Box>
        </>
      )}

      <Drawer anchor="right" open={drawerOpen} onClose={() => setDrawerOpen(false)}>
        {editingDoc ? (
          <EditDocumentType
            inputValue={inputValue}
            setInputValue={setInputValue}
            onClose={() => setDrawerOpen(false)}
            onSave={handleSave}
          />
        ) : (
          <AddDocumentType
            inputValue={inputValue}
            setInputValue={setInputValue}
            onClose={() => setDrawerOpen(false)}
            onSave={handleSave}
          />
        )}
      </Drawer>
    </MainCard>
  );
};

export default ClientDocumentTypes;
