import { Box, Button, IconButton, <PERSON>Field, Typography } from '@mui/material';
import { Close } from '@mui/icons-material';

const EditClientCategory = ({ onClose, onSave, inputValue, setInputValue }) => {
  const isValid = !!inputValue.trim();

  return (
    <Box p={3} width={300}>
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="h6">Edit Client Category</Typography>
        <IconButton onClick={onClose}><Close /></IconButton>
      </Box>
      <TextField
        label="Client Category"
        fullWidth
        size="small"
        required
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        error={!isValid}
        helperText={!isValid ? 'Client Category is required' : ''}
        sx={{ mt: 2 }}
      />
      <Button
        variant="contained"
        fullWidth
        sx={{ mt: 2 }}
        onClick={onSave}
        disabled={!isValid}
      >
        Save
      </Button>
    </Box>
  );
};

export default EditClientCategory;
