import {
  <PERSON>, <PERSON><PERSON>, Drawer, IconButton, InputAdornment, Paper, Table, TableBody,
  TableCell, TableContainer, TableHead, TableRow, TextField, Typography
} from '@mui/material';
import { Add, Edit, Delete, Search, Close } from '@mui/icons-material';
import { useState } from 'react';
import ActivityLog from './activitylog';
import CustomTablePagination from 'components/custom-components/CustomPaginationItems';
import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';
import AddClientCategory from './add';
import EditClientCategory from './edit';


const ClientCategories = () => {
  const [categories, setCategories] = useState([
    { name: 'Tier 1', createdBy: '<PERSON><PERSON>h<PERSON>n<PERSON>', modifiedBy: '<PERSON><PERSON>h<PERSON>nmu<PERSON>', lastModified: '2025-06-06 18:00:00' },
    { name: 'Direct', createdBy: '<PERSON><PERSON><PERSON><PERSON>', modifiedBy: '<PERSON><PERSON><PERSON><PERSON>', lastModified: '2025-06-06 19:00:00' }
  ]);
  const [logs, setLogs] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState({ field: 'name', direction: 'asc' });
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [editIndex, setEditIndex] = useState(null);
  const [inputValue, setInputValue] = useState('');
  const [showActivities, setShowActivities] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const filteredCategories = categories
    .filter((cat) => cat.name.toLowerCase().includes(searchTerm.toLowerCase()))
    .sort((a, b) => {
      const aValue = a[sortConfig.field];
      const bValue = b[sortConfig.field];
      if (sortConfig.direction === 'asc') return aValue.localeCompare(bValue);
      return bValue.localeCompare(aValue);
    });

  const handleSort = (field) => {
    setSortConfig((prev) => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const renderSortSymbol = (field) => {
    if (sortConfig.field !== field) return '↑↓';
    return sortConfig.direction === 'asc' ? '↑' : '↓';
  };

  const handleSave = () => {
    if (!inputValue.trim()) return;
    const time = new Date().toLocaleString('en-GB');
    if (editIndex !== null) {
      const oldName = categories[editIndex].name;
      const updated = [...categories];
      updated[editIndex] = {
        ...updated[editIndex],
        name: inputValue,
        modifiedBy: 'Prudhvi Kanmuri',
        lastModified: time
      };
      setCategories(updated);
      setLogs([...logs, { action: 'updated', name: inputValue, from: oldName, user: 'Prudhvi Kanmuri', timestamp: time }]);
    } else {
      const newCategory = {
        name: inputValue,
        createdBy: 'Prudhvi Kanmuri',
        modifiedBy: 'Prudhvi Kanmuri',
        lastModified: time
      };
      setCategories([...categories, newCategory]);
      setLogs([...logs, { action: 'created', name: inputValue, user: 'Prudhvi Kanmuri', timestamp: time }]);
    }
    setInputValue('');
    setEditIndex(null);
    setDrawerOpen(false);
  };

  const handleDelete = (index) => {
    const time = new Date().toLocaleString('en-GB');
    setLogs([...logs, { action: 'deleted', name: categories[index].name, user: 'Prudhvi Kanmuri', timestamp: time }]);
    setCategories(categories.filter((_, i) => i !== index));
  };

  const CustomDataGrid = ({ data, onEdit, onDelete }) => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell onClick={() => handleSort('name')} style={{ cursor: 'pointer' }}>
              Client Category {renderSortSymbol('name')}
            </TableCell>
            <TableCell>Created By</TableCell>
            <TableCell>Modified By</TableCell>
            <TableCell onClick={() => handleSort('lastModified')} style={{ cursor: 'pointer' }}>
              Last Modified {renderSortSymbol('lastModified')}
            </TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((cat, index) => (
            <TableRow key={index}>
              <TableCell>{cat.name}</TableCell>
              <TableCell>{cat.createdBy}</TableCell>
              <TableCell>{cat.modifiedBy}</TableCell>
              <TableCell>{cat.lastModified}</TableCell>
              <TableCell>
                <IconButton onClick={() => {
                  const globalIndex = categories.findIndex(c => c.name === cat.name);
                  setDrawerOpen(true);
                  setInputValue(cat.name);
                  setEditIndex(globalIndex);
                }}>
                  <Edit />
                </IconButton>
                <IconButton onClick={() => {
                  const globalIndex = categories.findIndex(c => c.name === cat.name);
                  handleDelete(globalIndex);
                }}>
                  <Delete />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Box p={2}>
      {showActivities ? (
        <>
          <Box display="flex" justifyContent="flex-end">
            <Button onClick={() => setShowActivities(false)} variant="outlined" size="small">Back</Button>
          </Box>
          <ActivityLog
  logs={logs}
  page={page}
  setPage={setPage}
  rowsPerPage={rowsPerPage}
  setRowsPerPage={setRowsPerPage}
/>

        </>
      ) : (
        <>
          <Box display="flex" justifyContent="space-between" mb={2}>
            <TextField
              size="small"
              placeholder="Search"
              InputProps={{
                startAdornment: <InputAdornment position="start"><Search /></InputAdornment>
              }}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Box>
              <Button onClick={() => setShowActivities(true)} variant="outlined" sx={{ mr: 1 }} size="small">
                Activities
              </Button>
              <Button variant="contained" size="small" onClick={() => {
                setDrawerOpen(true);
                setEditIndex(null);
                setInputValue('');
              }}>
                Add
              </Button>
            </Box>
          </Box>

          <CustomDataGrid
            data={filteredCategories.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)}
            onEdit={(index) => {}}
            onDelete={() => {}}
          />

          <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
            <RowsPerPageSelector
              getState={() => ({ pagination: { pageIndex: page, pageSize: rowsPerPage } })}
              setPageSize={(size) => { setRowsPerPage(size); setPage(0); }}
            />
            <CustomTablePagination
              setPageSize={(size) => { setRowsPerPage(size); setPage(0); }}
              setPageIndex={setPage}
              getState={() => ({ pagination: { pageIndex: page, pageSize: rowsPerPage } })}
              getPageCount={() => Math.ceil(filteredCategories.length / rowsPerPage)}
            />
          </Box>
        </>
      )}

<Drawer anchor="right" open={drawerOpen} onClose={() => setDrawerOpen(false)}>
  {editIndex !== null ? (
    <EditClientCategory
      inputValue={inputValue}
      setInputValue={setInputValue}
      onClose={() => setDrawerOpen(false)}
      onSave={handleSave}
    />
  ) : (
    <AddClientCategory
      inputValue={inputValue}
      setInputValue={setInputValue}
      onClose={() => setDrawerOpen(false)}
      onSave={handleSave}
    />
  )}
</Drawer>

    </Box>
  );
};

export default ClientCategories;
