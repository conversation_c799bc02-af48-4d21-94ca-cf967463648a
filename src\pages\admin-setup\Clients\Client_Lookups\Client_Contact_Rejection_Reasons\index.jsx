import {
  <PERSON>, But<PERSON>, Drawer, IconButton, InputAdornment, Paper, Table, TableBody,
  TableCell, TableContainer, TableHead, TableRow, TextField, Typography
} from '@mui/material';
import { Add, Edit, Delete, Search, Close } from '@mui/icons-material';
import { useState } from 'react';
import ActivityLog from './activitylog';
import CustomTablePagination from 'components/custom-components/CustomPaginationItems';
import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';
import AddClientContactRejectionReason from './add';
import EditClientContactRejectionReason from './edit';

const ClientContactRejectionReasons = () => {
  const [rejectionReasons, setRejectionReasons] = useState([
    { reason: 'Others', createdBy: '<PERSON><PERSON>h<PERSON> Kan<PERSON>ri', modifiedBy: 'Prudhvi Kanmuri', lastModified: '12/06/23 20:05:49' },
    { reason: 'No longer associated with the client', createdBy: '<PERSON><PERSON><PERSON><PERSON>', modifiedBy: '<PERSON><PERSON><PERSON><PERSON>', lastModified: '12/06/23 20:05:49' },
    { reason: 'Not Contacted', createdBy: 'Prudhvi Kanmuri', modifiedBy: 'Prudhvi Kanmuri', lastModified: '12/06/23 20:05:49' },
    { reason: 'Wrong contact', createdBy: 'Prudhvi Kanmuri', modifiedBy: 'Prudhvi Kanmuri', lastModified: '12/06/23 20:05:49' }
  ]);
  const [logs, setLogs] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState({ field: 'reason', direction: 'asc' });
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [editIndex, setEditIndex] = useState(null);
  const [inputValue, setInputValue] = useState('');
  const [showActivities, setShowActivities] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const filteredRejectionReasons = rejectionReasons
    .filter((reason) => reason.reason.toLowerCase().includes(searchTerm.toLowerCase()))
    .sort((a, b) => {
      const aValue = a[sortConfig.field];
      const bValue = b[sortConfig.field];
      if (sortConfig.direction === 'asc') return aValue.localeCompare(bValue);
      return bValue.localeCompare(aValue);
    });

  const handleSort = (field) => {
    setSortConfig((prev) => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const renderSortSymbol = (field) => {
    if (sortConfig.field !== field) return '↑↓';
    return sortConfig.direction === 'asc' ? '↑' : '↓';
  };

  const handleSave = () => {
    if (!inputValue.trim()) return;
    const time = new Date().toLocaleString('en-GB');
    if (editIndex !== null) {
      const oldReason = rejectionReasons[editIndex].reason;
      const updated = [...rejectionReasons];
      updated[editIndex] = {
        ...updated[editIndex],
        reason: inputValue,
        modifiedBy: 'Prudhvi Kanmuri',
        lastModified: time
      };
      setRejectionReasons(updated);
      setLogs([...logs, { action: 'updated', reason: inputValue, from: oldReason, user: 'Prudhvi Kanmuri', timestamp: time }]);
    } else {
      const newReason = {
        reason: inputValue,
        createdBy: 'Prudhvi Kanmuri',
        modifiedBy: 'Prudhvi Kanmuri',
        lastModified: time
      };
      setRejectionReasons([...rejectionReasons, newReason]);
      setLogs([...logs, { action: 'created', reason: inputValue, user: 'Prudhvi Kanmuri', timestamp: time }]);
    }
    setInputValue('');
    setEditIndex(null);
    setDrawerOpen(false);
  };

  const handleDelete = (index) => {
    const time = new Date().toLocaleString('en-GB');
    setLogs([...logs, { action: 'deleted', reason: rejectionReasons[index].reason, user: 'Prudhvi Kanmuri', timestamp: time }]);
    setRejectionReasons(rejectionReasons.filter((_, i) => i !== index));
  };

  const CustomDataGrid = ({ data, onEdit, onDelete }) => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell onClick={() => handleSort('reason')} style={{ cursor: 'pointer' }}>
              Rejection Reason {renderSortSymbol('reason')}
            </TableCell>
            <TableCell>Created By</TableCell>
            <TableCell>Modified By</TableCell>
            <TableCell onClick={() => handleSort('lastModified')} style={{ cursor: 'pointer' }}>
              Last Modified {renderSortSymbol('lastModified')}
            </TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((reason, index) => (
            <TableRow key={index}>
              <TableCell>{reason.reason}</TableCell>
              <TableCell>{reason.createdBy}</TableCell>
              <TableCell>{reason.modifiedBy}</TableCell>
              <TableCell>{reason.lastModified}</TableCell>
              <TableCell>
                <IconButton onClick={() => {
                  const globalIndex = rejectionReasons.findIndex(r => r.reason === reason.reason);
                  setDrawerOpen(true);
                  setInputValue(reason.reason);
                  setEditIndex(globalIndex);
                }}>
                  <Edit />
                </IconButton>
                <IconButton onClick={() => {
                  const globalIndex = rejectionReasons.findIndex(r => r.reason === reason.reason);
                  handleDelete(globalIndex);
                }}>
                  <Delete />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Box p={2}>
      {showActivities ? (
        <>
          <Box display="flex" justifyContent="flex-end">
            <Button onClick={() => setShowActivities(false)} variant="outlined" size="small">Back</Button>
          </Box>
          <ActivityLog
  logs={logs}
  page={page}
  setPage={setPage}
  rowsPerPage={rowsPerPage}
  setRowsPerPage={setRowsPerPage}
/>

        </>
      ) : (
        <>
          <Box display="flex" justifyContent="space-between" mb={2}>
            <TextField
              size="small"
              placeholder="Search"
              InputProps={{
                startAdornment: <InputAdornment position="start"><Search /></InputAdornment>
              }}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Box>
              <Button onClick={() => setShowActivities(true)} variant="outlined" sx={{ mr: 1 }} size="small">
                Activities
              </Button>
              <Button variant="contained" size="small" onClick={() => {
                setDrawerOpen(true);
                setEditIndex(null);
                setInputValue('');
              }}>
                Add
              </Button>
            </Box>
          </Box>

          <CustomDataGrid
            data={filteredRejectionReasons.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)}
            onEdit={(index) => {}}
            onDelete={() => {}}
          />

          <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
            <RowsPerPageSelector
              getState={() => ({ pagination: { pageIndex: page, pageSize: rowsPerPage } })}
              setPageSize={(size) => { setRowsPerPage(size); setPage(0); }}
            />
            <CustomTablePagination
              setPageSize={(size) => { setRowsPerPage(size); setPage(0); }}
              setPageIndex={setPage}
              getState={() => ({ pagination: { pageIndex: page, pageSize: rowsPerPage } })}
              getPageCount={() => Math.ceil(filteredRejectionReasons.length / rowsPerPage)}
            />
          </Box>
        </>
      )}

<Drawer anchor="right" open={drawerOpen} onClose={() => setDrawerOpen(false)}>
  {editIndex !== null ? (
    <EditClientContactRejectionReason
      inputValue={inputValue}
      setInputValue={setInputValue}
      onClose={() => setDrawerOpen(false)}
      onSave={handleSave}
    />
  ) : (
    <AddClientContactRejectionReason
      inputValue={inputValue}
      setInputValue={setInputValue}
      onClose={() => setDrawerOpen(false)}
      onSave={handleSave}
    />
  )}
</Drawer>
    </Box>
  );
};

export default ClientContactRejectionReasons; 