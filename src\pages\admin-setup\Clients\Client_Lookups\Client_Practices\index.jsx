import {
  <PERSON>, <PERSON><PERSON>, Drawer, IconButton, InputAdornment, Paper, Table, TableBody,
  TableCell, TableContainer, TableHead, TableRow, TextField, Typography
} from '@mui/material';
import { Add, Edit, Delete, Search, Close } from '@mui/icons-material';
import { useState } from 'react';
import ActivityLog from './activitylog';
import CustomTablePagination from 'components/custom-components/CustomPaginationItems';
import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';
import AddClientPractice from './add';
import EditClientPractice from './edit';

const ClientPractices = () => {
  const [practices, setPractices] = useState([]);
  const [logs, setLogs] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState({ field: 'practice', direction: 'asc' });
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [editIndex, setEditIndex] = useState(null);
  const [inputValue, setInputValue] = useState('');
  const [showActivities, setShowActivities] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const filteredPractices = practices
    .filter((practice) => practice.practice.toLowerCase().includes(searchTerm.toLowerCase()))
    .sort((a, b) => {
      const aValue = a[sortConfig.field];
      const bValue = b[sortConfig.field];
      if (sortConfig.direction === 'asc') return aValue.localeCompare(bValue);
      return bValue.localeCompare(aValue);
    });

  const handleSort = (field) => {
    setSortConfig((prev) => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const renderSortSymbol = (field) => {
    if (sortConfig.field !== field) return '↑↓';
    return sortConfig.direction === 'asc' ? '↑' : '↓';
  };

  const handleSave = () => {
    if (!inputValue.trim()) return;
    const time = new Date().toLocaleString('en-GB');
    if (editIndex !== null) {
      const oldPractice = practices[editIndex].practice;
      const updated = [...practices];
      updated[editIndex] = {
        ...updated[editIndex],
        practice: inputValue,
        modifiedBy: 'Prudhvi Kanmuri',
        lastModified: time
      };
      setPractices(updated);
      setLogs([...logs, { action: 'updated', practice: inputValue, from: oldPractice, user: 'Prudhvi Kanmuri', timestamp: time }]);
    } else {
      const newPractice = {
        practice: inputValue,
        createdBy: 'Prudhvi Kanmuri',
        modifiedBy: 'Prudhvi Kanmuri',
        lastModified: time
      };
      setPractices([...practices, newPractice]);
      setLogs([...logs, { action: 'created', practice: inputValue, user: 'Prudhvi Kanmuri', timestamp: time }]);
    }
    setInputValue('');
    setEditIndex(null);
    setDrawerOpen(false);
  };

  const handleDelete = (index) => {
    const time = new Date().toLocaleString('en-GB');
    setLogs([...logs, { action: 'deleted', practice: practices[index].practice, user: 'Prudhvi Kanmuri', timestamp: time }]);
    setPractices(practices.filter((_, i) => i !== index));
  };

  const CustomDataGrid = ({ data, onEdit, onDelete }) => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell onClick={() => handleSort('practice')} style={{ cursor: 'pointer' }}>
              Client Practices {renderSortSymbol('practice')}
            </TableCell>
            <TableCell>Created By</TableCell>
            <TableCell>Modified By</TableCell>
            <TableCell onClick={() => handleSort('lastModified')} style={{ cursor: 'pointer' }}>
              Last Modified {renderSortSymbol('lastModified')}
            </TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((practice, index) => (
            <TableRow key={index}>
              <TableCell>{practice.practice}</TableCell>
              <TableCell>{practice.createdBy}</TableCell>
              <TableCell>{practice.modifiedBy}</TableCell>
              <TableCell>{practice.lastModified}</TableCell>
              <TableCell>
                <IconButton onClick={() => {
                  const globalIndex = practices.findIndex(p => p.practice === practice.practice);
                  setDrawerOpen(true);
                  setInputValue(practice.practice);
                  setEditIndex(globalIndex);
                }}>
                  <Edit />
                </IconButton>
                <IconButton onClick={() => {
                  const globalIndex = practices.findIndex(p => p.practice === practice.practice);
                  handleDelete(globalIndex);
                }}>
                  <Delete />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Box p={2}>
      {showActivities ? (
        <>
          <Box display="flex" justifyContent="flex-end">
            <Button onClick={() => setShowActivities(false)} variant="outlined" size="small">Back</Button>
          </Box>
          <ActivityLog
  logs={logs}
  page={page}
  setPage={setPage}
  rowsPerPage={rowsPerPage}
  setRowsPerPage={setRowsPerPage}
/>

        </>
      ) : (
        <>
          <Box display="flex" justifyContent="space-between" mb={2}>
            <TextField
              size="small"
              placeholder="Search"
              InputProps={{
                startAdornment: <InputAdornment position="start"><Search /></InputAdornment>
              }}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Box>
              <Button onClick={() => setShowActivities(true)} variant="outlined" sx={{ mr: 1 }} size="small">
                Activities
              </Button>
              <Button variant="contained" size="small" onClick={() => {
                setDrawerOpen(true);
                setEditIndex(null);
                setInputValue('');
              }}>
                Add
              </Button>
            </Box>
          </Box>

          <CustomDataGrid
            data={filteredPractices.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)}
            onEdit={(index) => {}}
            onDelete={() => {}}
          />

          <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
            <RowsPerPageSelector
              getState={() => ({ pagination: { pageIndex: page, pageSize: rowsPerPage } })}
              setPageSize={(size) => { setRowsPerPage(size); setPage(0); }}
            />
            <CustomTablePagination
              setPageSize={(size) => { setRowsPerPage(size); setPage(0); }}
              setPageIndex={setPage}
              getState={() => ({ pagination: { pageIndex: page, pageSize: rowsPerPage } })}
              getPageCount={() => Math.ceil(filteredPractices.length / rowsPerPage)}
            />
          </Box>
        </>
      )}

<Drawer anchor="right" open={drawerOpen} onClose={() => setDrawerOpen(false)}>
  {editIndex !== null ? (
    <EditClientPractice
      inputValue={inputValue}
      setInputValue={setInputValue}
      onClose={() => setDrawerOpen(false)}
      onSave={handleSave}
    />
  ) : (
    <AddClientPractice
      inputValue={inputValue}
      setInputValue={setInputValue}
      onClose={() => setDrawerOpen(false)}
      onSave={handleSave}
    />
  )}
</Drawer>
    </Box>
  );
};

export default ClientPractices; 