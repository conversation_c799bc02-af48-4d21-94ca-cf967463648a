import { Box, Button, IconButton, TextField, Typography } from '@mui/material';
import { Close } from '@mui/icons-material';

const EditClientStatus = ({ onClose, onSave, inputValue, setInputValue }) => {
  const isValid = !!inputValue.trim();

  return (
    <Box p={3} width={300}>
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="h6">Edit Client Status</Typography>
        <IconButton onClick={onClose}><Close /></IconButton>
      </Box>
      <TextField
        label="Client Status"
        fullWidth
        size="small"
        required
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        error={!isValid}
        helperText={!isValid ? 'Client Status is required' : ''}
        sx={{ mt: 2 }}
      />
      <Button
        variant="contained"
        fullWidth
        sx={{ mt: 2 }}
        onClick={onSave}
        disabled={!isValid}
      >
        Save
      </Button>
    </Box>  
  );
};

export default EditClientStatus;
