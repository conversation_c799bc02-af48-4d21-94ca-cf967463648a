import {
  <PERSON>, <PERSON><PERSON>, Drawer, <PERSON>con<PERSON>utton, InputAdornment, Paper, Table, TableBody,
  TableCell, TableContainer, TableHead, TableRow, TextField, Typography
} from '@mui/material';
import { Add, Edit, Delete, Search, Close } from '@mui/icons-material';
import { useState } from 'react';
import ActivityLog from './activitylog';
import CustomTablePagination from 'components/custom-components/CustomPaginationItems';
import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';
import AddClientStatus from './add';
import EditClientStatus from './edit';

const ClientStatuses = () => {
  const [statuses, setStatuses] = useState([
    { status: 'Negotiation', createdBy: '<PERSON>rudhvi Kanmuri', modifiedBy: 'Prudhvi Kanmuri', lastModified: '12/06/23 20:05:49' },
    { status: 'Archive', createdBy: '<PERSON><PERSON>h<PERSON>', modifiedBy: '<PERSON><PERSON><PERSON><PERSON>', lastModified: '12/06/23 20:05:49' },
    { status: 'DNC', createdBy: '<PERSON><PERSON>h<PERSON>', modifiedBy: '<PERSON><PERSON>h<PERSON>mu<PERSON>', lastModified: '12/06/23 20:05:49' },
    { status: 'New Lead', createdBy: 'Prudhvi Kanmuri', modifiedBy: 'Prudhvi Kanmuri', lastModified: '12/06/23 20:05:49' },
    { status: 'Inactive', createdBy: 'Prudhvi Kanmuri', modifiedBy: 'Prudhvi Kanmuri', lastModified: '12/06/23 20:05:49' },
    { status: 'Active', createdBy: 'Prudhvi Kanmuri', modifiedBy: 'Prudhvi Kanmuri', lastModified: '12/06/23 20:05:49' }
  ]);
  const [logs, setLogs] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState({ field: 'status', direction: 'asc' });
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [editIndex, setEditIndex] = useState(null);
  const [inputValue, setInputValue] = useState('');
  const [showActivities, setShowActivities] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const filteredStatuses = statuses
    .filter((status) => status.status.toLowerCase().includes(searchTerm.toLowerCase()))
    .sort((a, b) => {
      const aValue = a[sortConfig.field];
      const bValue = b[sortConfig.field];
      if (sortConfig.direction === 'asc') return aValue.localeCompare(bValue);
      return bValue.localeCompare(aValue);
    });

  const handleSort = (field) => {
    setSortConfig((prev) => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const renderSortSymbol = (field) => {
    if (sortConfig.field !== field) return '↑↓';
    return sortConfig.direction === 'asc' ? '↑' : '↓';
  };

  const handleSave = () => {
    if (!inputValue.trim()) return;
    const time = new Date().toLocaleString('en-GB');
    if (editIndex !== null) {
      const oldStatus = statuses[editIndex].status;
      const updated = [...statuses];
      updated[editIndex] = {
        ...updated[editIndex],
        status: inputValue,
        modifiedBy: 'Prudhvi Kanmuri',
        lastModified: time
      };
      setStatuses(updated);
      setLogs([...logs, { action: 'updated', status: inputValue, from: oldStatus, user: 'Prudhvi Kanmuri', timestamp: time }]);
    } else {
      const newStatus = {
        status: inputValue,
        createdBy: 'Prudhvi Kanmuri',
        modifiedBy: 'Prudhvi Kanmuri',
        lastModified: time
      };
      setStatuses([...statuses, newStatus]);
      setLogs([...logs, { action: 'created', status: inputValue, user: 'Prudhvi Kanmuri', timestamp: time }]);
    }
    setInputValue('');
    setEditIndex(null);
    setDrawerOpen(false);
  };

  const handleDelete = (index) => {
    const time = new Date().toLocaleString('en-GB');
    setLogs([...logs, { action: 'deleted', status: statuses[index].status, user: 'Prudhvi Kanmuri', timestamp: time }]);
    setStatuses(statuses.filter((_, i) => i !== index));
  };

  const CustomDataGrid = ({ data, onEdit, onDelete }) => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell onClick={() => handleSort('status')} style={{ cursor: 'pointer' }}>
              Client Status {renderSortSymbol('status')}
            </TableCell>
            <TableCell>Created By</TableCell>
            <TableCell>Modified By</TableCell>
            <TableCell onClick={() => handleSort('lastModified')} style={{ cursor: 'pointer' }}>
              Last Modified {renderSortSymbol('lastModified')}
            </TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((status, index) => (
            <TableRow key={index}>
              <TableCell>{status.status}</TableCell>
              <TableCell>{status.createdBy}</TableCell>
              <TableCell>{status.modifiedBy}</TableCell>
              <TableCell>{status.lastModified}</TableCell>
              <TableCell>
                <IconButton onClick={() => {
                  const globalIndex = statuses.findIndex(s => s.status === status.status);
                  setDrawerOpen(true);
                  setInputValue(status.status);
                  setEditIndex(globalIndex);
                }}>
                  <Edit />
                </IconButton>
                <IconButton onClick={() => {
                  const globalIndex = statuses.findIndex(s => s.status === status.status);
                  handleDelete(globalIndex);
                }}>
                  <Delete />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Box p={2}>
      {showActivities ? (
        <>
          <Box display="flex" justifyContent="flex-end">
            <Button onClick={() => setShowActivities(false)} variant="outlined" size="small">Back</Button>
          </Box>
          <ActivityLog
            logs={logs}
            page={page}
            setPage={setPage}
            rowsPerPage={rowsPerPage}
            setRowsPerPage={setRowsPerPage}
          />
        </>
      ) : (
        <>
          <Box display="flex" justifyContent="space-between" mb={2}>
            <TextField
              size="small"
              placeholder="Search"
              InputProps={{
                startAdornment: <InputAdornment position="start"><Search /></InputAdornment>
              }}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Box>
              <Button onClick={() => setShowActivities(true)} variant="outlined" sx={{ mr: 1 }} size="small">
                Activities
              </Button>
              <Button variant="contained" size="small" onClick={() => {
                setDrawerOpen(true);
                setEditIndex(null);
                setInputValue('');
              }}>
                Add
              </Button>
            </Box>
          </Box>

          <CustomDataGrid
            data={filteredStatuses.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)}
            onEdit={(index) => {}}
            onDelete={() => {}}
          />

          <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
            <RowsPerPageSelector
              getState={() => ({ pagination: { pageIndex: page, pageSize: rowsPerPage } })}
              setPageSize={(size) => { setRowsPerPage(size); setPage(0); }}
            />
            <CustomTablePagination
              setPageSize={(size) => { setRowsPerPage(size); setPage(0); }}
              setPageIndex={setPage}
              getState={() => ({ pagination: { pageIndex: page, pageSize: rowsPerPage } })}
              getPageCount={() => Math.ceil(filteredStatuses.length / rowsPerPage)}
            />
          </Box>
        </>
      )}

      {!showActivities && (
        <Box
          sx={{
            position: 'fixed',
            bottom: 0,
            left: 362,
            right: 40,
            bgcolor: 'white',
            boxShadow: '0 -2px 10px rgba(233, 221, 221, 0.91)',
            p: 2,
            display: 'flex',
            justifyContent: 'center',
            zIndex: 10
          }}
        >
          <Button variant="contained" color="primary" onClick={handleSave}>
            Save
          </Button>
        </Box>
      )}

      <Drawer anchor="right" open={drawerOpen} onClose={() => setDrawerOpen(false)}>
        {editIndex !== null ? (
          <EditClientStatus
            inputValue={inputValue}
            setInputValue={setInputValue}
            onClose={() => setDrawerOpen(false)}
            onSave={handleSave}
          />
        ) : (
          <AddClientStatus
            inputValue={inputValue}
            setInputValue={setInputValue}
            onClose={() => setDrawerOpen(false)}
            onSave={handleSave}
          />
        )}
      </Drawer>
    </Box>
  );
};

export default ClientStatuses; 