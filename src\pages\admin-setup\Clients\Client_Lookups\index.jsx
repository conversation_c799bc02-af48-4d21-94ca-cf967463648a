import React, { useState } from 'react';
import { Typography, Box, Tabs, Tab } from '@mui/material';
import MainCard from 'components/MainCard';
import ClientCategories from './Client_Categories';
import ClientContactRejectionReasons from './Client_Contact_Rejection_Reasons';
import ClientContactStatuses from './Client_Contact_Statuses';
import ClientPractices from './Client_Practices';
import ClientStatuses from './Client_Statuses';

const ClientLookups = () => {
  const [selectedTab, setSelectedTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const renderTabContent = () => {
    switch (selectedTab) {
      case 0:
        return <ClientCategories />;
      case 1:
        return <ClientContactRejectionReasons />;
      case 2:
        return <ClientContactStatuses />;
      case 3:
        return <ClientPractices />;
      case 4:
        return <ClientStatuses />;
      default:
        return null;
    }
  };

  return (
    <MainCard sx={{ borderRadius: '2%', p: 2 }}>
      <Typography variant="h5" gutterBottom>Client Lookups</Typography>
      <Tabs
        value={selectedTab}
        onChange={handleTabChange}
        aria-label="client lookups tabs"
        sx={{ mb: 2, ml: -2, alignItems: 'flex-start', justifyContent: 'flex-start' }}
        orientation="horizontal"
      >
        <Tab label="Client Categories" />
        <Tab label="Client Contact Rejection Reasons" />
        <Tab label="Client Contact Statuses" />
        <Tab label="Client Practices" />
        <Tab label="Client Statuses" />
      </Tabs>
      <Box sx={{ ml: 0 }}>
        {renderTabContent()}
      </Box>
    </MainCard>
  );
};

export default ClientLookups;
