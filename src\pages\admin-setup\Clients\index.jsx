import { useState } from 'react';
import { Grid, List, ListItemButton, ListItemText, Box } from '@mui/material';
import MainCard from 'components/MainCard';
import ClientLookups from './Client_Lookups';
import ClientContactVisibility from './Client_Contact_Visibility';
import ClientDocumentTypes from './Client_Document_Types';
import ClientSettings from './Client_Settings';
import ClientTabOrder from './Client_Tab_Order';

// ==============================|| SAMPLE PAGE ||============================== //

export default function AdminSetupClients() {
  const [selectedTab, setSelectedTab] = useState(0);

  const handleListItemClick = (index) => {
    setSelectedTab(index);
  };

  const renderTabContent = () => {
    switch (selectedTab) {
      case 0:
        return <ClientLookups />;
      case 1:
        return <ClientContactVisibility />;
      case 2:
        return <ClientDocumentTypes />;
      case 3:
        return <ClientSettings />;
      case 4:
        return <ClientTabOrder />;
      default:
        return null;
    }
  };

  const menuItems = [
    'Client Lookups',
    'Client Contact Visibility',
    'Client Document Types',
    'Client Settings',
    'Client Tab Order'
  ];

  return (
    <Grid container spacing={3}>
      {/* Sidebar */}
      <Grid item xs={12} sm={4} md={3} lg={3} xl={3} sx={{ position: 'sticky', top: 60, alignSelf: 'flex-start', zIndex: 80 }}>
        <MainCard sx={{ borderRadius: '2%' }}>
          <List component="nav">
            {menuItems.map((text, index) => (
              <ListItemButton
                key={text}
                selected={selectedTab === index}
                onClick={() => handleListItemClick(index)}
              >
                <ListItemText primary={text} />
              </ListItemButton>
            ))}
          </List>
        </MainCard>
      </Grid>

      {/* Main Content */}
      <Grid item xs={12} sm={12} md={9}>
        <Box sx={{ mt: 0 }}>{renderTabContent()}</Box>
      </Grid>
    </Grid>
  );
}
