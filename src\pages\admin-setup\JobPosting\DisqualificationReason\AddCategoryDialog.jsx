import React, { useEffect } from 'react';
import { <PERSON>er, Typo<PERSON>, Box, Divider, IconButton, Button, Grid, Stack, InputLabel } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';

const validationSchema = yup.object().shape({
  disqualification_category: yup.string().required('Disqualification Category is required')
});

const AddCategoryDialog = ({ open, onClose, onSave }) => {
  const {
    handleSubmit,
    control,
    reset,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(validationSchema)
  });

  const onSubmit = (data) => {
    onSave(data);
    reset();
    onClose(); // Optional: close on save
  };

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box sx={{ width: { xs: '100%', sm: 400 }, height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Sticky Header */}
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">Add Disqualification Category</Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <Divider />

        {/* Form */}
        <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel>Disqualification Category</InputLabel>
                <CustomDropdownField
                  name="disqualification_category"
                  control={control}
                  placeholder="Select Category"
                  options={[
                    { value: 'Category A', label: 'Category A' },
                    { value: 'Category B', label: 'Category B' },
                    { value: 'Category C', label: 'Category C' }
                  ]}
                  error={!!errors.disqualification_category}
                  helperText={errors.disqualification_category?.message}
                />
              </Stack>
            </Grid>
          </Grid>
        </Box>

        {/* Footer */}
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            onClick={() => {
              reset();
              onClose();
            }}
            size="small"
            variant="outlined"
          >
            Cancel
          </Button>
          <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(onSubmit)}>
            Add
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
};

export default AddCategoryDialog;
