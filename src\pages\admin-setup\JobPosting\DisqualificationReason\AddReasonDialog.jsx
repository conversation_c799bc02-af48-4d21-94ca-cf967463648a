import React, { useEffect } from 'react';
import { Drawer, Typo<PERSON>, Box, Divider, IconButton, Button, Grid, Stack, InputLabel } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomNameField from 'components/custom-components/CustomNameField';

const validationSchema = yup.object().shape({
  restricted_to_role: yup.string().required('Restricted to Role is required'),
  disqualification_reason_description: yup.string().required('Description is required'),
  disqualification_category: yup.string().required('Disqualification Category is required')
});

const AddReasonDialog = ({ open, onClose, onSave, categories }) => {
  const {
    handleSubmit,
    control,
    reset,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(validationSchema)
  });

  const onSubmit = (data) => {
    onSave(data);
    reset();
    onClose(); // Optionally close drawer on save
  };

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box sx={{ width: { xs: '100%', sm: 500 }, height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Sticky Header */}
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">Add Disqualification Reason</Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <Divider />

        {/* Form Content */}
        <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ p: 3, overflowY: 'auto', flexGrow: 1 }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel>Restricted To Role</InputLabel>
                <CustomDropdownField
                  name="restricted_to_role"
                  control={control}
                  placeholder="Select Role"
                  options={[
                    { value: 'Job Publisher', label: 'Job Publisher' },
                    { value: 'Primary Recruiter', label: 'Primary Recruiter' },
                    { value: 'Recruitment Manager', label: 'Recruitment Manager' },
                    { value: 'Sales Manager', label: 'Sales Manager' },
                    { value: 'Others', label: 'Others' }
                  ]}
                  error={!!errors.restricted_to_role}
                  helperText={errors.restricted_to_role?.message}
                />
              </Stack>
            </Grid>

            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel>Disqualification Reason Description</InputLabel>
                <CustomNameField
                  name="disqualification_reason_description"
                  control={control}
                  placeholder="Enter Description"
                  error={!!errors.disqualification_reason_description}
                  helperText={errors.disqualification_reason_description?.message}
                />
              </Stack>
            </Grid>

            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel>Disqualification Category</InputLabel>
                <CustomDropdownField
                  name="disqualification_category"
                  control={control}
                  placeholder="Select Category"
                  options={categories.map((cat) => ({
                    value: cat.disqualification_category,
                    label: cat.disqualification_category
                  }))}
                  error={!!errors.disqualification_category}
                  helperText={errors.disqualification_category?.message}
                />
              </Stack>
            </Grid>
          </Grid>
        </Box>

        {/* Footer */}
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button onClick={onClose} size="small" variant="outlined">
            Cancel
          </Button>
          <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(onSubmit)}>
            Add
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
};

export default AddReasonDialog;
