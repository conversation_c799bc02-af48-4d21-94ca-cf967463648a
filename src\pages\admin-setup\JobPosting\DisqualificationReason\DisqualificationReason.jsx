import React, { useState } from 'react';
import { Box, Typography, <PERSON>bs, Tab, Button, Grid, Tooltip, IconButton, Menu, MenuItem } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddCategoryDialog from './AddCategoryDialog';
import EditCategoryDialog from './EditCategoryDialog';
import AddReasonDialog from './AddReasonDialog';
import EditReasonDialog from './EditReasonDialog';

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div role="tabpanel" hidden={value !== index} id={`simple-tabpanel-${index}`} aria-labelledby={`simple-tab-${index}`} {...other}>
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`
  };
}

function DisqualificationReason() {
  const [selectedTab, setSelectedTab] = useState(0);
  const [openAddCategoryDialog, setOpenAddCategoryDialog] = useState(false);
  const [openAddReasonDialog, setOpenAddReasonDialog] = useState(false);
  const [openEditCategoryDialog, setOpenEditCategoryDialog] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [openEditReasonDialog, setOpenEditReasonDialog] = useState(false);
  const [selectedReason, setSelectedReason] = useState(null);
  const [categoryDetails, setCategoryDetails] = useState([
    { id: 1, disqualification_category: 'Category A', created_by: 'Admin', updated_by: 'Admin', last_modified: '2023-01-01' },
    { id: 2, disqualification_category: 'Category B', created_by: 'Admin', updated_by: 'Admin', last_modified: '2023-01-05' }
  ]);
  const { control } = useForm();
  const [reasonDetails, setReasonDetails] = useState([
    {
      id: 1,
      description: 'Reason 1 Description',
      disqualification_category: 'Category A',
      restricted_to_role: 'Job Publisher',
      created_by: 'User1',
      updated_by: 'User1',
      last_modified: '2023-01-01'
    },
    {
      id: 2,
      description: 'Reason 2 Description',
      disqualification_category: 'Category B',
      restricted_to_role: 'Primary Recruiter',
      created_by: 'User2',
      updated_by: 'User2',
      last_modified: '2023-01-06'
    }
  ]);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const handleAddCategoryDialogOpen = () => {
    setOpenAddCategoryDialog(true);
  };

  const handleAddCategoryDialogClose = () => {
    setOpenAddCategoryDialog(false);
  };

  const handleCategorySave = (data) => {
    setCategoryDetails((prev) => [
      ...prev,
      { id: prev.length + 1, ...data, created_by: 'User', updated_by: 'User', last_modified: new Date().toISOString().slice(0, 10) }
    ]);
    handleAddCategoryDialogClose();
  };

  const handleCategoryUpdate = (updatedCategory) => {
    setCategoryDetails((prev) => prev.map((cat) => (cat.id === updatedCategory.id ? updatedCategory : cat)));
    setOpenEditCategoryDialog(false);
    setSelectedCategory(null);
  };

  const handleAddReasonDialogOpen = () => {
    setOpenAddReasonDialog(true);
  };

  const handleAddReasonDialogClose = () => {
    setOpenAddReasonDialog(false);
  };

  const handleReasonSave = (data) => {
    setReasonDetails((prev) => [
      ...prev,
      { id: prev.length + 1, ...data, created_by: 'User', updated_by: 'User', last_modified: new Date().toISOString().slice(0, 10) }
    ]);
    handleAddReasonDialogClose();
  };

  const handleReasonUpdate = (updatedReason) => {
    setReasonDetails((prev) => prev.map((reason) => (reason.id === updatedReason.id ? updatedReason : reason)));
    setOpenEditReasonDialog(false);
    setSelectedReason(null);
  };

  const CategoryActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedCategory(params.row);
      setOpenEditCategoryDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete category:', params.row);
      setCategoryDetails((prev) => prev.filter((cat) => cat.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const ReasonActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedReason(params.row);
      setOpenEditReasonDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete reason:', params.row);
      setReasonDetails((prev) => prev.filter((reason) => reason.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const categoryColumns = [
    {
      field: 'disqualification_category',
      headerName: 'Disqualification Category',
      flex: 1,
      minWidth: 200,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'Created By',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'updated_by',
      headerName: 'Updated By',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'last_modified',
      headerName: 'Last Modified',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <CategoryActionCell params={params} />
    }
  ];

  const reasonColumns = [
    {
      field: 'description',
      headerName: 'Description',
      flex: 1,
      minWidth: 200,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'disqualification_category',
      headerName: 'Disqualification Category',
      flex: 1,
      minWidth: 200,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'restricted_to_role',
      headerName: 'Restricted Role',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'Created By',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'updated_by',
      headerName: 'Updated By',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <ReasonActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <MainCard
      title="Disqualification Reasons"
      sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
    >
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={selectedTab} onChange={handleTabChange} aria-label="disqualification reason tabs">
          <Tab label="Categories" {...a11yProps(0)} />
          <Tab label="Reasons" {...a11yProps(1)} />
        </Tabs>
      </Box>
      <TabPanel value={selectedTab} index={0}>
        {/* Categories Tab Content */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          {/* <Typography variant="h5">Disqualification Categories</Typography> */}
          <Grid item xs={3}>
            <CustomNameField name="search_category" control={control} placeholder="search" />
          </Grid>
          <Box display="flex" alignItems="center" gap={1}>
            <Button variant="outlined" size="small">
              Activities
            </Button>
            <Button variant="contained" size="small" color="primary" onClick={handleAddCategoryDialogOpen}>
              Add
            </Button>
          </Box>
        </Box>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box sx={{ height: 300, width: '100%' }}>
              <CustomDataGrid
                rows={categoryDetails}
                columns={categoryColumns}
                paginationModel={paginationModel}
                onPaginationModelChange={setPaginationModel}
                rowCount={categoryDetails.length}
              />
            </Box>
          </Grid>
        </Grid>
        <AddCategoryDialog open={openAddCategoryDialog} onClose={handleAddCategoryDialogClose} onSave={handleCategorySave} />
        <EditCategoryDialog
          open={openEditCategoryDialog}
          onClose={() => setOpenEditCategoryDialog(false)}
          onSave={handleCategoryUpdate}
          category={selectedCategory}
        />
      </TabPanel>
      <TabPanel value={selectedTab} index={1}>
        {/* Reasons Tab Content */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          {/* <Typography variant="h5">Disqualification Reasons</Typography> */}
          <Grid item xs={3}>
            <CustomNameField name="search_reason" control={control} placeholder="search" />
          </Grid>
          <Box display="flex" alignItems="center" gap={1}>
            <Button variant="outlined" size="small">
              Activities
            </Button>
            <Button variant="contained" size="small" color="primary" onClick={handleAddReasonDialogOpen}>
              Add
            </Button>
          </Box>
        </Box>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box sx={{ height: 300, width: '100%' }}>
              <CustomDataGrid
                rows={reasonDetails}
                columns={reasonColumns}
                paginationModel={paginationModel}
                onPaginationModelChange={setPaginationModel}
                rowCount={reasonDetails.length}
              />
            </Box>
          </Grid>
        </Grid>
        <AddReasonDialog
          open={openAddReasonDialog}
          onClose={handleAddReasonDialogClose}
          onSave={handleReasonSave}
          categories={categoryDetails}
        />
        <EditReasonDialog
          open={openEditReasonDialog}
          onClose={() => setOpenEditReasonDialog(false)}
          onSave={handleReasonUpdate}
          reason={selectedReason}
          categories={categoryDetails}
        />
      </TabPanel>
    </MainCard>
  );
}
export default DisqualificationReason;
