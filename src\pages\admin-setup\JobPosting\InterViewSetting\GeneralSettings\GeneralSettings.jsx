import React from 'react';
import { <PERSON>po<PERSON>, Box, Grid, Button, Checkbox, FormControlLabel, Switch, InputLabel, Radio, RadioGroup } from '@mui/material';
import MainCard from 'components/MainCard';
import { useForm, Controller } from 'react-hook-form';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomNameField from 'components/custom-components/CustomNameField';

function GeneralSettings() {
  const { control } = useForm();

  return (
    <MainCard
      title="General Interview Settings"
      secondary={
        <Button variant="outlined" size="small">
          Activities
        </Button>
      }
      sx={{
        borderRadius: '1%',
        backgroundColor: 'white',
        '& .MuiInputLabel-root': { fontSize: '0.875rem' },
        mt: 0.2
      }}
    >
      <Grid container spacing={4} alignItems="center">
        {/* 1. Interview Scheduling */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>Interview Scheduling</InputLabel>
              <Typography variant="body2" color="textSecondary">
                (Hide the interview scheduling button for the &quot;Rejected&quot; category submissions.)
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
            <FormControlLabel
              control={
                <Controller
                  name="interview_scheduling"
                  control={control}
                  defaultValue={false}
                  render={({ field }) => <Checkbox {...field} />}
                />
              }
              label=""
            />
          </Grid>
        </Grid>

        {/* 2. Send Interview Schedule Notifications to the Client Contact? */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                Send Interview Schedule Notifications to the Client Contact?
              </InputLabel>
              <Typography variant="body2" color="textSecondary">
                (Send interview schedule notifications to the client contact.)
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
            <FormControlLabel
              control={
                <Controller
                  name="send_notifications_to_client"
                  control={control}
                  defaultValue={false}
                  render={({ field }) => <Switch {...field} />}
                />
              }
              label=""
            />
          </Grid>
        </Grid>

        {/* 3. Cancel the interview if the interviewer reschedules the interview */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                Cancel the interview if the interviewer reschedules the interview
              </InputLabel>
              <Typography variant="body2" color="textSecondary">
                (Cancel the interview if the interviewer reschedules the interview.)
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
            <FormControlLabel
              control={
                <Controller
                  name="cancel_on_reschedule"
                  control={control}
                  defaultValue={false}
                  render={({ field }) => <Switch {...field} />}
                />
              }
              label=""
            />
          </Grid>
        </Grid>

        {/* 4. End interview feedback reminder */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                End interview feedback reminder
              </InputLabel>
              <Typography variant="body2" color="textSecondary">
                (Select the notifier for end interview feedback reminder.)
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6}>
            <CustomDropdownField
              name="end_interview_feedback_reminder"
              control={control}
              placeholder="Select Notifier"
              options={[
                { value: 'Primary Recruiter' },
                { value: 'Interview Scheduled By' },
                { value: 'Submitted By' },
                { value: 'Interview Additional Notifiers' },
                { value: 'Bench Account Manager' },
                { value: 'Sales Team Member' },
                { value: 'Interviewer' },
              ]}
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Grid>
        </Grid>

        {/* 5. Feedback on the Schedule Interview */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                Feedback on the Schedule Interview
              </InputLabel>
              <Typography variant="body2" color="textSecondary">
                (If enabled, a user needs to submit the feedback before scheduling another round of interviews.)
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
            <FormControlLabel
              control={
                <Controller
                  name="feedback_on_schedule_interview"
                  control={control}
                  defaultValue={false}
                  render={({ field }) => <Switch {...field} />}
                />
              }
              label=""
            />
          </Grid>
        </Grid>

        {/* 6. Schedule Interview Option Visibility */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                Schedule Interview Option Visibility
              </InputLabel>
              <Typography variant="body2" color="textSecondary">
                (Configure when the &apos;Schedule Interview&apos; option should be displayed. Selecting &quot;No Restriction&quot; always displays it; otherwise, it depends on status.)
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
            <Controller
              name="schedule_interview_option_visibility"
              control={control}
              defaultValue="no_restriction"
              render={({ field }) => (
                <RadioGroup {...field} row>
                  <FormControlLabel value="no_restriction" control={<Radio />} label="No Restriction" />
                  <FormControlLabel value="status_with_option" control={<Radio />} label="Status with schedule interview option" />
                </RadioGroup>
              )}
            />
          </Grid>
        </Grid>

        {/* 7. Default Reminder Options */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                Default Reminder Options
              </InputLabel>
              <Typography variant="body2" color="textSecondary">
                (Choose the default options for scheduled interview reminders.)
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6}>
            <Grid container spacing={1} alignItems="center">
              <Grid item xs={12} sm={6}>
                <CustomDropdownField
                  name="default_reminder_communication_method"
                  control={control}
                  placeholder="Email"
                  options={[{ value: 'Email' }]}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <CustomDropdownField
                  name="default_reminder_recipient_type"
                  control={control}
                  placeholder="Primary Recruiter"
                  options={[{ value: 'Primary Recruiter' }]}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <CustomDropdownField
                  name="default_reminder_time_unit"
                  control={control}
                  placeholder="Minutes"
                  options={[{ value: 'minutes' }, {value:"Days"}]}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <CustomNameField
                  name="default_reminder_time_value"
                  control={control}
                  placeholder="180"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2">
                  (Minutes before)
                </Typography>
              </Grid>
            </Grid>
          </Grid>
        </Grid>

        {/* 8. Interviewer Feedback Notification */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                Interviewer Feedback Notification
              </InputLabel>
              <Typography variant="body2" color="textSecondary">
                (Select the roles that should receive interviewer feedback notifications.)
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6}>
            <CustomDropdownField
              name="interviewer_feedback_notification"
              control={control}
              placeholder="Select Roles"
              options={[
                { value: 'Job Posting Recruitment Manager' },
                { value: 'Job Posting Primary Recruiter' },
                { value: 'Interview Scheduled By' },
                { value: 'Profile Submitted By' },
              ]}
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Grid>
        </Grid>
      </Grid>
    </MainCard>
  );
}

export default GeneralSettings;
