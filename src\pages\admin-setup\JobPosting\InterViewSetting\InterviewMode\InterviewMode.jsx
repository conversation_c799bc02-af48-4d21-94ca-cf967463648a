import React, { useState } from 'react';
import { <PERSON>po<PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Custom<PERSON>ameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddInterviewModeDialog from './AddInterviewModeDialog';
import EditInterviewModeDialog from './EditInterviewModeDialog';

function InterviewMode() {
  const [interviewModeDetails, setInterviewModeDetails] = useState([
    {
      id: 1,
      interview_mode: 'Online',
      created_by: 'Admin',
      created_on: '2023-01-01',
      modified_by: 'Admin',
      modified_on: '2023-01-01',
      status: 'Active'
    },
    {
      id: 2,
      interview_mode: 'Offline',
      created_by: 'Admin',
      created_on: '2023-01-05',
      modified_by: 'Admin',
      modified_on: '2023-01-05',
      status: 'Active'
    }
  ]);

  const { control } = useForm();

  const [openAddInterviewModeDialog, setOpenAddInterviewModeDialog] = useState(false);
  const [openEditInterviewModeDialog, setOpenEditInterviewModeDialog] = useState(false);
  const [selectedInterviewMode, setSelectedInterviewMode] = useState(null);

  const handleAddInterviewModeDialogOpen = () => {
    setOpenAddInterviewModeDialog(true);
  };

  const handleAddInterviewModeDialogClose = () => {
    setOpenAddInterviewModeDialog(false);
  };

  const handleAddInterviewModeSave = (data) => {
    setInterviewModeDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'User',
        modified_on: new Date().toISOString().slice(0, 10),
        status: 'Active'
      }
    ]);
    handleAddInterviewModeDialogClose();
  };

  const handleEditInterviewModeSave = (updatedMode) => {
    setInterviewModeDetails((prev) => prev.map((mode) => (mode.id === updatedMode.id ? updatedMode : mode)));
    setOpenEditInterviewModeDialog(false);
    setSelectedInterviewMode(null);
  };

  const InterviewModeActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedInterviewMode(params.row);
      setOpenEditInterviewModeDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete interview mode:', params.row);
      setInterviewModeDetails((prev) => prev.filter((mode) => mode.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const interviewModeColumns = [
    {
      field: 'interview_mode',
      headerName: 'INTERVIEW MODE',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_on',
      headerName: 'CREATED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'status',
      headerName: 'STATUS',
      flex: 1,
      minWidth: 100,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <InterviewModeActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <MainCard
      title="Interview Mode"
      sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Grid item xs={3}>
          <CustomNameField
            name="search_interview_mode"
            control={control}
            placeholder="Search"
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
          />
        </Grid>
        <Box display="flex" alignItems="center" gap={1}>
          <Button variant="outlined" size="small">
            Activities
          </Button>
          <Button variant="contained" size="small" color="primary" onClick={handleAddInterviewModeDialogOpen}>
            + Add
          </Button>
        </Box>
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={interviewModeDetails}
              columns={interviewModeColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={interviewModeDetails.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddInterviewModeDialog
        open={openAddInterviewModeDialog}
        onClose={handleAddInterviewModeDialogClose}
        onSave={handleAddInterviewModeSave}
      />
      <EditInterviewModeDialog
        open={openEditInterviewModeDialog}
        onClose={() => setOpenEditInterviewModeDialog(false)}
        onSave={handleEditInterviewModeSave}
        interviewMode={selectedInterviewMode}
      />
    </MainCard>
  );
}

export default InterviewMode;
