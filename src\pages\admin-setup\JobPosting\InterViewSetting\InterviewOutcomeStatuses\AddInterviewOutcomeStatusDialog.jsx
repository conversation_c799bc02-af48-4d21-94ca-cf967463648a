import React, { useEffect } from 'react';
import { Dialog, DialogTitle, DialogContent, DialogA<PERSON>, Button, Grid, Stack, InputLabel, Typography } from '@mui/material';
import { useForm, useFieldArray } from 'react-hook-form';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import DeleteIcon from '@mui/icons-material/Delete';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';

function AddInterviewOutcomeStatusDialog({ open, onClose, onSave }) {
  const { control, handleSubmit, reset } = useForm({
    defaultValues: {
      interviewOutcomePairs: [{ select_interview_round: '', select_submission_status: '' }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'interviewOutcomePairs',
  });

  const handleSave = (data) => {
    onSave(data);
    reset();
    onClose();
  };

  useEffect(() => {
    if (!open) {
      reset({
        interviewOutcomePairs: [{ select_interview_round: '', select_submission_status: '' }],
      });
    }
  }, [open, reset]);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        Add Interview Outcome Status
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        <Grid container spacing={3} direction="column">
          <Grid item xs={12}>
            <Stack spacing={1}>
              <InputLabel sx={{ color: "black" }} htmlFor="interview_outcome_status">Interview Outcome Status</InputLabel>
              <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                (This status will be selected automatically, once the interview feedback is given.)
              </Typography>
              <CustomNameField name="interview_outcome_status" control={control} placeholder="" sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} />
            </Stack>
          </Grid>
          {fields.map((field, index) => (
            <Grid item xs={12} key={field.id} container spacing={2} alignItems="center">
              <Grid item xs={6}>
                <Stack spacing={1}>
                  <InputLabel sx={{ color: "black" }} htmlFor={`select_interview_round_${index}`}>Select Interview Round</InputLabel>
                  <CustomDropdownField
                    name={`interviewOutcomePairs.${index}.select_interview_round`}
                    control={control}
                    placeholder="Select"
                    options={[
                      { value: 'Client Interview' },
                      { value: 'End Client Interview' },
                      { value: 'Internal Interview' },
                      { value: 'L1 Interview' },
                      { value: 'L2 Interview' },
                      { value: 'Prime Vendor Interview' },
                      { value: 'Vendor Interview' },
                    ]}
                    sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  />
                </Stack>
              </Grid>
              <Grid item xs={5}>
                <Stack spacing={1}>
                  <InputLabel sx={{ color: "black" }} htmlFor={`select_submission_status_${index}`}>Select Submission Status</InputLabel>
                  <CustomDropdownField
                    name={`interviewOutcomePairs.${index}.select_submission_status`}
                    control={control}
                    placeholder="Select"
                    options={[
                      { value: 'Archived' },
                      { value: 'BGV Failed' },
                      { value: 'Client Interview' },
                      { value: 'Confirmed' },
                      { value: 'Disqualified By Client' },
                      { value: 'Disqualified by End Client' },
                      { value: 'Disqualified By Vendor' },
                      { value: 'Drop' },
                    ]}
                    sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  />
                </Stack>
              </Grid>
              <Grid item xs={1} sx={{ mt: 3.5, display: 'flex', alignItems: 'center' }}>
                {fields.length > 1 && (
                  <IconButton onClick={() => remove(index)} color="error">
                    <DeleteIcon />
                  </IconButton>
                )}
                  <IconButton onClick={() => append({ select_interview_round: '', select_submission_status: '' })} color="primary">
                    <AddCircleOutlineIcon />
                  </IconButton>
              </Grid>
            </Grid>
          ))}
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} size="small" variant="outlined">
          Cancel
        </Button>
        <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(handleSave)}>
          Add
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default AddInterviewOutcomeStatusDialog; 