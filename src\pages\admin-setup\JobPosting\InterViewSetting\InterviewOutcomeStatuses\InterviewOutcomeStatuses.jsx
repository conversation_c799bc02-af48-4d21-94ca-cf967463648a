import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Custom<PERSON>ameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddInterviewOutcomeStatusDialog from './AddInterviewOutcomeStatusDialog';
import EditInterviewOutcomeStatusDialog from './EditInterviewOutcomeStatusDialog';

function InterviewOutcomeStatuses() {
  const [interviewOutcomeStatusesDetails, setInterviewOutcomeStatusesDetails] = useState([
    {
      id: 1,
      status_name: 'Positive',
      status_type: 'Good Fit',
      created_by: 'Admin',
      created_on: '2023-01-01',
      modified_by: 'Admin',
      modified_on: '2023-01-01'
    },
    {
      id: 2,
      status_name: 'Negative',
      status_type: 'Not a Fit',
      created_by: 'Admin',
      created_on: '2023-01-05',
      modified_by: 'Admin',
      modified_on: '2023-01-05'
    }
  ]);

  const { control } = useForm();

  const [openAddInterviewOutcomeStatusDialog, setOpenAddInterviewOutcomeStatusDialog] = useState(false);
  const [openEditInterviewOutcomeStatusDialog, setOpenEditInterviewOutcomeStatusDialog] = useState(false);
  const [selectedInterviewOutcomeStatus, setSelectedInterviewOutcomeStatus] = useState(null);

  const handleAddInterviewOutcomeStatusDialogOpen = () => {
    setOpenAddInterviewOutcomeStatusDialog(true);
  };

  const handleAddInterviewOutcomeStatusDialogClose = () => {
    setOpenAddInterviewOutcomeStatusDialog(false);
  };

  const handleAddInterviewOutcomeStatusSave = (data) => {
    setInterviewOutcomeStatusesDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'User',
        modified_on: new Date().toISOString().slice(0, 10)
      }
    ]);
    handleAddInterviewOutcomeStatusDialogClose();
  };

  const handleEditInterviewOutcomeStatusSave = (updatedStatus) => {
    setInterviewOutcomeStatusesDetails((prev) => prev.map((status) => (status.id === updatedStatus.id ? updatedStatus : status)));
    setOpenEditInterviewOutcomeStatusDialog(false);
    setSelectedInterviewOutcomeStatus(null);
  };

  const InterviewOutcomeStatusActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedInterviewOutcomeStatus(params.row);
      setOpenEditInterviewOutcomeStatusDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete interview outcome status:', params.row);
      setInterviewOutcomeStatusesDetails((prev) => prev.filter((status) => status.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const interviewOutcomeStatusesColumns = [
    {
      field: 'status_name',
      headerName: 'STATUS NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    // {
    //   field: 'status_type',
    //   headerName: 'STATUS TYPE',
    //   flex: 1,
    //   minWidth: 150,
    //   renderCell: (params) => (
    //     <Tooltip title={params.value}>
    //       <Typography variant="body2">{params.value}</Typography>
    //     </Tooltip>
    //   )
    // },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_on',
      headerName: 'CREATED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <InterviewOutcomeStatusActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <MainCard
      title="Interview Outcome Statuses"
      sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Grid item xs={3}>
          <CustomNameField name="search_outcome_status" control={control} placeholder="Search" />
        </Grid>
        <Box display="flex" alignItems="center" gap={1}>
          <Button variant="outlined" size="small">
            Activities
          </Button>
          <Button variant="contained" size="small" color="primary" onClick={handleAddInterviewOutcomeStatusDialogOpen}>
            + Add
          </Button>
        </Box>
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={interviewOutcomeStatusesDetails}
              columns={interviewOutcomeStatusesColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={interviewOutcomeStatusesDetails.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddInterviewOutcomeStatusDialog
        open={openAddInterviewOutcomeStatusDialog}
        onClose={handleAddInterviewOutcomeStatusDialogClose}
        onSave={handleAddInterviewOutcomeStatusSave}
      />
      <EditInterviewOutcomeStatusDialog
        open={openEditInterviewOutcomeStatusDialog}
        onClose={() => setOpenEditInterviewOutcomeStatusDialog(false)}
        onSave={handleEditInterviewOutcomeStatusSave}
        interviewOutcomeStatus={selectedInterviewOutcomeStatus}
      />
    </MainCard>
  );
}

export default InterviewOutcomeStatuses;
