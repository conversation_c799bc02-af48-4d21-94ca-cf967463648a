import React, { useEffect } from 'react';
import { Drawer, Typography, Box, Divider, IconButton, Button, Grid, Stack, InputLabel } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm } from 'react-hook-form';
import Custom<PERSON>ameField from 'components/custom-components/CustomNameField';

function EditInterviewRecommendationDialog({ open, onClose, onSave, initialData }) {
  const { control, handleSubmit, reset } = useForm();

  useEffect(() => {
    if (open && initialData) {
      reset({
        recommendation_name: initialData.name,
      });
    } else if (!open) {
      reset();
    }
  }, [open, initialData, reset]);

  const handleSave = (data) => {
    onSave(data);
    onClose();
  };

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box sx={{ width: { xs: '100%', sm: 400 }, height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Sticky Header */}
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">Edit Interview Recommendation</Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <Divider />

        {/* Form */}
        <Box component="form" onSubmit={handleSubmit(handleSave)} sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel sx={{ color: 'black' }} htmlFor="recommendation_name">
                  Recommendation Name
                </InputLabel>
                <CustomNameField
                  name="recommendation_name"
                  control={control}
                  placeholder=""
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>
          </Grid>
        </Box>

        {/* Footer */}
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button onClick={onClose} size="small" variant="outlined">
            Cancel
          </Button>
          <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(handleSave)}>
            Update
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
}

export default EditInterviewRecommendationDialog;
