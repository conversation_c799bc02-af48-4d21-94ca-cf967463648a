import React, { useState } from 'react';
import { Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem, useTheme, Typography } from '@mui/material';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MainCard from 'components/MainCard';
import AddInterviewRecommendationDialog from './AddInterviewRecommendationDialog';
import EditInterviewRecommendationDialog from './EditInterviewRecommendationDialog';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';

function InterviewRecommendation() {
  const { control } = useForm();
  const theme = useTheme();
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [selectedRecommendation, setSelectedRecommendation] = useState(null);
  const [recommendations, setRecommendations] = useState([
    { id: 1, name: 'Positive', created_by: 'Admin', modified_by: 'Admin', modified_on: '2023-01-01' },
    { id: 2, name: 'Neutral', created_by: 'User1', modified_by: 'User1', modified_on: '2023-02-15' },
    { id: 3, name: 'Negative', created_by: 'Admin', modified_by: 'User2', modified_on: '2023-03-20' }
  ]);

  const handleAddClick = () => {
    setOpenAddDialog(true);
  };

  const handleEditClick = (recommendation) => {
    setSelectedRecommendation(recommendation);
    setOpenEditDialog(true);
  };

  const handleAddDialogClose = () => {
    setOpenAddDialog(false);
  };

  const handleEditDialogClose = () => {
    setOpenEditDialog(false);
    setSelectedRecommendation(null);
  };

  const handleSaveAdd = (data) => {
    setRecommendations((prev) => [
      ...prev,
      {
        id: prev.length > 0 ? Math.max(...prev.map((r) => r.id)) + 1 : 1,
        name: data.recommendation_name,
        created_by: 'User',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'User',
        modified_on: new Date().toISOString().slice(0, 10)
      }
    ]);
    handleAddDialogClose();
  };

  const handleSaveEdit = (updatedData) => {
    setRecommendations((prev) =>
      prev.map((rec) => (rec.id === selectedRecommendation.id ? { ...rec, name: updatedData.recommendation_name } : rec))
    );
    handleEditDialogClose();
  };

  const InterviewRecommendationActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      handleEditClick(params.row);
      handleClose();
    };

    const handleDelete = () => {
      setRecommendations((prev) => prev.filter((rec) => rec.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const columns = [
    {
      field: 'name',
      headerName: 'NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      renderCell: (params) => <InterviewRecommendationActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <Box>
      <MainCard
        title="Interview Recommendation"
        sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
      >
        <Grid container spacing={2} alignItems="center" justifyContent="space-between" mb={2}>
          <Grid item xs={12} sm={6} md={4}>
            <CustomNameField name="search" control={control} placeholder="Search" />
          </Grid>
          <Grid item xs={12} sm={6} md={5} display="flex" justifyContent="flex-end" gap={1}>
            <Button variant="outlined" size="small">
              Activities
            </Button>
            <Button size="small" variant="contained" color="primary" onClick={handleAddClick}>
              + Add
            </Button>
          </Grid>
        </Grid>
        <CustomDataGrid
          rows={recommendations}
          columns={columns}
          paginationModel={paginationModel}
          onPaginationModelChange={setPaginationModel}
          rowCount={recommendations.length}
        />
      </MainCard>

      <AddInterviewRecommendationDialog open={openAddDialog} onClose={handleAddDialogClose} onSave={handleSaveAdd} />
      <EditInterviewRecommendationDialog
        open={openEditDialog}
        onClose={handleEditDialogClose}
        onSave={handleSaveEdit}
        initialData={selectedRecommendation}
      />
    </Box>
  );
}

export default InterviewRecommendation;
