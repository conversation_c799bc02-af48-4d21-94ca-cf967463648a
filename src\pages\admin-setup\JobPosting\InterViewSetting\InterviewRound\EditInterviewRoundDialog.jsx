import React, { useEffect } from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Grid, Stack, InputLabel, Typography } from '@mui/material';
import { useForm } from 'react-hook-form';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';

function EditInterviewRoundDialog({ open, onClose, onSave, interviewRound }) {
  const { control, handleSubmit, reset, setValue } = useForm();

  useEffect(() => {
    if (open && interviewRound) {
      setValue('interview_round', interviewRound.interview_round);
      setValue('interview_category', interviewRound.interview_category);
      setValue('submission_status', interviewRound.submission_status);
    }
  }, [open, interviewRound, setValue]);

  const handleSave = (data) => {
    onSave({ ...interviewRound, ...data });
    reset();
    onClose();
  };

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        Edit Interview Round
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        <Grid container spacing={3} direction="column">
          <Grid item xs={4}>
            <Stack spacing={1}>
              <InputLabel sx={{color:"black"}}htmlFor="interview_round_name">Interview Round Name</InputLabel>
              <CustomNameField name="interview_round" control={control} placeholder="Enter Interview Round Name" sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} />
            </Stack>
          </Grid>
          <Grid item xs={4}>
            <Stack spacing={1}>
              <InputLabel sx={{color:"black"}}htmlFor="interview_category">Select Interview Category</InputLabel>
              <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                (Select the category of the interview. The system will consider the interview as external or internal interview
                in the report based on category selected here.)
              </Typography>
              <CustomDropdownField
                name="interview_category"
                control={control}
                placeholder="Select Category"
                options={[
                  { value: 'Client Interview' },
                  { value: 'End Client Interview' },
                  { value: 'Internal Interview' },
                  { value: 'Interview Scheduled' },
                  { value: 'Vendor Interview' },
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={4}>
            <Stack spacing={1}>
              <InputLabel sx={{color:"black"}}htmlFor="submission_status">Select Submission Status</InputLabel>
              <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                (Select the Submission Status to be updated when an Interview is scheduled with the Round selected here.)
              </Typography>
              <CustomDropdownField
                name="submission_status"
                control={control}
                placeholder="Select Status"
                options={[
                  { value: 'Pending' },
                  { value: 'Completed' },
                  { value: 'In Progress' },
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} size="small" variant="outlined">
          Cancel
        </Button>
        <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(handleSave)}>
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default EditInterviewRoundDialog; 