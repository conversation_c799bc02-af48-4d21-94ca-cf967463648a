import React, { useState } from 'react';
import { Typo<PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddInterviewRoundDialog from './AddInterviewRoundDialog';
import EditInterviewRoundDialog from './EditInterviewRoundDialog';

function InterviewRound() {
  const [interviewRoundDetails, setInterviewRoundDetails] = useState([
    { id: 1, interview_round: 'Round 1', interview_category: 'Technical', submission_status: 'Pending', created_by: 'Admin', created_on: '2023-01-01', modified_by: 'Admin', modified_on: '2023-01-01', status: 'Active' },
    { id: 2, interview_round: 'Round 2', interview_category: 'HR', submission_status: 'Completed', created_by: 'Admin', created_on: '2023-01-05', modified_by: 'Admin', modified_on: '2023-01-05', status: 'Active' },
  ]);

  const { control } = useForm();

  const [openAddInterviewRoundDialog, setOpenAddInterviewRoundDialog] = useState(false);
  const [openEditInterviewRoundDialog, setOpenEditInterviewRoundDialog] = useState(false);
  const [selectedInterviewRound, setSelectedInterviewRound] = useState(null);

  const handleAddInterviewRoundDialogOpen = () => {
    setOpenAddInterviewRoundDialog(true);
  };

  const handleAddInterviewRoundDialogClose = () => {
    setOpenAddInterviewRoundDialog(false);
  };

  const handleAddInterviewRoundSave = (data) => {
    setInterviewRoundDetails((prev) => [
      ...prev,
      { id: prev.length + 1, ...data, created_by: 'User', created_on: new Date().toISOString().slice(0, 10), modified_by: 'User', modified_on: new Date().toISOString().slice(0, 10), status: 'Active' }
    ]);
    handleAddInterviewRoundDialogClose();
  };

  const handleEditInterviewRoundSave = (updatedRound) => {
    setInterviewRoundDetails((prev) => prev.map((round) => (round.id === updatedRound.id ? updatedRound : round)));
    setOpenEditInterviewRoundDialog(false);
    setSelectedInterviewRound(null);
  };

  const InterviewRoundActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedInterviewRound(params.row);
      setOpenEditInterviewRoundDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete interview round:', params.row);
      setInterviewRoundDetails((prev) => prev.filter((round) => round.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const interviewRoundColumns = [
    {
      field: 'interview_round',
      headerName: 'INTERVIEW ROUND',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    // {
    //   field: 'interview_category',
    //   headerName: 'INTERVIEW CATEGORY',
    //   flex: 1,
    //   minWidth: 150,
    //   renderCell: (params) => (
    //     <Tooltip title={params.value}>
    //       <Typography variant="body2">{params.value}</Typography>
    //     </Tooltip>
    //   )
    // },
    // {
    //   field: 'submission_status',
    //   headerName: 'SUBMISSION STATUS',
    //   flex: 1,
    //   minWidth: 150,
    //   renderCell: (params) => (
    //     <Tooltip title={params.value}>
    //       <Typography variant="body2">{params.value}</Typography>
    //     </Tooltip>
    //   )
    // },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_on',
      headerName: 'CREATED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'status',
      headerName: 'STATUS',
      flex: 1,
      minWidth: 100,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <InterviewRoundActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <MainCard
      title="Interview Round"
      sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Grid item xs={3}>
          <CustomNameField name="search_interview_round" control={control} placeholder="Search" />
        </Grid>
        <Box display="flex" alignItems="center" gap={1}>
          <Button variant="outlined" size="small">
            Activities
          </Button>
          <Button variant="contained" size="small" color="primary" onClick={handleAddInterviewRoundDialogOpen}>
            + Add
          </Button>
        </Box>
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={interviewRoundDetails}
              columns={interviewRoundColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={interviewRoundDetails.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddInterviewRoundDialog open={openAddInterviewRoundDialog} onClose={handleAddInterviewRoundDialogClose} onSave={handleAddInterviewRoundSave} />
      <EditInterviewRoundDialog
        open={openEditInterviewRoundDialog}
        onClose={() => setOpenEditInterviewRoundDialog(false)}
        onSave={handleEditInterviewRoundSave}
        interviewRound={selectedInterviewRound}
      />
    </MainCard>
  );
}

export default InterviewRound; 