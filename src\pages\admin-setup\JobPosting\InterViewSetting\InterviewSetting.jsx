import React, { useState } from 'react';
import { Box, Typography, Tabs, Tab } from '@mui/material';
import MainCard from 'components/MainCard';
import GeneralSettings from './GeneralSettings/GeneralSettings';
import InterviewMode from './InterviewMode/InterviewMode';
import InterviewRound from './InterviewRound/InterviewRound';
import InterviewOutcomeStatuses from './InterviewOutcomeStatuses/InterviewOutcomeStatuses';
import InterviewRecommendation from './InterviewRecommendation/InterviewRecommendation';

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div role="tabpanel" hidden={value !== index} id={`simple-tabpanel-${index}`} aria-labelledby={`simple-tab-${index}`} {...other}>
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`
  };
}

function InterViewSetting() {
  const [selectedTab, setSelectedTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  return (
    <MainCard
      title="Interview Settings"
      sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 0.2 }}
    >
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={selectedTab} onChange={handleTabChange} aria-label="interview settings tabs">
          <Tab label="General Settings" {...a11yProps(0)} />
          <Tab label="Interview Mode" {...a11yProps(1)} />
          <Tab label="Interview Round" {...a11yProps(2)} />
          <Tab label="Interview Outcome Statuses" {...a11yProps(3)} />
          <Tab label="Interview Recommendation" {...a11yProps(4)} />
        </Tabs>
      </Box>
      <TabPanel value={selectedTab} index={0}>
        <GeneralSettings />
      </TabPanel>
      <TabPanel value={selectedTab} index={1}>
        <InterviewMode />
      </TabPanel>
      <TabPanel value={selectedTab} index={2}>
        <InterviewRound />
      </TabPanel>
      <TabPanel value={selectedTab} index={3}>
        <InterviewOutcomeStatuses />
      </TabPanel>
      <TabPanel value={selectedTab} index={4}>
        <InterviewRecommendation />
      </TabPanel>
    </MainCard>
  );
}

export default InterViewSetting;
