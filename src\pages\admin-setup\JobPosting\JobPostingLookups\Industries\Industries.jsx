import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddIndustryDialog from './AddIndustryDialog';
import EditIndustryDialog from './EditIndustryDialog';

function Industries() {
  const [industryDetails, setIndustryDetails] = useState([
    {
      id: 1,
      name: 'Technology',
      created_by: 'Admin',
      created_on: '2023-01-01',
      modified_by: 'Admin',
      modified_on: '2023-01-01',
      status: 'Active'
    },
    {
      id: 2,
      name: 'Healthcare',
      created_by: 'Admin',
      created_on: '2023-01-05',
      modified_by: 'Admin',
      modified_on: '2023-01-05',
      status: 'Active'
    }
  ]);

  const { control } = useForm();

  const [openAddIndustryDialog, setOpenAddIndustryDialog] = useState(false);
  const [openEditIndustryDialog, setOpenEditIndustryDialog] = useState(false);
  const [selectedIndustry, setSelectedIndustry] = useState(null);

  const handleAddIndustryDialogOpen = () => {
    setOpenAddIndustryDialog(true);
  };

  const handleAddIndustryDialogClose = () => {
    setOpenAddIndustryDialog(false);
  };

  const handleAddIndustrySave = (data) => {
    setIndustryDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'User',
        modified_on: new Date().toISOString().slice(0, 10),
        status: 'Active'
      }
    ]);
    handleAddIndustryDialogClose();
  };

  const handleEditIndustrySave = (updatedMode) => {
    setIndustryDetails((prev) => prev.map((industry) => (industry.id === updatedMode.id ? updatedMode : industry)));
    setOpenEditIndustryDialog(false);
    setSelectedIndustry(null);
  };

  const IndustryActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedIndustry(params.row);
      setOpenEditIndustryDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete industry:', params.row);
      setIndustryDetails((prev) => prev.filter((industry) => industry.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const industryColumns = [
    {
      field: 'name',
      headerName: 'NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_on',
      headerName: 'CREATED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <IndustryActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <MainCard
      title="Industries"
      sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Grid item xs={3}>
          <CustomNameField
            name="search_industry"
            control={control}
            placeholder="Search"
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
          />
        </Grid>
        <Box display="flex" alignItems="center" gap={1}>
          <Button variant="outlined" size="small">
            Activities
          </Button>
          <Button variant="contained" size="small" color="primary" onClick={handleAddIndustryDialogOpen}>
            + Add
          </Button>
        </Box>
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={industryDetails}
              columns={industryColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={industryDetails.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddIndustryDialog
        open={openAddIndustryDialog}
        onClose={handleAddIndustryDialogClose}
        onSave={handleAddIndustrySave}
      />
      <EditIndustryDialog
        open={openEditIndustryDialog}
        onClose={() => setOpenEditIndustryDialog(false)}
        onSave={handleEditIndustrySave}
        industry={selectedIndustry}
      />
    </MainCard>
  );
}

export default Industries; 