import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Custom<PERSON>ameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddJobCategoryDialog from './AddJobCategoryDialog';
import EditJobCategoryDialog from './EditJobCategoryDialog';

function JobCategories() {
  const [jobCategoryDetails, setJobCategoryDetails] = useState([
    {
      id: 1,
      category_name: 'Software Engineering',
      created_by: 'Admin',
      modified_by: 'Admin',
      last_modified_on: '2023-01-01',
      status: 'Active'
    },
    {
      id: 2,
      category_name: 'Human Resources',
      created_by: 'Admin',
      modified_by: 'Admin',
      last_modified_on: '2023-01-05',
      status: 'Active'
    }
  ]);

  const { control } = useForm();

  const [openAddJobCategoryDialog, setOpenAddJobCategoryDialog] = useState(false);
  const [openEditJobCategoryDialog, setOpenEditJobCategoryDialog] = useState(false);
  const [selectedJobCategory, setSelectedJobCategory] = useState(null);

  const handleAddJobCategoryDialogOpen = () => {
    setOpenAddJobCategoryDialog(true);
  };

  const handleAddJobCategoryDialogClose = () => {
    setOpenAddJobCategoryDialog(false);
  };

  const handleAddJobCategorySave = (data) => {
    setJobCategoryDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        modified_by: 'User',
        last_modified_on: new Date().toISOString().slice(0, 10),
        status: 'Active'
      }
    ]);
    handleAddJobCategoryDialogClose();
  };

  const handleEditJobCategorySave = (updatedMode) => {
    setJobCategoryDetails((prev) => prev.map((jobCategory) => (jobCategory.id === updatedMode.id ? updatedMode : jobCategory)));
    setOpenEditJobCategoryDialog(false);
    setSelectedJobCategory(null);
  };

  const JobCategoryActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedJobCategory(params.row);
      setOpenEditJobCategoryDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete job category:', params.row);
      setJobCategoryDetails((prev) => prev.filter((jobCategory) => jobCategory.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const jobCategoryColumns = [
    {
      field: 'category_name',
      headerName: 'CATEGORY NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'last_modified_on',
      headerName: 'LAST MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <JobCategoryActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <MainCard
      title="Job Categories"
      sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Grid item xs={3}>
          <CustomNameField
            name="search_job_category"
            control={control}
            placeholder="Search"
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
          />
        </Grid>
        <Box display="flex" alignItems="center" gap={1}>
          <Button variant="outlined" size="small">
            Activities
          </Button>
          <Button variant="contained" size="small" color="primary" onClick={handleAddJobCategoryDialogOpen}>
            + Add
          </Button>
        </Box>
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={jobCategoryDetails}
              columns={jobCategoryColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={jobCategoryDetails.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddJobCategoryDialog
        open={openAddJobCategoryDialog}
        onClose={handleAddJobCategoryDialogClose}
        onSave={handleAddJobCategorySave}
      />
      <EditJobCategoryDialog
        open={openEditJobCategoryDialog}
        onClose={() => setOpenEditJobCategoryDialog(false)}
        onSave={handleEditJobCategorySave}
        jobCategory={selectedJobCategory}
      />
    </MainCard>
  );
}

export default JobCategories; 