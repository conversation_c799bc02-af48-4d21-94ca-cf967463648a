import React, { useState } from 'react';
import { Box, Tabs, Tab, Typography } from '@mui/material';
import MainCard from 'components/MainCard';
import Domains from './Domain/Domains';
import Industries from './Industries/Industries';
import JobType from './JobType/JobType';
import NoteActions from './NoteActions/NoteActions';
import ProjectTypes from './ProjectTypes/ProjectTypes';
import JobCategories from './JobCategorie/JobCategories';

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div role="tabpanel" hidden={value !== index} id={`simple-tabpanel-${index}`} aria-labelledby={`simple-tab-${index}`} {...other}>
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`
  };
}

function JobPostingLookups() {
  const [selectedTab, setSelectedTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  return (
    <MainCard title="Job Posting Lookups">
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={selectedTab} onChange={handleTabChange} aria-label="job posting lookups tabs">
          <Tab label="Domains" {...a11yProps(0)} />
          <Tab label="Industries" {...a11yProps(1)} />
          <Tab label="Job Type" {...a11yProps(2)} />
          <Tab label="Note Actions" {...a11yProps(3)} />
          <Tab label="Project Types" {...a11yProps(4)} />
          <Tab label="Job Categories" {...a11yProps(5)} />
        </Tabs>
      </Box>

      <TabPanel value={selectedTab} index={0}>
        <Domains />
      </TabPanel>
      <TabPanel value={selectedTab} index={1}>
        <Industries />
      </TabPanel>
      <TabPanel value={selectedTab} index={2}>
        <JobType />
      </TabPanel>
      <TabPanel value={selectedTab} index={3}>
        <NoteActions />
      </TabPanel>
      <TabPanel value={selectedTab} index={4}>
        <ProjectTypes />
      </TabPanel>
      <TabPanel value={selectedTab} index={5}>
        <JobCategories />
      </TabPanel>
    </MainCard>
  );
}

export default JobPostingLookups;
