import React, { useEffect } from 'react';
import { <PERSON>alog, DialogTitle, DialogContent, DialogActions, Button, Grid, Stack, InputLabel } from '@mui/material';
import { useForm } from 'react-hook-form';
import CustomNameField from 'components/custom-components/CustomNameField';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';

function AddJobPostingLookupDialog({ open, onClose, onSave }) {
  const { control, handleSubmit, reset } = useForm();

  const handleSave = (data) => {
    onSave(data);
    reset();
    onClose();
  };

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        Add Lookup
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        <Grid container spacing={3} direction="column">
          <Grid item xs={12}>
            <Stack spacing={1}>
              <InputLabel sx={{ color: "black" }} htmlFor="lookup_name">Name</InputLabel>
              <CustomNameField name="lookup_name" control={control} placeholder="" sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} />
            </Stack>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} size="small" variant="outlined">
          Cancel
        </Button>
        <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(handleSave)}>
          Add
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default AddJobPostingLookupDialog; 