import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Custom<PERSON>ameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddJobTypeDialog from './AddJobTypeDialog';
import EditJobTypeDialog from './EditJobTypeDialog';

function JobType() {
  const [jobTypeDetails, setJobTypeDetails] = useState([
    {
      id: 1,
      job_type_name: 'Full-time',
      created_by: 'Admin',
      created_on: '2023-01-01',
      modified_by: 'Admin',
      modified_on: '2023-01-01',
      status: 'Active'
    },
    {
      id: 2,
      job_type_name: 'Part-time',
      created_by: 'Admin',
      created_on: '2023-01-05',
      modified_by: 'Admin',
      modified_on: '2023-01-05',
      status: 'Active'
    }
  ]);

  const { control } = useForm();

  const [openAddJobTypeDialog, setOpenAddJobTypeDialog] = useState(false);
  const [openEditJobTypeDialog, setOpenEditJobTypeDialog] = useState(false);
  const [selectedJobType, setSelectedJobType] = useState(null);

  const handleAddJobTypeDialogOpen = () => {
    setOpenAddJobTypeDialog(true);
  };

  const handleAddJobTypeDialogClose = () => {
    setOpenAddJobTypeDialog(false);
  };

  const handleAddJobTypeSave = (data) => {
    setJobTypeDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'User',
        modified_on: new Date().toISOString().slice(0, 10),
        status: 'Active'
      }
    ]);
    handleAddJobTypeDialogClose();
  };

  const handleEditJobTypeSave = (updatedMode) => {
    setJobTypeDetails((prev) => prev.map((jobType) => (jobType.id === updatedMode.id ? updatedMode : jobType)));
    setOpenEditJobTypeDialog(false);
    setSelectedJobType(null);
  };

  const JobTypeActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedJobType(params.row);
      setOpenEditJobTypeDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete job type:', params.row);
      setJobTypeDetails((prev) => prev.filter((jobType) => jobType.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const jobTypeColumns = [
    {
      field: 'job_type_name',
      headerName: 'JOB TYPE NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_on',
      headerName: 'CREATED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <JobTypeActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <MainCard
      title="Job Type"
      sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Grid item xs={3}>
          <CustomNameField
            name="search_job_type"
            control={control}
            placeholder="Search"
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
          />
        </Grid>
        <Box display="flex" alignItems="center" gap={1}>
          <Button variant="outlined" size="small">
            Activities
          </Button>
          <Button variant="contained" size="small" color="primary" onClick={handleAddJobTypeDialogOpen}>
            + Add
          </Button>
        </Box>
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={jobTypeDetails}
              columns={jobTypeColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={jobTypeDetails.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddJobTypeDialog
        open={openAddJobTypeDialog}
        onClose={handleAddJobTypeDialogClose}
        onSave={handleAddJobTypeSave}
      />
      <EditJobTypeDialog
        open={openEditJobTypeDialog}
        onClose={() => setOpenEditJobTypeDialog(false)}
        onSave={handleEditJobTypeSave}
        jobType={selectedJobType}
      />
    </MainCard>
  );
}

export default JobType; 