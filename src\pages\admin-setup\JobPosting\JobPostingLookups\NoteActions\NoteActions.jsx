import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddNoteActionDialog from './AddNoteActionDialog';
import EditNoteActionDialog from './EditNoteActionDialog';

function NoteActions() {
  const [noteActionDetails, setNoteActionDetails] = useState([
    {
      id: 1,
      name: 'Follow Up',
      created_by: 'Admin',
      created_on: '2023-01-01',
      modified_by: 'Admin',
      modified_on: '2023-01-01',
      status: 'Active'
    },
    {
      id: 2,
      name: 'Send Email',
      created_by: 'Admin',
      created_on: '2023-01-05',
      modified_by: 'Admin',
      modified_on: '2023-01-05',
      status: 'Active'
    }
  ]);

  const { control } = useForm();

  const [openAddNoteActionDialog, setOpenAddNoteActionDialog] = useState(false);
  const [openEditNoteActionDialog, setOpenEditNoteActionDialog] = useState(false);
  const [selectedNoteAction, setSelectedNoteAction] = useState(null);

  const handleAddNoteActionDialogOpen = () => {
    setOpenAddNoteActionDialog(true);
  };

  const handleAddNoteActionDialogClose = () => {
    setOpenAddNoteActionDialog(false);
  };

  const handleAddNoteActionSave = (data) => {
    setNoteActionDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'User',
        modified_on: new Date().toISOString().slice(0, 10),
        status: 'Active'
      }
    ]);
    handleAddNoteActionDialogClose();
  };

  const handleEditNoteActionSave = (updatedMode) => {
    setNoteActionDetails((prev) => prev.map((noteAction) => (noteAction.id === updatedMode.id ? updatedMode : noteAction)));
    setOpenEditNoteActionDialog(false);
    setSelectedNoteAction(null);
  };

  const NoteActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedNoteAction(params.row);
      setOpenEditNoteActionDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete note action:', params.row);
      setNoteActionDetails((prev) => prev.filter((noteAction) => noteAction.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const noteActionColumns = [
    {
      field: 'name',
      headerName: 'NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_on',
      headerName: 'CREATED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <NoteActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <MainCard
      title="Note Actions"
      sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Grid item xs={3}>
          <CustomNameField
            name="search_note_action"
            control={control}
            placeholder="Search"
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
          />
        </Grid>
        <Box display="flex" alignItems="center" gap={1}>
          <Button variant="outlined" size="small">
            Activities
          </Button>
          <Button variant="contained" size="small" color="primary" onClick={handleAddNoteActionDialogOpen}>
            + Add
          </Button>
        </Box>
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={noteActionDetails}
              columns={noteActionColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={noteActionDetails.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddNoteActionDialog
        open={openAddNoteActionDialog}
        onClose={handleAddNoteActionDialogClose}
        onSave={handleAddNoteActionSave}
      />
      <EditNoteActionDialog
        open={openEditNoteActionDialog}
        onClose={() => setOpenEditNoteActionDialog(false)}
        onSave={handleEditNoteActionSave}
        noteAction={selectedNoteAction}
      />
    </MainCard>
  );
}

export default NoteActions; 