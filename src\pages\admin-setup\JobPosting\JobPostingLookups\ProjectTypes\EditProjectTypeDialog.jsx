import React, { useEffect } from 'react';
import { Drawer, Typo<PERSON>, Box, Divider, IconButton, Button, Grid, Stack, InputLabel } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm } from 'react-hook-form';
import CustomNameField from 'components/custom-components/CustomNameField';

function EditProjectTypeDialog({ open, onClose, onSave, projectType }) {
  const { control, handleSubmit, reset, setValue } = useForm();

  useEffect(() => {
    if (open && projectType) {
      setValue('project_type_name', projectType.project_type_name);
      setValue('project_type_code', projectType.project_type_code);
    }
  }, [open, projectType, setValue]);

  const handleSave = (data) => {
    onSave({ ...projectType, ...data });
    reset();
  };

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box sx={{ width: { xs: '100%', sm: 400 }, height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Sticky Header */}
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">Edit Project Type</Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <Divider />

        {/* Form */}
        <Box component="form" onSubmit={handleSubmit(handleSave)} sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel htmlFor="project_type_name">Project Type Name</InputLabel>
                <CustomNameField name="project_type_name" control={control} placeholder="Enter Project Type Name"
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)'}} />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel htmlFor="project_type_code">Project Type Code</InputLabel>
                <CustomNameField name="project_type_code" control={control} placeholder="Enter Project Type Code"
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)'}} />
              </Stack>
            </Grid>
          </Grid>
        </Box>

        {/* Footer */}
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            onClick={() => {
              reset();
              onClose();
            }}
            size="small"
            variant="outlined"
          >
            Cancel
          </Button>
          <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(handleSave)}>
            Save
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
}

export default EditProjectTypeDialog; 