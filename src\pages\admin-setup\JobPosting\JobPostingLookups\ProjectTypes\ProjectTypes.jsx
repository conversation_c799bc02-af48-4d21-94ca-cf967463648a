import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Custom<PERSON><PERSON>Field from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
import AddProjectTypeDialog from './AddProjectTypeDialog';
import EditProjectTypeDialog from './EditProjectTypeDialog';

function ProjectTypes() {
  const [projectTypeDetails, setProjectTypeDetails] = useState([
    {
      id: 1,
      project_type_name: 'New Development',
      project_type_code: 'ND',
      created_by: 'Admin',
      created_on: '2023-01-01',
      modified_by: 'Admin',
      modified_on: '2023-01-01',
      status: 'Active'
    },
    {
      id: 2,
      project_type_name: 'Maintenance',
      project_type_code: 'MN',
      created_by: 'Admin',
      created_on: '2023-01-05',
      modified_by: 'Admin',
      modified_on: '2023-01-05',
      status: 'Active'
    }
  ]);

  const { control } = useForm();

  const [openAddProjectTypeDialog, setOpenAddProjectTypeDialog] = useState(false);
  const [openEditProjectTypeDialog, setOpenEditProjectTypeDialog] = useState(false);
  const [selectedProjectType, setSelectedProjectType] = useState(null);

  const handleAddProjectTypeDialogOpen = () => {
    setOpenAddProjectTypeDialog(true);
  };

  const handleAddProjectTypeDialogClose = () => {
    setOpenAddProjectTypeDialog(false);
  };

  const handleAddProjectTypeSave = (data) => {
    setProjectTypeDetails((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        created_by: 'User',
        created_on: new Date().toISOString().slice(0, 10),
        modified_by: 'User',
        modified_on: new Date().toISOString().slice(0, 10),
        status: 'Active'
      }
    ]);
    handleAddProjectTypeDialogClose();
  };

  const handleEditProjectTypeSave = (updatedMode) => {
    setProjectTypeDetails((prev) => prev.map((projectType) => (projectType.id === updatedMode.id ? updatedMode : projectType)));
    setOpenEditProjectTypeDialog(false);
    setSelectedProjectType(null);
  };

  const ProjectTypeActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedProjectType(params.row);
      setOpenEditProjectTypeDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete project type:', params.row);
      setProjectTypeDetails((prev) => prev.filter((projectType) => projectType.id !== params.row.id));
      handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const projectTypeColumns = [
    {
      field: 'project_type_name',
      headerName: 'PROJECT TYPE NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'project_type_code',
      headerName: 'PROJECT TYPE CODE',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'created_on',
      headerName: 'CREATED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'modified_on',
      headerName: 'MODIFIED ON',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <ProjectTypeActionCell params={params} />
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <MainCard
      title="Project Types"
      sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Grid item xs={3}>
          <CustomNameField
            name="search_project_type"
            control={control}
            placeholder="Search"
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
          />
        </Grid>
        <Box display="flex" alignItems="center" gap={1}>
          <Button variant="outlined" size="small">
            Activities
          </Button>
          <Button variant="contained" size="small" color="primary" onClick={handleAddProjectTypeDialogOpen}>
            + Add
          </Button>
        </Box>
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={projectTypeDetails}
              columns={projectTypeColumns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={projectTypeDetails.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddProjectTypeDialog
        open={openAddProjectTypeDialog}
        onClose={handleAddProjectTypeDialogClose}
        onSave={handleAddProjectTypeSave}
      />
      <EditProjectTypeDialog
        open={openEditProjectTypeDialog}
        onClose={() => setOpenEditProjectTypeDialog(false)}
        onSave={handleEditProjectTypeSave}
        projectType={selectedProjectType}
      />
    </MainCard>
  );
}

export default ProjectTypes; 