import React from 'react';
import {
  Typo<PERSON>,
  Box,
  Grid,
  Button,
  Checkbox,
  FormControlLabel,
  Switch,
  InputLabel,
  Radio,
  RadioGroup,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import MainCard from 'components/MainCard';
import { useForm, Controller } from 'react-hook-form';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomNameField from 'components/custom-components/CustomNameField';

function JobPostingSettings() {
  const { control, watch } = useForm();
  const jobPostingCodeGenerationType = watch('job_posting_code_generation_type', 'automatic');
  const vendorJobPostingCodeGenerationType = watch('vendor_job_posting_code_generation_type', 'manual');

  return (
    <MainCard
      title="Job Posting Settings"
      secondary={
        <Button variant="outlined" size="small">
          Activities
        </Button>
      }
      sx={{
        borderRadius: '1%',
        backgroundColor: 'white',
        '& .MuiInputLabel-root': { fontSize: '0.875rem' },
        mt: 0.2
      }}
    >
      <Grid container spacing={4} alignItems="center">
        {/* 1. Set Turnaround Time Based on Business Working Hours */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} lg={8} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <FormControlLabel
                control={
                  <Controller
                    name="set_turnaround_time"
                    control={control}
                    defaultValue={false}
                    render={({ field }) => <Checkbox {...field} />}
                  />
                }
                label="Set Turnaround Time Based on Business Working Hours"
              />
            </Box>
          </Grid>
        </Grid>

        {/* 2. Business Working Days */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>Business Working Days</InputLabel>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
            <Box display="flex" flexWrap="wrap" gap={2} justifyContent="flex-end">
              <FormControlLabel
                control={
                  <Controller name="monday" control={control} defaultValue={false} render={({ field }) => <Checkbox {...field} />} />
                }
                label="Mon"
              />
              <FormControlLabel
                control={
                  <Controller name="tuesday" control={control} defaultValue={false} render={({ field }) => <Checkbox {...field} />} />
                }
                label="Tue"
              />
              <FormControlLabel
                control={
                  <Controller name="wednesday" control={control} defaultValue={false} render={({ field }) => <Checkbox {...field} />} />
                }
                label="Wed"
              />
              <FormControlLabel
                control={
                  <Controller name="thursday" control={control} defaultValue={false} render={({ field }) => <Checkbox {...field} />} />
                }
                label="Thu"
              />
              <FormControlLabel
                control={
                  <Controller name="friday" control={control} defaultValue={false} render={({ field }) => <Checkbox {...field} />} />
                }
                label="Fri"
              />
              <FormControlLabel
                control={
                  <Controller name="saturday" control={control} defaultValue={false} render={({ field }) => <Checkbox {...field} />} />
                }
                label="Sat"
              />
              <FormControlLabel
                control={
                  <Controller name="sunday" control={control} defaultValue={false} render={({ field }) => <Checkbox {...field} />} />
                }
                label="Sun"
              />
            </Box>
          </Grid>
        </Grid>

        {/* 3. When Should One Get Notified Before Turnaround Time Ends? */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                When Should One Get Notified Before Turnaround Time Ends?
              </InputLabel>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6}>
            <CustomDropdownField
              name="notification_turnaround_time"
              control={control}
              placeholder="15 Minutes"
              options={[{ value: '15 Minutes' }, { value: '30 Minutes' }, { value: '45 Minutes' }]}
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Grid>
        </Grid>

        {/* 4. What are assigned jobs? */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>What are assigned jobs?</InputLabel>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6}>
            <Controller
              name="assigned_jobs_option"
              control={control}
              defaultValue="all_jobs"
              render={({ field }) => (
                <RadioGroup {...field} row>
                  <FormControlLabel value="all_jobs" control={<Radio />} label="All Jobs" />
                  <FormControlLabel value="assigned_jobs" control={<Radio />} label="Assigned Jobs" />
                </RadioGroup>
              )}
            />
          </Grid>
        </Grid>

        {/* 5. Which Job Posting Statuses Should Display the Tag to Job Option? */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                Which Job Posting Statuses Should Display the Tag to Job Option?
              </InputLabel>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6}>
            <CustomDropdownField
              name="tag_to_job_option_statuses"
              control={control}
              placeholder="Select"
              options={[{ value: 'Closed' }, { value: 'Filled' }, { value: 'Hold By Client' }, { value: 'On Hold' }]}
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Grid>
        </Grid>

        {/* 6. Choose the Job Posting statuses for which the "Submit to Job" option will be available: */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                Choose the Job Posting statuses for which the &quot;Submit to Job&quot; option will be available:
              </InputLabel>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6}>
            <CustomDropdownField
              name="submit_to_job_option_statuses"
              control={control}
              placeholder="Select"
              options={[{ value: 'Closed' }, { value: 'Filled' }, { value: 'Hold By Client' }, { value: 'On Hold' }]}
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Grid>
        </Grid>

        {/* 7. Restrict the edit access for the job posting */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                Restrict the edit access for the job posting
              </InputLabel>
              <Typography variant="body2" color="textSecondary">
                (For the selected job posting statuses, users cannot edit the job)
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6}>
            <CustomDropdownField
              name="restrict_edit_access_statuses"
              control={control}
              placeholder="Select"
              options={[{ value: 'Status X' }, { value: 'Status Y' }, { value: 'Status Z' }]}
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Grid>
        </Grid>

        {/* 8. Select roles for whom the Edit access must be restricted when the job status is a restricted status: */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                Select roles for whom the Edit access must be restricted when the job status is a restricted status:
              </InputLabel>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6}>
            <CustomDropdownField
              name="restricted_edit_access_roles"
              control={control}
              placeholder="Select"
              options={[{ value: 'Administrator' }, { value: 'Hiring Manager' }, { value: 'HR Manager' }, { value: 'Technical Recruiter' }]}
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Grid>
        </Grid>

        {/* 9. Change the job status to "Filled" once all the positions are filled? */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                Change the job status to &quot;Filled&quot; once all the positions are filled?
              </InputLabel>
              <Typography variant="body2" color="textSecondary">
                (If enabled, jobs will close automatically in the Job Posting module when all positions are filled)
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
            <FormControlLabel
              control={
                <Controller
                  name="auto_fill_job_status"
                  control={control}
                  defaultValue={false}
                  render={({ field }) => <Switch {...field} />}
                />
              }
              label=""
            />
          </Grid>
        </Grid>

        {/* 10. Employment Test Notification */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>Employment Test Notification</InputLabel>
              <Typography variant="body2" color="textSecondary">
                (Sends a notification to the selected users when an applicant completes an employment test.)
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6}>
            <CustomDropdownField
              name="employment_test_notification"
              control={control}
              placeholder="Select"
              options={[
                { value: 'Initiator' },
                { value: 'Recruitment Manager' },
                { value: 'Sales Manager' },
                { value: 'Primary Recruiter' }
              ]}
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Grid>
        </Grid>

        {/* 11. Default Option for 'Respond By' in Job Posting Details */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                Default Option for &apos;Respond By&apos; in Job Posting Details
              </InputLabel>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6}>
            <Controller
              name="respond_by_option"
              control={control}
              defaultValue="open_until_filled"
              render={({ field }) => (
                <RadioGroup {...field} row>
                  <FormControlLabel value="open_until_filled" control={<Radio />} label="Open Until Filled" />
                  <FormControlLabel value="date_option" control={<Radio />} label="Date Option" />
                </RadioGroup>
              )}
            />
          </Grid>
        </Grid>

        {/* 12. Job Seeker Applied From Career Portal/Job Board Notification */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                Job Seeker Applied From Career Portal/Job Board Notification
              </InputLabel>
              <Typography variant="body2" color="textSecondary">
                (Sends notifications to the selected user(s) when an applicant applies to a job from the Career Portal/Job boards.)
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6}>
            <CustomDropdownField
              name="job_seeker_applied_notification"
              control={control}
              placeholder="Primary Recruiter, Assigned To, Recruit..."
              options={[
                { value: 'Primary Recruiter' },
                { value: 'Assigned To' },
                { value: 'Recruitment Manager' },
                { value: 'Sales Manager' },
                { value: 'Publisher' },
                { value: 'Additional Notifications' },
                { value: 'Nagaraju' },
                { value: 'Prudhvi Kanmuri' },
                { value: 'Radhika G' }
              ]}
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Grid>
        </Grid>

        {/* 13. Mail Template fields configuration when Job Seeker applied From Job Board Notification */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                Mail Template fields configuration when Job Seeker applied From Job Board Notification
              </InputLabel>
              <Typography variant="body2" color="textSecondary">
                (The selected fields will be displayed in the mail when an applicant applies to a job from the job boards.)
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6}>
            <CustomDropdownField
              name="mail_template_fields_configuration"
              control={control}
              placeholder="Select"
              options={[{ value: 'Field 1' }, { value: 'Field 2' }, { value: 'Field 3' }]}
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Grid>
        </Grid>

        {/* 14. Default Ownership for Pipeline Applicant */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                Default Ownership for Pipeline Applicant
              </InputLabel>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6}>
            <CustomDropdownField
              name="default_ownership_pipeline_applicant"
              control={control}
              placeholder="Select"
              options={[
                { value: 'Created By' },
                { value: 'Published By' },
                { value: 'Primary Recruiter' },
                { value: 'Recruitment Manager' },
                { value: 'Sales Manager' },
                { value: 'Nagaraju' },
                { value: 'Prudhvi Kanmuri' },
                { value: 'Radhika G' }
              ]}
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Grid>
        </Grid>

        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                Default Status When &apos;Responded By&apos; is passed
              </InputLabel>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6}>
            <CustomDropdownField
              name="responded_by_default_status"
              control={control}
              placeholder="Select"
              options={[{ value: 'Active' }, { value: 'Close' }, { value: 'Filled' }, { value: 'Hold By Client' }, { value: 'On Hold' }]}
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Grid>
        </Grid>

        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                Restrict Sharing Duplicate Applicants
              </InputLabel>
              <Typography variant="body2" color="textSecondary">
                (If enabled users cannot share the same job to an application multiple times.)
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
            <FormControlLabel
              control={
                <Controller name="restrict_sharing" control={control} defaultValue={false} render={({ field }) => <Switch {...field} />} />
              }
              label=""
            />
          </Grid>
        </Grid>

        {/* 15. Do you want to assign recruiters while approving the Job requisition? */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                Do you want to assign recruiters while approving the Job requisition?
              </InputLabel>
              <Typography variant="body2" color="textSecondary">
                (If it is enabled, the system will show the option to assign recruiters to the job at the time of job requisition final
                approval)
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
            <FormControlLabel
              control={
                <Controller
                  name="assign_recruiters_on_approval"
                  control={control}
                  defaultValue={false}
                  render={({ field }) => <Switch {...field} />}
                />
              }
              label=""
            />
          </Grid>
        </Grid>

        {/* 16. Auto Matching Applicants Notification */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                AI Matching Applicants Notification
              </InputLabel>
              <Typography variant="body2" color="textSecondary">
                (When configured, the system sends the Top 10 Matching Candidates summary to the selected roles.)
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6}>
            <CustomDropdownField
              name="auto_matching_applicants_notification"
              control={control}
              placeholder="Select"
              options={[{ value: 'Role X' }, { value: 'Role Y' }, { value: 'Role Z' }]}
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Grid>
        </Grid>

        {/* 17. Enable skills auto-mapping */}
        <Grid item xs={12} container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
            <Box>
              <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>Enable skills auto-mapping</InputLabel>
              <Typography variant="body2" color="textSecondary">
                (If it is enabled, the system will autofill the applicant&apos;s skill set while submitting the profile for a job)
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
            <FormControlLabel
              control={
                <Controller
                  name="enable_skills_auto_mapping"
                  control={control}
                  defaultValue={false}
                  render={({ field }) => <Switch {...field} />}
                />
              }
              label=""
            />
          </Grid>
        </Grid>
      </Grid>
      <Accordion defaultExpanded sx={{ mt: 2, mb: 2 }}>
        <AccordionSummary expandIcon={<span>&#9660;</span>}>
          <Typography variant="h5">Job Code Settings</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={4} alignItems="center">
            {/* Job Posting Code Generation Type */}
            <Grid item xs={12} container spacing={2} alignItems="center">
              <Grid item xs={12} sm={3} lg={4}>
                <Box>
                  <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                    Job Posting Code Generation Type
                  </InputLabel>
                  <Typography variant="body2" color="textSecondary">
                    (Choose the Job Post code generation type. Automatic code generation will instantly generate the code while the post is
                    created.)
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={9} lg={6}>
                <Controller
                  name="job_posting_code_generation_type"
                  control={control}
                  defaultValue="automatic"
                  render={({ field }) => (
                    <RadioGroup {...field} row>
                      <FormControlLabel value="automatic" control={<Radio />} label="Automatic Code Generation" />
                      <FormControlLabel value="manual" control={<Radio />} label="Manual Code Generation" />
                    </RadioGroup>
                  )}
                />
              </Grid>
            </Grid>

            {/* Prefix */}
            {jobPostingCodeGenerationType === 'automatic' && (
              <Grid item xs={12} container spacing={2} alignItems="center">
                <Grid item xs={12} sm={3} lg={4}>
                  <Box>
                    <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>Prefix</InputLabel>
                    <Typography variant="body2" color="textSecondary">
                      (Job codes will be prefixed with this value.)
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={9} lg={6}>
                  <CustomNameField name="prefix" control={control} defaultValue="JPC -" />
                </Grid>
              </Grid>
            )}

            {/* Number */}
            {jobPostingCodeGenerationType === 'automatic' && (
              <Grid item xs={12} container spacing={2} alignItems="center">
                <Grid item xs={12} sm={3} lg={4}>
                  <Box>
                    <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>Number</InputLabel>
                    <Typography variant="body2" color="textSecondary">
                      (This number will be auto-incremented after the prefix if &apos;Automatic Code Generation&apos; is selected.)
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={9} lg={6}>
                  <CustomNameField name="number" control={control} defaultValue="10" />
                </Grid>
              </Grid>
            )}

            {/* Vendor Job Posting Code Generation Type */}
            <Grid item xs={12} container spacing={2} alignItems="center">
              <Grid item xs={12} sm={3} lg={4}>
                <Box>
                  <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                    Vendor Job Posting Code Generation Type
                  </InputLabel>
                  <Typography variant="body2" color="textSecondary">
                    (Choose the vendor job posting code generation type. Automatic Code Generation will instantly generate the code when the
                    vendor job post is created.)
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={9} lg={6}>
                <Controller
                  name="vendor_job_posting_code_generation_type"
                  control={control}
                  defaultValue="manual"
                  render={({ field }) => (
                    <RadioGroup {...field} row>
                      <FormControlLabel value="automatic" control={<Radio />} label="Automatic Code Generation" />
                      <FormControlLabel value="manual" control={<Radio />} label="Manual Code Generation" />
                    </RadioGroup>
                  )}
                />
              </Grid>
            </Grid>
            {vendorJobPostingCodeGenerationType === 'automatic' && (
              <>
                <Grid item xs={12} container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={3} lg={4}>
                    <Box>
                      <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>Prefix</InputLabel>
                      <Typography variant="body2" color="textSecondary">
                        (Vendor job codes will be prefixed with this value.)
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={9} lg={6}>
                    <CustomNameField name="vendor_prefix" control={control} defaultValue="VJ -" />
                  </Grid>
                </Grid>

                <Grid item xs={12} container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={3} lg={4}>
                    <Box>
                      <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>Number</InputLabel>
                      <Typography variant="body2" color="textSecondary">
                        (This number will be auto-incremented after the prefix if &apos;Automatic Code Generation&apos; is selected.)
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={9} lg={6}>
                    <CustomNameField name="vendor_number" control={control} defaultValue="1" />
                  </Grid>
                </Grid>
              </>
            )}
          </Grid>
        </AccordionDetails>
      </Accordion>

      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<span>&#9660;</span>}>
          <Typography variant="h5">Career Portal Settings</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={4} alignItems="center">
            {/* Apply Now */}
            <Grid item xs={12} container spacing={2} alignItems="center">
              <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                <Box>
                  <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>Apply Now</InputLabel>
                  <Typography variant="body2" color="textSecondary">
                    (When checked, job seekers can see the &apos;Apply Now&apos; link in the Career Portal.)
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
                <Controller name="apply_now" control={control} defaultValue={false} render={({ field }) => <Checkbox {...field} />} />
              </Grid>
            </Grid>

            {/* Easy Apply */}
            <Grid item xs={12} container spacing={2} alignItems="center">
              <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                <Box>
                  <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>Easy Apply</InputLabel>
                  <Typography variant="body2" color="textSecondary">
                    (When checked, job seekers can see the &apos;Easy Apply&apos; link in the Career Portal.)
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
                <Controller name="easy_apply" control={control} defaultValue={false} render={({ field }) => <Checkbox {...field} />} />
              </Grid>
            </Grid>

            {/* Apply with Indeed */}
            <Grid item xs={12} container spacing={2} alignItems="center">
              <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                <Box>
                  <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>Apply with Indeed</InputLabel>
                  <Typography variant="body2" color="textSecondary">
                    (When enabled, job seekers will see the &apos;Apply With Indeed&apos; button in the Career Portal / Job Boards.)
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
                <Box display="flex" flexWrap="wrap" gap={2} justifyContent="flex-end">
                  <FormControlLabel
                    control={
                      <Controller
                        name="apply_with_indeed_career_portal"
                        control={control}
                        defaultValue={false}
                        render={({ field }) => <Checkbox {...field} />}
                      />
                    }
                    label="Career Portal"
                  />
                  <FormControlLabel
                    control={
                      <Controller
                        name="apply_with_indeed_job_boards"
                        control={control}
                        defaultValue={false}
                        render={({ field }) => <Checkbox {...field} />}
                      />
                    }
                    label="Job Boards"
                  />
                </Box>
              </Grid>
            </Grid>

            {/* Apply with Monster */}
            <Grid item xs={12} container spacing={2} alignItems="center">
              <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                <Box>
                  <InputLabel sx={{ whiteWhiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>Apply with Monster</InputLabel>
                  <Typography variant="body2" color="textSecondary">
                    (When enabled, job seekers will see the &apos;Apply With Monster&apos; button in the Career Portal / Job Boards.)
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
                <Box display="flex" flexWrap="wrap" gap={2} justifyContent="flex-end">
                  <FormControlLabel
                    control={
                      <Controller
                        name="apply_with_monster_career_portal"
                        control={control}
                        defaultValue={false}
                        render={({ field }) => <Checkbox {...field} />}
                      />
                    }
                    label="Career Portal"
                  />
                  <FormControlLabel
                    control={
                      <Controller
                        name="apply_with_monster_job_boards"
                        control={control}
                        defaultValue={false}
                        render={({ field }) => <Checkbox {...field} />}
                      />
                    }
                    label="Job Boards"
                  />
                </Box>
              </Grid>
            </Grid>

            {/* Apply with Indeed Notification */}
            <Grid item xs={12} container spacing={2} alignItems="center">
              <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                <Box>
                  <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                    Apply with Indeed Notification
                  </InputLabel>
                  <Typography variant="body2" color="textSecondary">
                    (When enabled, the user will get a notification when job seekers submit a resume via Indeed.)
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
                <FormControlLabel
                  control={
                    <Controller
                      name="apply_with_indeed_notification"
                      control={control}
                      defaultValue={false}
                      render={({ field }) => <Switch {...field} />}
                    />
                  }
                  label=""
                />
              </Grid>
            </Grid>

            {/* Apply with Monster Notification */}
            <Grid item xs={12} container spacing={2} alignItems="center">
              <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                <Box>
                  <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                    Apply with Monster Notification
                  </InputLabel>
                  <Typography variant="body2" color="textSecondary">
                    (When enabled, the user will get a notification when job seekers submit a resume via Monster.)
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
                <FormControlLabel
                  control={
                    <Controller
                      name="apply_with_monster_notification"
                      control={control}
                      defaultValue={false}
                      render={({ field }) => <Switch {...field} />}
                    />
                  }
                  label=""
                />
              </Grid>
            </Grid>

            {/* Apply with Post Job Free Notification */}
            <Grid item xs={12} container spacing={2} alignItems="center">
              <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                <Box>
                  <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                    Apply with Post Job Free Notification
                  </InputLabel>
                  <Typography variant="body2" color="textSecondary">
                    (When enabled, the user will get a notification when job seekers submit a resume via Post Job Free.)
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
                <FormControlLabel
                  control={
                    <Controller
                      name="apply_with_post_job_free_notification"
                      control={control}
                      defaultValue={false}
                      render={({ field }) => <Switch {...field} />}
                    />
                  }
                  label=""
                />
              </Grid>
            </Grid>

            {/* Apply with Careerjet Notification */}
            <Grid item xs={12} container spacing={2} alignItems="center">
              <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                <Box>
                  <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                    Apply with Careerjet Notification
                  </InputLabel>
                  <Typography variant="body2" color="textSecondary">
                    (When enabled, the user will get a notification when job seekers submit a resume via Careerjet.)
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
                <FormControlLabel
                  control={
                    <Controller
                      name="apply_with_careerjet_notification"
                      control={control}
                      defaultValue={false}
                      render={({ field }) => <Switch {...field} />}
                    />
                  }
                  label=""
                />
              </Grid>
            </Grid>

            {/* Apply with ZipRecruiter Notification */}
            <Grid item xs={12} container spacing={2} alignItems="center">
              <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                <Box>
                  <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                    Apply with ZipRecruiter Notification
                  </InputLabel>
                  <Typography variant="body2" color="textSecondary">
                    (When enabled, the user will get a notification when job seekers submit a resume via ZipRecruiter.)
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
                <FormControlLabel
                  control={
                    <Controller
                      name="apply_with_ziprecruiter_notification"
                      control={control}
                      defaultValue={false}
                      render={({ field }) => <Switch {...field} />}
                    />
                  }
                  label=""
                />
              </Grid>
            </Grid>

            {/* Apply with Neuvoo Notification */}
            <Grid item xs={12} container spacing={2} alignItems="center">
              <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                <Box>
                  <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                    Apply with Neuvoo Notification
                  </InputLabel>
                  <Typography variant="body2" color="textSecondary">
                    (When enabled, the user will get a notification when job seekers submit a resume via Neuvoo.)
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
                <FormControlLabel
                  control={
                    <Controller
                      name="apply_with_neuvoo_notification"
                      control={control}
                      defaultValue={false}
                      render={({ field }) => <Switch {...field} />}
                    />
                  }
                  label=""
                />
              </Grid>
            </Grid>

            {/* Apply with Resume Library Notification */}
            <Grid item xs={12} container spacing={2} alignItems="center">
              <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                <Box>
                  <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                    Apply with Resume Library Notification
                  </InputLabel>
                  <Typography variant="body2" color="textSecondary">
                    (When enabled, the user will get a notification when job seekers submit a resume via Resume Library.)
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
                <FormControlLabel
                  control={
                    <Controller
                      name="apply_with_resume_library_notification"
                      control={control}
                      defaultValue={false}
                      render={({ field }) => <Switch {...field} />}
                    />
                  }
                  label=""
                />
              </Grid>
            </Grid>

            {/* Applicants should be displayed under this list when they apply from the Career Portal/Job Boards/Referral Portal/Campus Portal */}
            <Grid item xs={12} container spacing={2} alignItems="center">
              <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                <Box>
                  <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                    Applicants should be displayed under this list when they apply from the Career Portal/Job Boards/Referral Portal/Campus
                    Portal
                  </InputLabel>
                </Box>
              </Grid>
              <Grid item xs={12} sm={9} lg={6}>
                <CustomDropdownField
                  name="applicants_display_list"
                  control={control}
                  placeholder="Select"
                  options={[{ value: 'Submission List' }, { value: 'Pipeline' }]}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>
            </Grid>

            {/* Display EEO Details in the Career Portal */}
            <Grid item xs={12} container spacing={2} alignItems="center">
              <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                <Box>
                  <InputLabel sx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: 'black' }}>
                    Display EEO Details in the Career Portal
                  </InputLabel>
                  <Typography variant="body2" color="textSecondary">
                    (When enabled, the EEO Details section will be displayed on the Career Portal page when job seekers apply for any job.)
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={9} lg={6} style={{ textAlign: 'right' }}>
                <FormControlLabel
                  control={
                    <Controller
                      name="display_eeo_details"
                      control={control}
                      defaultValue={false}
                      render={({ field }) => <Switch {...field} />}
                    />
                  }
                  label=""
                />
              </Grid>
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Save Button */}
      <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
          <Button variant="contained" color="primary" size="small">
            Save
          </Button>
        </Grid>
    </MainCard>
  );
}

export default JobPostingSettings;
