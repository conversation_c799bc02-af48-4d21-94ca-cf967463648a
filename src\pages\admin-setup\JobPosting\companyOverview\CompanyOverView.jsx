import React from 'react';
import { Grid, Switch, FormControlLabel, InputLabel, Button, Stack } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import { useForm } from 'react-hook-form';
import ReactDraft<PERSON>uto from './ReactDraftAuto';

function CompanyOverview() {
  const [checked, setChecked] = React.useState(false);
  const [showOthers, setShowOthers] = React.useState(false);
  const { control, watch } = useForm();
  const [mergeFieldToAdd, setMergeFieldToAdd] = React.useState(null);

  const contentDetails = watch('content_details');
  const availableMergeFields = watch('available_merge_fields');
  const selectField = watch('select_field');

  // Define dynamic options for select_field based on availableMergeFields
  const selectFieldOptions = React.useMemo(() => {
    if (availableMergeFields === 'User') {
      return [
        { value: 'first_name', label: 'First Name' },
        { value: 'last_name', label: 'Last name' },
        { value: 'middle_name', label: 'Middle name' },
        { value: 'display_name', label: 'Display name' },
        { value: 'email', label: 'Email' },
        { value: 'role', label: 'Role' },
        { value: 'contact_number', label: 'Contact number' }
      ];
    } else if (availableMergeFields === 'Job Posting') {
      return [
        { value: 'job_title', label: 'Job Title' },
        { value: 'posted_by', label: 'Posted By ' },
        { value: 'respond_by', label: 'Respond By' },
        { value: 'number_of_positions', label: 'Number of Positions' },
        { value: 'recruitment_manager', label: ' Recruitment Manager' },
        { value: 'industry', label: 'Industry' },
        { value: 'states', label: 'States' },
        { value: 'country', label: 'country' },
        { value: 'address', label: 'address' },
        { value: 'remote_job', label: ' Remote Job' },
        { value: 'job_status', label: 'Job Status' }
      ];
    }
    return [];
  }, [availableMergeFields]);

  React.useEffect(() => {
    setShowOthers(contentDetails === 'others');
  }, [contentDetails]);

  const handleChange = (event) => {
    setChecked(event.target.checked);
  };

  const handleAddMergeField = () => {
    if (availableMergeFields && selectField) {
      const formattedField = `{${availableMergeFields} : ${selectField}}`;
      setMergeFieldToAdd(formattedField);
      // After adding, clear the selected fields if needed, or rely on form reset
    }
  };

  return (
    <MainCard title="Company Overview">
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12}>
          <Grid container spacing={2} alignItems="center" justifyContent="space-between">
            <Grid item xs={12} sm={8}>
              <InputLabel>Do you want to attach the company overview while posting on Job Boards?</InputLabel>
            </Grid>
            <Grid item xs={12} sm={4} style={{ textAlign: 'right' }}>
              <FormControlLabel control={<Switch checked={checked} onChange={handleChange} color="primary" />} label="" />
            </Grid>
          </Grid>

          {checked && (
            <>
              <Grid container spacing={2} alignItems="center" sx={{ mt: 2 }}>
                <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                  <InputLabel>Where do you want to attach the overview?</InputLabel>
                </Grid>
                <Grid item xs={12} sm={9} lg={6}>
                  <CustomDropdownField
                    name="overview_position"
                    control={control}
                    placeholder="Select Position"
                    options={[{ value: 'Above Job Description' }, { value: 'Below Job Description' }]}
                    sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  />
                </Grid>
              </Grid>

              <Grid container spacing={2} alignItems="center" sx={{ mt: 2 }}>
                <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                  <InputLabel>Show content Details Of:</InputLabel>
                  <span>(Select the role whose contact details would be display when posting on Job Boards)</span>
                </Grid>
                <Grid item xs={12} sm={9} lg={6}>
                  <CustomDropdownField
                    name="content_details"
                    control={control}
                    placeholder="Select Details"
                    options={[
                      { value: 'Job Publisher' },
                      { value: 'Primary Recruiter' },
                      { value: 'Recruitment Manager' },
                      { value: 'Sales Manager' },
                      { value: 'others' }
                    ]}
                    sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                  />
                </Grid>
              </Grid>

              {showOthers && (
                <Grid container spacing={2} alignItems="center" sx={{ mt: 2 }}>
                  <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                    <InputLabel>Select Person:</InputLabel>
                  </Grid>
                  <Grid item xs={12} sm={9} lg={6}>
                    <CustomDropdownField
                      name="other_person"
                      control={control}
                      placeholder="Select Person"
                      options={[{ value: 'Nagaraju' }, { value: 'Radhika' }, { value: 'Pruvdhi' }]}
                      sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                    />
                  </Grid>
                </Grid>
              )}
              <Grid container spacing={2} alignItems="center" sx={{ mt: 2 }}>
                <Grid item xs={12} sm={3} lg={4} sx={{ pt: { xs: 2, sm: '0 !important' } }}>
                  <InputLabel>Available Merge Fields</InputLabel>
                </Grid>
                <Grid item xs={12} sm={9} lg={6}>
                  <Grid container spacing={1} alignItems="center">
                    <Grid item xs={6}>
                      <Stack spacing={1}>
                        <InputLabel>Available Merge Fields</InputLabel>
                        <CustomDropdownField
                          name="available_merge_fields"
                          control={control}
                          placeholder="Select Table"
                          options={[
                            { value: 'User', label: 'User' },
                            { value: 'Job Posting', label: 'Job Posting' }
                          ]}
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                        />
                      </Stack>
                    </Grid>
                    <Grid item xs={6}>
                      <Grid container spacing={1} alignItems="center">
                        <Grid item xs={9}>
                          <Stack spacing={1}>
                            <InputLabel>Select Fields</InputLabel>
                            <CustomDropdownField
                              name="select_field"
                              control={control}
                              placeholder="Select Field"
                              options={selectFieldOptions}
                              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                            />
                          </Stack>
                        </Grid>
                        <Grid item xs={3} sx={{ mt: 3.5 }}>
                          <Button variant="contained" color="primary" fullWidth onClick={handleAddMergeField}>
                            +Add
                          </Button>
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
              <Grid item xs={12} sx={{ mt: 2 }}>
                <ReactDraftAuto mergeField={mergeFieldToAdd} />
              </Grid>
            </>
          )}
        </Grid>
      </Grid>
    </MainCard>
  );
}

export default CompanyOverview;
