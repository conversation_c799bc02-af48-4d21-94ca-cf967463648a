import { useState, useEffect } from 'react';

// third-party
import { Editor } from 'react-draft-wysiwyg';
import { ContentState, EditorState, Modifier } from 'draft-js';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
// import { border } from '@mui/system';

// ==============================|| EDITOR - DRAFT ||============================== //

export default function ReactDraftAuto({ mergeField }) {
  const [editorState, setEditorState] = useState(() => {
    const initialContent = '';
    // "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.";
    return EditorState.createWithContent(ContentState.createFromText(initialContent));
  });

  useEffect(() => {
    if (mergeField) {
      const currentContent = editorState.getCurrentContent();
      const currentSelection = editorState.getSelection();

      const newContent = Modifier.insertText(currentContent, currentSelection, mergeField);

      const newEditorState = EditorState.push(editorState, newContent, 'insert-characters');

      setEditorState(newEditorState);
    }
  }, [mergeField]);

  const onEditorStateChange = (editor) => {
    setEditorState(editor);
  };
  return (
    <Editor
      editorState={editorState}
      toolbarClassName="toolbarClassName"
      wrapperClassName="wrapperClassName"
      editorClassName="editorClassName"
      onEditorStateChange={onEditorStateChange}
      wrapperStyle={{
        border: '0.1px solid rgba(101, 86, 86, 0.3)',
        minHeight: '400px', // Example fixed height
        maxHeight: '400px', // Ensures it doesn't grow beyond
      }}
    />
  );
}
