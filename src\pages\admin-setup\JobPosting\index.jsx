import { useState } from 'react';
import { Grid, List, ListItemButton, ListItemText, Box } from '@mui/material';
import MainCard from 'components/MainCard';
import CompanyOverview from './companyOverview/CompanyOverView';
import DisqualificationReason from './DisqualificationReason/DisqualificationReason';
import InterViewSetting from './InterViewSetting/InterviewSetting';
import JobPostingLookups from './JobPostingLookups/JobPostingLookups';
import JobPostingSettings from './JobPostingSettings/JobPostingSettings';

// ==============================|| SAMPLE PAGE ||============================== //

export default function AdminSetupJobPosting() {
  const [selectedTab, setSelectedTab] = useState(0);

  const handleListItemClick = (index) => {
    setSelectedTab(index);
  };

  const renderTabContent = () => {
    switch (selectedTab) {
      case 0:
        return <CompanyOverview />;
      case 1:
        return <DisqualificationReason />;
      case 2:
        return <InterViewSetting />;
      case 3:
        return <JobPostingLookups />;
      case 4:
        return <JobPostingSettings />;
      default:
        return <CompanyOverview />;
    }
  };

  const menuItems = [
    'Company Overview',
    'Disqualification Reasons',
    'Interview Settings',
    'Job Posting Lookups',
    'Job Posting Settings',
    'Job Posting Statuses',
    'Job Posting Summary',
    'Keyword Masking',
    'Priorities',
  ];

  return (
    <Grid container spacing={3}>
      {/* Sidebar */}
      <Grid item xs={12} sm={4} md={3} lg={3} xl={3} sx={{ position: 'sticky', top: 60, alignSelf: 'flex-start', zIndex: 80 }}>
        <MainCard sx={{ borderRadius: '2%' }}>
          <List component="nav">
            {menuItems.map((text, index) => (
              <ListItemButton key={text} selected={selectedTab === index} onClick={() => handleListItemClick(index)}>
                <ListItemText primary={text} />
              </ListItemButton>
            ))}
          </List>
        </MainCard>
      </Grid>

      {/* Main Content */}
      <Grid item xs={12} sm={12} md={9}>
        <Box sx={{ mt: 0 }}>{renderTabContent()}</Box>
      </Grid>
    </Grid>
  );
}