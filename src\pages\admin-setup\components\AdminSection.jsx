import React from 'react';
import PropTypes from 'prop-types';
import { Box, Typography } from '@mui/material';

export default function AdminSection({ title, configured, children }) {
  return (
    <Box sx={{ mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5">{title}</Typography>
        {configured && <Typography variant="body2" color="text.secondary">{configured}</Typography>}
      </Box>
      {children}
    </Box>
  );
}

AdminSection.propTypes = {
  title: PropTypes.string.isRequired,
  configured: PropTypes.string,
  children: PropTypes.node,
}; 