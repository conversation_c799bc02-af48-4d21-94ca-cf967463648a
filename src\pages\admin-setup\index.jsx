import { Grid, Box, Typography, useTheme } from '@mui/material';
import MainCard from 'components/MainCard';
import { Link } from 'react-router-dom';

import {
  AddBusiness as AddBusinessIcon,
  Api as ApiIcon,
  CalendarToday as CalendarTodayIcon,
  Engineering as EngineeringIcon,
  EventSeat as EventSeatIcon,
  Group as GroupIcon,
  People as PeopleIcon,
  ReceiptLongOutlined as ReceiptLongOutlinedIcon,
  School as SchoolIcon,
  Security as SecurityIcon,
  Settings as SettingsIcon,
  TextSnippet as TextSnippetIcon,
  TravelExplore as TravelExploreIcon,
  WhatsApp as WhatsAppIcon,
  WhereToVote as WhereToVoteIcon,
  PersonSearch as PersonSearchIcon
} from '@mui/icons-material';

export default function AdminSetup() {
  const theme = useTheme();

  const cards = [
    { icon: <ApiIcon sx={{ fontSize: 24, color: theme.palette.secondary.main }} />, title: 'API Settings', path: '/admin-setup/APISettings' },
    { icon: <CalendarTodayIcon sx={{ fontSize: 24, color: theme.palette.secondary.main }} />, title: 'Reminders', path: '/admin-setup/Reminders' },
    { icon: <PeopleIcon sx={{ fontSize: 24, color: theme.palette.success.main }} />, title: 'Clients', path: '/admin-setup/Clients' },
    { icon: <SchoolIcon sx={{ fontSize: 24, color: theme.palette.warning.main }} />, title: 'Campus', path: '/admin-setup/Campus' },
    { icon: <TextSnippetIcon sx={{ fontSize: 24, color: theme.palette.info.main }} />, title: 'Job Template', path: '/admin-setup/JobTemplate' },
    { icon: <PersonSearchIcon sx={{ fontSize: 24, color: theme.palette.primary.main }} />, title: 'Job Posting', path: '/admin-setup/JobPosting' },
    { icon: <ReceiptLongOutlinedIcon sx={{ fontSize: 24, color: theme.palette.secondary.main }} />, title: 'Job Requisition', path: '/admin-setup/JobRequisition' },
    { icon: <EngineeringIcon sx={{ fontSize: 24, color: theme.palette.success.main }} />, title: 'Applicants', path: '/admin-setup/Applicants' },
    { icon: <WhereToVoteIcon sx={{ fontSize: 24, color: theme.palette.warning.main }} />, title: 'Placement', path: '/admin-setup/Placement' },
    { icon: <EventSeatIcon sx={{ fontSize: 24, color: theme.palette.info.main }} />, title: 'Talent Bench', path: '/admin-setup/TalentBench' },
    { icon: <AddBusinessIcon sx={{ fontSize: 24, color: theme.palette.primary.main }} />, title: 'Vendor', path: '/admin-setup/Vendor' },
    { icon: <TravelExploreIcon sx={{ fontSize: 24, color: theme.palette.secondary.main }} />, title: 'Leads', path: '/admin-setup/Leads' },
    { icon: <SecurityIcon sx={{ fontSize: 24, color: theme.palette.success.main }} />, title: 'Security', path: '/admin-setup/Security' },
    { icon: <TextSnippetIcon sx={{ fontSize: 24, color: theme.palette.warning.main }} />, title: 'Text To Hire', path: '/admin-setup/TextToHire' },
    { icon: <SettingsIcon sx={{ fontSize: 24, color: theme.palette.info.main }} />, title: 'Global Settings', path: '/admin-setup/GlobalSettings' },
    { icon: <GroupIcon sx={{ fontSize: 24, color: theme.palette.primary.main }} />, title: 'Organization', path: '/admin-setup/Organization' },
    { icon: <WhatsAppIcon sx={{ fontSize: 24, color: theme.palette.secondary.main }} />, title: 'WhatsApp', path: '/admin-setup/WhatsApp' }
  ];

  return (
    <Box sx={{ p: { xs: 1, sm: 2 } }}>
      <Grid container spacing={2} justifyContent="center">
        {cards.map((card, index) => (
          <Grid
            key={index}
            item
            xs={12}
            sm={6}
            md={4}
            lg={3}
            sx={{
              flexBasis: { lg: '25%' },
              maxWidth: { lg: '25%' }
            }}
          >
            <Link to={card.path} style={{ textDecoration: 'none' }}>
              <MainCard
                sx={{
                  height: 160,
                  p: 1.5,
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  textAlign: 'center',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: theme.shadows[4],
                    bgcolor: theme.palette.action.hover
                  }
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: 1
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: 40,
                      height: 40,
                      borderRadius: '10px',
                      bgcolor: theme.palette.background.paper,
                      boxShadow:
                        theme.palette.mode === 'dark'
                          ? '0 1px 4px rgba(255,255,255,0.05)'
                          : '0 1px 4px rgba(0,0,0,0.1)'
                    }}
                  >
                    {card.icon}
                  </Box>
                  <Typography variant="subtitle2" fontWeight={600} noWrap>
                    {card.title}
                  </Typography>
                </Box>
              </MainCard>
            </Link>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
}
